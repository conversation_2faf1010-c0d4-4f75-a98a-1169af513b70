import Link from "next/link";
import React from "react";

type Props = {
  children: React.ReactNode;
  title: string;
  href?: string;
};

export function FooterLink({ children, title, href }: Props) {
  return (
    <div>
      {href ? (
        <Link
          href={href}
          className="text-lg font-black text-secondary mb-4 block hover:text-primary transition-colors duration-200"
        >
          {title}
        </Link>
      ) : (
        <div className="text-lg font-black text-secondary mb-4">{title}</div>
      )}
      <ul className="flex flex-col gap-1">{children}</ul>
    </div>
  );
}

type FooterLinkItemProps = {
  label: string;
  href: string;
};

export function FooterLinkItem({ label, href }: FooterLinkItemProps) {
  return (
    <li className="text-sm lg:text-base text-secondary font-medium duration-200 hover:text-primary">
      <Link href={href} title={label} className="block truncate py-2">
        {label}
      </Link>
    </li>
  );
}
