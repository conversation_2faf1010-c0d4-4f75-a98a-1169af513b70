import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
export { auth } from "./auth";

export function middleware(request: NextRequest) {
  // Start with Next's default response handling
  const response = NextResponse.next();

  try {
    const accept = request.headers.get("accept") || "";
    const pathname = request.nextUrl?.pathname || request.url || "";
    const looksLikeHtmlRequest =
      accept.includes("text/html") || /^\/dashboard/.test(pathname);
    if (looksLikeHtmlRequest) {
      response.headers.set(
        "Cache-Control",
        "no-cache, max-age=600, must-revalidate"
      );
    }
  } catch (_e) {
    // noop
  }

  return response;
}

export const config = {
  matcher: ["/dashboard/:path*"],
};
