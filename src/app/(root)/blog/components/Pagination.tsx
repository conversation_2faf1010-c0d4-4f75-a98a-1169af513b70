"use client";

import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import clsx from "clsx";
import { ArrowRigthIcon } from "@/ui/icons/navigation";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  siblingCount?: number;
  boundaryCount?: number;
  nextPageUrl?: string | null;
  previousPageUrl?: string | null;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  siblingCount = 1,
  boundaryCount = 2,
  nextPageUrl,
  previousPageUrl,
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createPageURL = (page: number) => {
    // Check if we're in the blog section
    if (pathname.includes('/blog')) {
      // Check if we're on a category page
      const pathParts = pathname.split('/');
      if (pathParts.length >= 3 && pathParts[1] === 'blog' && pathParts[2] !== 'page') {
        // This is a category page like /blog/seo-tips
        const categorySlug = pathParts[2];
        if (page === 1) {
          return `/blog/${categorySlug}`;
        } else {
          // For category pagination, use query parameters for now
          const params = new URLSearchParams();
          params.set("page", page.toString());
          return `/blog/${categorySlug}?${params.toString()}`;
        }
      } else {
        // This is the main blog page
        // Page 1 should go to /blog, other pages to /blog/page/[page]
        return page === 1 ? '/blog' : `/blog/page/${page}`;
      }
    }
    
    // Fallback to query parameter approach for other sections
    const params = new URLSearchParams();
    params.set("page", page.toString());
    return `${pathname}?${params.toString()}`;
  };

  const getPaginationItems = () => {
    const pages: (number | "...")[] = [];

    // If total pages is small, show all pages
    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
      return pages;
    }

    // Always show first page
    pages.push(1);

    // Calculate the range around current page
    const leftSibling = Math.max(currentPage - siblingCount, 1);
    const rightSibling = Math.min(currentPage + siblingCount, totalPages);

    // Show ellipsis if there's a gap between first page and left sibling
    const shouldShowLeftEllipsis = leftSibling > 2;

    // Show ellipsis if there's a gap between right sibling and last page
    const shouldShowRightEllipsis = rightSibling < totalPages - 1;

    if (!shouldShowLeftEllipsis && shouldShowRightEllipsis) {
      // No left ellipsis, show pages from 2 to rightSibling
      for (let i = 2; i <= rightSibling; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(totalPages);
    } else if (shouldShowLeftEllipsis && !shouldShowRightEllipsis) {
      // No right ellipsis, show left ellipsis and pages from leftSibling to totalPages-1
      pages.push("...");
      for (let i = leftSibling; i < totalPages; i++) {
        pages.push(i);
      }
      pages.push(totalPages);
    } else if (shouldShowLeftEllipsis && shouldShowRightEllipsis) {
      // Both ellipses, show left ellipsis, current range, right ellipsis
      pages.push("...");
      for (let i = leftSibling; i <= rightSibling; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(totalPages);
    } else {
      // No ellipses needed, show all pages from 2 to totalPages-1
      for (let i = 2; i < totalPages; i++) {
        pages.push(i);
      }
      pages.push(totalPages);
    }

    // Remove duplicates while preserving order
    const uniquePages: (number | "...")[] = [];
    const seen = new Set<number | "...">();

    for (const page of pages) {
      if (!seen.has(page)) {
        seen.add(page);
        uniquePages.push(page);
      }
    }

    return uniquePages;
  };

  const paginationItems = getPaginationItems();

  return (
    <div className="flex items-center justify-between w-full mt-10 rounded-lg bg-[#914AC41A] p-4">
      {/* Previous Button */}
      <Link
        href={createPageURL(currentPage - 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === 1 || !previousPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        <ArrowRigthIcon className="rotate-180" />
        Previous
      </Link>

      {/* Full Pagination (Desktop) */}
      <div className="hidden sm:flex items-center gap-2">
        {paginationItems.map((item, index) =>
          item === "..." ? (
            <span key={index} className="px-3 py-1 text-gray-500">
              ...
            </span>
          ) : (
            <Link
              key={index}
              href={createPageURL(item)}
              className={clsx(
                "px-3 py-1 rounded text-secondary transition-all",
                item === currentPage
                  ? "bg-primary text-white"
                  : "hover:bg-gray-100"
              )}
            >
              {item}
            </Link>
          )
        )}
      </div>

      {/* Next Button */}
      <Link
        href={createPageURL(currentPage + 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === totalPages || !nextPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        Next
        <ArrowRigthIcon />
      </Link>
    </div>
  );
};

const NoNumberPagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  nextPageUrl,
  previousPageUrl,
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createPageURL = (page: number) => {
    // Check if we're in the blog section
    if (pathname.includes('/blog')) {
      // Check if we're on a category page
      const pathParts = pathname.split('/');
      if (pathParts.length >= 3 && pathParts[1] === 'blog' && pathParts[2] !== 'page') {
        // This is a category page like /blog/seo-tips
        const categorySlug = pathParts[2];
        if (page === 1) {
          return `/blog/${categorySlug}`;
        } else {
          // For category pagination, use query parameters for now
          const params = new URLSearchParams();
          params.set("page", page.toString());
          return `/blog/${categorySlug}?${params.toString()}`;
        }
      } else {
        // This is the main blog page
        // Page 1 should go to /blog, other pages to /blog/page/[page]
        return page === 1 ? '/blog' : `/blog/page/${page}`;
      }
    }
    
    // Fallback to query parameter approach for other sections
    const params = new URLSearchParams();
    params.set("page", page.toString());
    return `${pathname}?${params.toString()}`;
  };

  return (
    <div className="flex items-center justify-between w-full  rounded-lg bg-[#914AC41A] p-4">
      {/* Previous Button */}
      <Link
        href={createPageURL(currentPage - 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === 1 || !previousPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        <ArrowRigthIcon className="rotate-180" />
        Previous
      </Link>

      {/* Next Button */}
      <Link
        href={createPageURL(currentPage + 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === totalPages || !nextPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        Next
        <ArrowRigthIcon />
      </Link>
    </div>
  );
};

export { Pagination, NoNumberPagination };
