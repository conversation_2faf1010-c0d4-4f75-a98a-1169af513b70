interface SharedListProps {
  children: React.ReactNode;
}
const SharedList: React.FC<SharedListProps> = ({ children }) => {
  return (
    <ul className="list-disc list-inside space-y-3 ps-4 lg:ps-6 mb-4">
      {children}
    </ul>
  );
};

const SharedListItem: React.FC<SharedListProps> = ({ children }) => {
  return <li className="text-secondary text-sm lg:text-base leading-relaxed">{children}</li>;
};
export {SharedList, SharedListItem}