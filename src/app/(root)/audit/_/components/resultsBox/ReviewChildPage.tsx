"use client";
import { useState } from "react";
import BoxPrimary from "../BoxPrimary";
import LoadingSlate from "@/components/loading/LoadingSlate";

const pages: string[] = [];

type ReviewChildPageProps = {
  data?: string[] | Record<string, unknown> | "loading" | null; // Accept string array, object, loading state, or null
};

export default function ReviewChildPage({ data }: ReviewChildPageProps = {}) {
  // React hooks must be called at the top level, before any early returns
  const [showMore, setShowMore] = useState(false);

  // Check if data is in loading state
  if (data === "loading") {
    return (
      <BoxPrimary title="Review Child Pages">
        <LoadingSlate
          title="Loading child pages..."
          showHeader={false}
          showCards={false}
          showChart={false}
          showProgress={false}
          height="sm"
        />
      </BoxPrimary>
    );
  }

  // Use the provided data or fall back to the static pages array
  const pagesToShow = Array.isArray(data) ? data : pages;

  // Define how many items to show initially
  const initialItemsCount = 10;

  // Function to render a URL item with consistent styling
  const renderUrlItem = (url: string, index: number) => {
    return (
      <div
        key={index}
        className="flex items-center gap-2.5 p-2 rounded bg-light-gray-6 overflow-hidden"
      >
        <p className="font-semibold text-xs sm:text-sm text-secondary truncate">
          url:
        </p>
        <span className="text-xs sm:text-sm text-secondary/70 truncate">
          {url}
        </span>
      </div>
    );
  };

  return (
    <BoxPrimary title="Review Child Pages">
      <div className="flex flex-col">
        <div className="text-secondary font-semibold mb-2">Page</div>

        {/* Initial items (always visible) */}
        <div className="flex flex-col gap-2 overflow-hidden">
          {pagesToShow
            .slice(0, initialItemsCount)
            .map((page, index) => renderUrlItem(page, index))}
        </div>

        {/* Additional items (shown when "Show More" is clicked) */}
        {pagesToShow.length > initialItemsCount && (
          <div
            className={`transition-all duration-300 ${
              showMore
                ? "opacity-100 mb-4 mt-2"
                : "opacity-0 max-h-0 overflow-hidden"
            }`}
          >
            <div className="flex flex-col gap-2">
              {pagesToShow
                .slice(initialItemsCount)
                .map((page, index) =>
                  renderUrlItem(page, index + initialItemsCount)
                )}
            </div>
          </div>
        )}

        {/* Show More/Less button */}
        {pagesToShow.length > initialItemsCount && (
          <button
            onClick={() => setShowMore(!showMore)}
            className="mt-2 w-full btn btn--outline-light"
          >
            Show {showMore ? "Less" : "More"}
          </button>
        )}
      </div>
    </BoxPrimary>
  );
}
