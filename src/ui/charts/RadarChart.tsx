"use client";

import { ApexOptions } from "apexcharts";
import dynamic from "next/dynamic";
import { useEffect, useRef } from "react";
const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

type RadarChartProps = {
  labels: string[];
  data: number[];
  onLabelClick?: (label: string) => void;
};

export default function RadarChart({
  labels,
  data,
  onLabelClick,
}: RadarChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);

  // Ensure we have valid data
  const validData = data && data.length > 0 ? data : [0, 0, 0, 0];
  const validLabels =
    labels && labels.length > 0
      ? labels
      : ["On-Page", "Usability", "Tech ", "Local SEO"];

  // Add click event listeners to labels and markers after chart is rendered
  useEffect(() => {
    if (!onLabelClick || !chartRef.current) return;

    const addInteractiveElements = () => {
      // console.log("Adding interactive elements to radar chart");

      // Try multiple selectors to find the labels - expanded list with more specific selectors
      const labelSelectors = [
        ".apexcharts-xaxis-label",
        ".apexcharts-text",
        ".apexcharts-datalabel-label",
        ".apexcharts-xaxis-texts-g text",
        ".apexcharts-xaxis-texts-g .apexcharts-text",
        "g.apexcharts-xaxis-texts-g text",
        "[class*='apexcharts'][class*='label']",
        "text[class*='apexcharts']",
        ".apexcharts-radar text",
        "svg text", // More general fallback
      ];

      let labelElements: NodeListOf<Element> | null = null;

      for (const selector of labelSelectors) {
        labelElements = chartRef.current?.querySelectorAll(selector);
        if (labelElements && labelElements.length > 0) {
          // console.log(
          //   `Found ${labelElements.length} labels with selector: ${selector}`
          // );
          break;
        }
      }

      if (!labelElements || labelElements.length === 0) {
        // If we can't find labels, try to find all text elements in the chart
        labelElements = chartRef.current?.querySelectorAll("text");
        // console.log(
        //   `Fallback: Found ${labelElements?.length || 0} text elements`
        // );
      }

      if (!labelElements || labelElements.length === 0) {
        console.warn("No text elements found in radar chart");
        return;
      }

      // Handle label elements
      labelElements?.forEach((labelElement) => {
        // Try to find the text content
        const textElement = labelElement.querySelector("tspan") || labelElement;
        const textContent = textElement.textContent?.trim();

        // Only add listeners to elements that match our valid labels
        if (textContent && validLabels.includes(textContent)) {
          const htmlElement = textElement as HTMLElement;

          // Set cursor and hover styles with enhanced visibility
          htmlElement.style.cursor = "pointer";
          htmlElement.style.transition = "all 0.2s ease";
          htmlElement.style.pointerEvents = "auto";

          // Add a data attribute to identify clickable elements
          htmlElement.setAttribute("data-clickable", "true");
          htmlElement.setAttribute("data-label", textContent);

          const clickHandler = (e: Event) => {
            e.stopPropagation();
            // console.log(`Label clicked: ${textContent}`);
            onLabelClick(textContent);
          };

          const mouseEnterHandler = () => {
            htmlElement.style.fill = "var(--color-primary)";
            htmlElement.style.opacity = "1";
          };

          const mouseLeaveHandler = () => {
            htmlElement.style.fill = "#666666";
            htmlElement.style.opacity = "1";
          };

          htmlElement.addEventListener("click", clickHandler);
          htmlElement.addEventListener("mouseenter", mouseEnterHandler);
          htmlElement.addEventListener("mouseleave", mouseLeaveHandler);

          // Store the handlers for cleanup
          (htmlElement as any)._clickHandler = clickHandler;
          (htmlElement as any)._mouseEnterHandler = mouseEnterHandler;
          (htmlElement as any)._mouseLeaveHandler = mouseLeaveHandler;
        }
      });

      // Handle data point markers - make them clickable with cursor pointer
      const markerElements =
        chartRef.current?.querySelectorAll(".apexcharts-marker");
      markerElements?.forEach((markerElement, index) => {
        const htmlElement = markerElement as HTMLElement;

        // Set cursor pointer for markers (no hover effects)
        htmlElement.style.cursor = "pointer";

        const clickHandler = (e: Event) => {
          e.stopPropagation();
          const label = validLabels[index];
          if (label) {
            // console.log(`Marker clicked: ${label}`);
            onLabelClick(label);
          }
        };

        htmlElement.addEventListener("click", clickHandler);

        // Store the handler for cleanup
        (htmlElement as any)._clickHandler = clickHandler;
      });

      // Handle radar line segments - make them show cursor pointer but rely on ApexCharts events for clicks
      const lineElements = chartRef.current?.querySelectorAll(
        ".apexcharts-radar-series path"
      );
      lineElements?.forEach((lineElement) => {
        const htmlElement = lineElement as HTMLElement;

        // Set cursor pointer for line segments (no click handler to avoid conflicts)
        htmlElement.style.cursor = "pointer";
      });
    };

    // Try multiple times with increasing delays to catch the chart rendering
    // Added more frequent attempts to ensure elements are found
    const timers = [50, 100, 200, 300, 500, 800, 1000, 1500].map((delay) =>
      setTimeout(addInteractiveElements, delay)
    );

    return () => {
      timers.forEach((timer) => clearTimeout(timer));

      // Clean up event listeners for text elements and markers
      const allInteractiveElements = chartRef.current?.querySelectorAll(
        "text, tspan, .apexcharts-marker"
      );
      allInteractiveElements?.forEach((element) => {
        const clickHandler = (element as any)._clickHandler;
        const mouseEnterHandler = (element as any)._mouseEnterHandler;
        const mouseLeaveHandler = (element as any)._mouseLeaveHandler;

        if (clickHandler) {
          element.removeEventListener("click", clickHandler);
          delete (element as any)._clickHandler;
        }
        // Clean up hover handlers for labels only (markers don't have them)
        if (mouseEnterHandler) {
          element.removeEventListener("mouseenter", mouseEnterHandler);
          delete (element as any)._mouseEnterHandler;
        }
        if (mouseLeaveHandler) {
          element.removeEventListener("mouseleave", mouseLeaveHandler);
          delete (element as any)._mouseLeaveHandler;
        }
      });
    };
  }, [onLabelClick, validLabels]);

  const options: ApexOptions = {
    chart: {
      toolbar: {
        show: false,
      },
      events: onLabelClick
        ? {
            dataPointSelection: (_event, _chartContext, config) => {
              const dataPointIndex = config.dataPointIndex;
              const label = validLabels[dataPointIndex];
              if (label) {
                // console.log(`Data point clicked: ${label}`);
                onLabelClick(label);
              }
            },
            markerClick: (_event, _chartContext, { dataPointIndex }) => {
              const label = validLabels[dataPointIndex];
              if (label) {
                // console.log(`Marker clicked: ${label}`);
                onLabelClick(label);
              }
            },
            mounted: (chartContext, _config) => {
              // This event fires when the chart is fully mounted
              // console.log("Chart mounted, adding label listeners");
              setTimeout(() => {
                const chartElement = chartContext.el;
                const textElements = chartElement.querySelectorAll("text");

                textElements.forEach((textElement: Element) => {
                  const textContent = textElement.textContent?.trim();
                  if (textContent && validLabels.includes(textContent)) {
                    const htmlElement = textElement as HTMLElement;

                    // Set cursor and hover styles with enhanced visibility
                    htmlElement.style.cursor = "pointer";
                    htmlElement.style.transition = "all 0.2s ease";
                    htmlElement.style.pointerEvents = "auto";

                    // Add data attributes for identification
                    htmlElement.setAttribute("data-clickable", "true");
                    htmlElement.setAttribute("data-label", textContent);

                    const clickHandler = (e: Event) => {
                      e.stopPropagation();
                      // console.log(`Text label clicked: ${textContent}`);
                      onLabelClick(textContent);
                    };

                    const mouseEnterHandler = () => {
                      htmlElement.style.fill = "var(--color-primary)";
                      htmlElement.style.opacity = "1";
                    };

                    const mouseLeaveHandler = () => {
                      htmlElement.style.fill = "#666666";
                      htmlElement.style.opacity = "1";
                    };

                    htmlElement.addEventListener("click", clickHandler);
                    htmlElement.addEventListener(
                      "mouseenter",
                      mouseEnterHandler
                    );
                    htmlElement.addEventListener(
                      "mouseleave",
                      mouseLeaveHandler
                    );
                  }
                });
              }, 100);
            },
          }
        : undefined,
    },
    markers: {
      size: 3,
    },
    fill: {
      opacity: 0.5,
      colors: ["rgba(148, 80, 196, 0.5)"],
    },
    stroke: {
      show: true,
      width: 1,
      colors: ["var(--color-primary)"],
      dashArray: 0,
    },
    plotOptions: {
      radar: {
        size: 85,
        offsetY: 10,
        polygons: {
          strokeWidth: "0.7",
          strokeColors: "var(--color-light-gray-2)",
          connectorColors: "var(--color-light-gray-2)",
        },
      },
    },
    tooltip: {
      enabled: true,
      y: {
        formatter: (value) => `Score: ${value}`,
      },
    },

    series: [
      {
        name: "SEO Score",
        data: validData,
      },
    ],
    labels: validLabels,
    xaxis: {
      labels: {
        style: {
          colors: onLabelClick
            ? Array(validLabels.length).fill("#666666")
            : undefined,
          fontSize: "12px",
          fontWeight: "600",
          fontFamily: "Inter, sans-serif",
          cssClass: onLabelClick ? "radar-chart-clickable-label" : undefined,
        },
      },
    },
    yaxis: {
      show: false,
      labels: {
        show: false,
      },
    },
    dataLabels: {
      enabled: false,
    },
    legend: {
      show: false,
    },
  };

  // Handle clicks on the entire chart container
  const handleChartClick = (e: React.MouseEvent) => {
    if (!onLabelClick) return;

    const target = e.target as Element;
    const textContent = target.textContent?.trim();

    // Check if the clicked element or its parent contains label text
    if (textContent && validLabels.includes(textContent)) {
      // console.log(`Container click detected on label: ${textContent}`);
      onLabelClick(textContent);
      return;
    }

    // Check parent elements for label text
    let currentElement = target.parentElement;
    while (currentElement) {
      const parentText = currentElement.textContent?.trim();
      if (parentText && validLabels.includes(parentText)) {
        // console.log(`Container click detected on parent label: ${parentText}`);
        onLabelClick(parentText);
        return;
      }
      currentElement = currentElement.parentElement;
    }
  };

  return (
    <>
      <style jsx global>{`
        .radar-chart-container .apexcharts-xaxis-label {
          font-size: 12px !important;
          font-weight: 600 !important;
          font-family: "Space Grotesk", sans-serif !important;
        }
        .radar-chart-container .apexcharts-text {
          font-size: 12px !important;
          font-weight: 600 !important;
          font-family: "Space Grotesk", sans-serif !important;
        }
        .radar-chart-container .apexcharts-xaxis-texts-g text {
          font-size: 12px !important;
          font-weight: 600 !important;
          font-family: "Space Grotesk", sans-serif !important;
        }
      `}</style>
      <div
        ref={chartRef}
        className="w-full min-h-[250px] mt-2 mb-2 radar-chart-container"
        onClick={handleChartClick}
      >
        <ReactApexChart
          height={260}
          options={options}
          series={[{ name: "SEO Score", data: validData }]}
          type="radar"
        />
      </div>
    </>
  );
}
