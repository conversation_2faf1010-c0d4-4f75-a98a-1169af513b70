/**
 * Auth State Machine
 * 
 * This module provides a state machine for managing authentication flow.
 * It defines the possible states and transitions for the authentication process.
 */

// Define the possible states in the authentication flow
export type AuthState = 
  | 'idle'           // Initial state, no auth modal shown
  | 'checking'       // Checking authentication status
  | 'loginRegister'  // Showing login/register options
  | 'login'          // Showing login form
  | 'register'       // Showing register form
  | 'otp'            // Showing OTP verification form
  | 'success';       // Authentication successful

// Define the possible events that can trigger state transitions
export type AuthEvent =
  | { type: 'OPEN' }
  | { type: 'CLOSE' }
  | { type: 'AUTHENTICATED' }
  | { type: 'UNAUTHENTICATED' }
  | { type: 'LOGIN' }
  | { type: 'REGISTER' }
  | { type: 'SUCCESS' }
  | { type: 'FAILURE' }
  | { type: 'VERIFY_EMAIL', email: string };

// Define the state machine configuration
export const authStateMachine = {
  initial: 'idle' as AuthState,
  states: {
    idle: {
      on: { 
        OPEN: 'checking'
      }
    },
    checking: {
      on: { 
        AUTHENTICATED: 'success',
        UNAUTHENTICATED: 'loginRegister'
      }
    },
    loginRegister: {
      on: {
        LOGIN: 'login',
        REGISTER: 'register',
        CLOSE: 'idle'
      }
    },
    login: {
      on: {
        SUCCESS: 'success',
        REGISTER: 'register',
        FAILURE: 'login',
        CLOSE: 'idle'
      }
    },
    register: {
      on: {
        SUCCESS: 'otp',
        LOGIN: 'login',
        FAILURE: 'register',
        CLOSE: 'idle'
      }
    },
    otp: {
      on: {
        SUCCESS: 'success',
        FAILURE: 'otp',
        CLOSE: 'idle'
      }
    },
    success: {
      on: {
        CLOSE: 'idle'
      }
    }
  }
};

/**
 * Transition function to determine the next state based on the current state and event
 * @param currentState The current state
 * @param event The event that occurred
 * @returns The next state
 */
export function transition(currentState: AuthState, event: AuthEvent): AuthState {
  const stateConfig = authStateMachine.states[currentState];
  
  if (!stateConfig) {
    return authStateMachine.initial;
  }

  const nextState = stateConfig.on[event.type as keyof typeof stateConfig.on];

  if (!nextState) {
    return currentState;
  }
  
  return nextState as AuthState;
}

/**
 * Get the view name for the auth modal based on the current state
 * @param state The current state
 * @returns The view name for the auth modal
 */
export function getAuthModalView(state: AuthState): "login-register" | "login" | "register" | "otp" | "success" {
  switch (state) {
    case 'loginRegister':
      return 'login-register';
    case 'login':
      return 'login';
    case 'register':
      return 'register';
    case 'otp':
      return 'otp';
    case 'success':
      return 'success';
    default:
      return 'login-register';
  }
}

export default {
  authStateMachine,
  transition,
  getAuthModalView
};
