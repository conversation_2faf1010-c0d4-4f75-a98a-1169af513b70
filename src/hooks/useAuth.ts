/**
 * Centralized authentication hook
 * This hook provides a consistent way to access authentication state and methods across the application
 */
import { useEffect, useState, useCallback } from "react";
import { useAuthStore } from "@/store/authStore";
import authService from "@/services/authService";
import { useRouter } from "next/navigation";

interface LoginData {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  confirm_password: string;
  first_name: string;
  last_name: string;
}

interface VerifyEmailData {
  email: string;
  otp: string;
}

interface UseAuthReturn {
  // Authentication state
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any;
  error: string | null;

  // Authentication actions
  login: (data: LoginData) => Promise<boolean>;
  register: (
    data: RegisterData
  ) => Promise<{ success: boolean; email?: string }>;
  verifyEmail: (data: VerifyEmailData) => Promise<boolean>;
  logout: () => void;
  refreshProfile: (force?: boolean) => Promise<boolean>;

  // Modal actions
  openAuthModal: (
    view?: "login-register" | "login" | "register",
    actionType?: "pdf" | "share" | "whiteLabel" | "general",
    callback?: (() => void) | null,
    showSteps?: boolean
  ) => void;
  closeAuthModal: () => void;

  // Utility functions
  requireLogin: (
    callback: () => void,
    actionType?: "pdf" | "share" | "whiteLabel" | "general"
  ) => boolean;
  redirectToLogin: (returnUrl?: string) => void;
}

/**
 * Custom hook for authentication
 * Provides a consistent interface for authentication across the application
 */
export function useAuth(): UseAuthReturn {
  const router = useRouter();

  // Get authentication state and actions from the store
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const isLoading = useAuthStore((state) => state.isLoading);
  const user = useAuthStore((state) => state.user);
  const error = useAuthStore((state) => state.error);

  // Get authentication actions from the store
  const login = useAuthStore((state) => state.login);
  const register = useAuthStore((state) => state.register);
  const verifyEmail = useAuthStore((state) => state.verifyEmail);
  const storeLogout = useAuthStore((state) => state.logout);
  const checkAuth = useAuthStore((state) => state.checkAuth);

  // Get modal actions from the store
  const openAuthModal = useAuthStore((state) => state.openAuthModal);
  const closeAuthModal = useAuthStore((state) => state.closeAuthModal);

  // Get utility functions from the store
  const storeRequireLogin = useAuthStore((state) => state.requireLogin);

  // Enhanced logout function that handles additional cleanup
  const logout = useCallback(() => {
    storeLogout();
    // Additional cleanup if needed
  }, [storeLogout]);

  // Enhanced require login function with action type
  const requireLogin = useCallback(
    (
      callback: () => void,
      actionType: "pdf" | "share" | "whiteLabel" | "general" = "general"
    ) => {
      if (isAuthenticated) {
        callback();
        return true;
      } else {
        openAuthModal("login-register", actionType, callback, false);
        return false;
      }
    },
    [isAuthenticated, openAuthModal]
  );

  // Function to redirect to login page
  const redirectToLogin = useCallback(
    (returnUrl?: string) => {
      // Implement redirect to login page
      // This could be used for pages that require authentication
      const url = returnUrl
        ? `/login?returnUrl=${encodeURIComponent(returnUrl)}`
        : "/login";
      router.push(url);
    },
    [router]
  );

  // Refresh profile data with option to force refresh
  const refreshProfile = useCallback(
    async (force = false) => {
      return await checkAuth(force);
    },
    [checkAuth]
  );

  // We don't need to check auth status here anymore
  // The Providers component handles the auth check centrally

  return {
    // Authentication state
    isAuthenticated,
    isLoading,
    user,
    error,

    // Authentication actions
    login,
    register,
    verifyEmail,
    logout,
    refreshProfile,

    // Modal actions
    openAuthModal,
    closeAuthModal,

    // Utility functions
    requireLogin,
    redirectToLogin,
  };
}

export default useAuth;
