"use client";
import { useState, useEffect } from "react";
import RHTextBox from "@/ui/RHTextBox";
import { useAuthStore } from "@/store/authStore";
import { motion } from "framer-motion";
import PasswordStrengthIndicator from "./PasswordStrengthIndicator";
import { UserIcon, LockIcon } from "@/ui/icons/general";
import { EmailIcon } from "@/ui/icons/socialMedia";
import useFormState from "@/hooks/useFormState";

type RegisterFormProps = {
  onSuccess: (email: string) => void;
  onLoginClick: () => void;
  onFormStateChange?: (shouldPreventClose: () => boolean) => void;
};

export default function RegisterForm({
  onSuccess,
  onLoginClick,
  onFormStateChange,
}: RegisterFormProps) {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({});

  // Form state management
  const [formState, formActions] = useFormState();

  // Get register function and loading/error states from auth store
  const register = useAuthStore((state) => state.register);
  const isLoading = useAuthStore((state) => state.isLoading);
  const error = useAuthStore((state) => state.error);

  // Update form state when any field has content
  useEffect(() => {
    const hasContent = !!(
      firstName ||
      lastName ||
      email ||
      password ||
      confirmPassword ||
      acceptTerms
    );
    formActions.setHasTypedContent(hasContent);
    formActions.setIsDirty(hasContent);

    // Notify parent component about form state changes
    if (onFormStateChange) {
      onFormStateChange(formActions.shouldPreventModalClose);
    }
  }, [
    firstName,
    lastName,
    email,
    password,
    confirmPassword,
    acceptTerms,
    formActions,
    onFormStateChange,
  ]);

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password: string): boolean => {
    // Password must be at least 8 characters and contain:
    // - At least one uppercase letter
    // - At least one lowercase letter
    // - At least one number
    // - At least one special character
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(
      password
    );
    const hasMinLength = password.length >= 8;

    return (
      hasUppercase &&
      hasLowercase &&
      hasNumber &&
      hasSpecialChar &&
      hasMinLength
    );
  };

  const getPasswordStrength = (
    password: string
  ): {
    score: number;
    requirements: {
      minLength: boolean;
      hasUppercase: boolean;
      hasLowercase: boolean;
      hasNumber: boolean;
      hasSpecialChar: boolean;
    };
  } => {
    const requirements = {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /\d/.test(password),
      hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
    };

    const score = Object.values(requirements).filter(Boolean).length;

    return { score, requirements };
  };

  const validateName = (name: string): boolean => {
    return name.length >= 2;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent multiple submissions
    if (isLoading) return;

    // Reset field errors
    setFieldErrors({});

    // Validate form fields
    const errors: Record<string, string[]> = {};

    // Validate first name
    if (!firstName) {
      errors.first_name = ["First name is required"];
    } else if (!validateName(firstName)) {
      errors.first_name = ["First name must be at least 2 characters"];
    }

    // Validate last name
    if (!lastName) {
      errors.last_name = ["Last name is required"];
    } else if (!validateName(lastName)) {
      errors.last_name = ["Last name must be at least 2 characters"];
    }

    // Validate email
    if (!email) {
      errors.email = ["Email is required"];
    } else if (!validateEmail(email)) {
      errors.email = ["Please enter a valid email address"];
    }

    // Validate password
    if (!password) {
      errors.password = ["Password is required"];
    } else if (!validatePassword(password)) {
      const passwordErrors = [];
      const { requirements } = getPasswordStrength(password);

      if (!requirements.minLength) {
        passwordErrors.push("Password must be at least 8 characters long");
      }
      if (!requirements.hasUppercase) {
        passwordErrors.push(
          "Password must contain at least one uppercase letter"
        );
      }
      if (!requirements.hasLowercase) {
        passwordErrors.push(
          "Password must contain at least one lowercase letter"
        );
      }
      if (!requirements.hasNumber) {
        passwordErrors.push("Password must contain at least one number");
      }
      if (!requirements.hasSpecialChar) {
        passwordErrors.push(
          "Password must contain at least one special character"
        );
      }

      errors.password = passwordErrors;
    }

    // Validate confirm password
    if (!confirmPassword) {
      errors.confirm_password = ["Please confirm your password"];
    } else if (password !== confirmPassword) {
      errors.confirm_password = ["Passwords do not match"];
    }

    // Validate terms and conditions acceptance
    if (!acceptTerms) {
      errors.accept_terms = ["You must agree to the Terms & Conditions"];
    }

    // If there are validation errors, show them and stop
    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      return;
    }

    // Format request body according to API requirements
    const requestBody = {
      email,
      password,
      confirm_password: confirmPassword,
      first_name: firstName,
      last_name: lastName,
    };

    // Call the register function from auth store
    const result = await register(requestBody);

    // If registration was successful, call the success callback with the email
    // No need for a delay here since registration redirects to OTP verification
    if (result.success) {
      onSuccess(result.email || email);
    } else {
      // Handle backend field errors from 400 responses
      if (result.fieldErrors) {
        setFieldErrors(result.fieldErrors);
      }
    }
  };

  return (
    <div className="px-2 lg:px-4 pb-2 flex-1">
      <form
        onSubmit={handleSubmit}
        className="h-full flex flex-col"
        autoComplete="off"
        method="post"
        action="javascript:void(0);"
        data-form-type="register"
      >
        <div className="flex flex-col gap-2 mb-1 overflow-y-auto">
          {/* CSRF token input removed */}

          {/* First Name and Last Name in the same row */}
          <div className="flex flex-row gap-1 sm:gap-2 w-full">
            <div className="flex flex-col gap-1 flex-1 min-w-0">
              <RHTextBox
                label="First Name"
                placeholder="Enter your first name"
                type="text"
                name="firstName"
                value={firstName}
                onChange={(value) => {
                  if (value !== firstName) {
                    setFirstName(value);
                  }
                }}
                onFocus={() => formActions.setHasActiveInput(true)}
                onBlur={() => formActions.setHasActiveInput(false)}
                required
                autoComplete="given-name"
                className="!py-2 text-xs lg:text-sm mx-1"
                icon={<UserIcon className="w-4 h-4" />}
              />
              {fieldErrors.first_name && fieldErrors.first_name.length > 0 && (
                <div className="text-primary-red text-xs md:text-sm font-bold">
                  {fieldErrors.first_name[0]}
                </div>
              )}
            </div>

            <div className="flex flex-col gap-1 flex-1 min-w-0">
              <RHTextBox
                label="Last Name"
                placeholder="Enter your last name"
                type="text"
                name="lastName"
                value={lastName}
                onChange={(value) => {
                  if (value !== lastName) {
                    setLastName(value);
                  }
                }}
                onFocus={() => formActions.setHasActiveInput(true)}
                onBlur={() => formActions.setHasActiveInput(false)}
                required
                autoComplete="family-name"
                className="!py-2 text-xs lg:text-sm mx-1"
                icon={<UserIcon className="w-4 h-4" />}
              />
              {fieldErrors.last_name && fieldErrors.last_name.length > 0 && (
                <div className="text-primary-red text-xs md:text-sm font-bold">
                  {fieldErrors.last_name[0]}
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col gap-1">
            <RHTextBox
              label="Email Address"
              placeholder="Enter your email address"
              type="email"
              name="email"
              value={email}
              onChange={(value) => {
                if (value !== email) {
                  setEmail(value);
                }
              }}
              onFocus={() => formActions.setHasActiveInput(true)}
              onBlur={() => formActions.setHasActiveInput(false)}
              required
              autoComplete="username email"
              className="!py-2 text-xs lg:text-sm mx-1"
              icon={<EmailIcon className="w-4 h-4" />}
            />
            {fieldErrors.email && fieldErrors.email.length > 0 && (
              <div className="text-primary-red text-xs md:text-sm font-bold">
                {fieldErrors.email[0]}
              </div>
            )}
          </div>

          <div className="flex flex-col gap-1">
            <RHTextBox
              label="Password"
              placeholder="Create a password"
              type="password"
              name="password"
              value={password}
              onChange={(value) => {
                if (value !== password) {
                  setPassword(value);
                }
              }}
              onFocus={() => formActions.setHasActiveInput(true)}
              onBlur={() => formActions.setHasActiveInput(false)}
              required
              autoComplete="new-password"
              className="!py-2 text-xs lg:text-sm mx-1 "
              icon={<LockIcon className="w-4 h-4" />}
            />

            {/* Password Errors */}
            {fieldErrors.password && fieldErrors.password.length > 0 && (
              <div className="flex flex-col gap-1">
                {fieldErrors.password.map((error, index) => (
                  <div
                    key={index}
                    className="text-primary-red text-xs md:text-sm font-bold"
                  >
                    {error}
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="flex flex-col gap-1 mb-1">
            <RHTextBox
              label="Confirm Password"
              placeholder="Confirm your password"
              type="password"
              name="confirmPassword"
              value={confirmPassword}
              onChange={(value) => {
                if (value !== confirmPassword) {
                  setConfirmPassword(value);
                }
              }}
              onFocus={() => formActions.setHasActiveInput(true)}
              onBlur={() => formActions.setHasActiveInput(false)}
              required
              autoComplete="new-password"
              className="!py-2 text-xs lg:text-sm mx-1"
              icon={<LockIcon className="w-4 h-4" />}
            />
            {fieldErrors.confirm_password &&
              fieldErrors.confirm_password.length > 0 && (
                <div className="text-primary-red text-xs md:text-sm">
                  {fieldErrors.confirm_password[0]}
                </div>
              )}
          </div>

          {/* Password Strength Indicator - moved after confirm password */}
          {password && (
            <PasswordStrengthIndicator
              password={password}
              confirmPassword={confirmPassword}
              requirements={getPasswordStrength(password).requirements}
              score={getPasswordStrength(password).score}
            />
          )}

          {/* Terms and Conditions Checkbox */}
          <div className="flex flex-col gap-1 mt-2">
            <div className="flex items-center gap-2 mx-1">
              <input
                id="acceptTerms"
                type="checkbox"
                checked={acceptTerms}
                onChange={(e) => setAcceptTerms(e.target.checked)}
                onFocus={() => formActions.setHasActiveInput(true)}
                onBlur={() => formActions.setHasActiveInput(false)}
                className="accent-primary flex-shrink-0"
                required
              />
              <label
                htmlFor="acceptTerms"
                className="text-secondary text-xs lg:text-sm leading-relaxed"
              >
                I agree to the{" "}
                <a
                  href="/terms-and-conditions"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-black font-bold hover:text-primary transition-colors duration-300"
                >
                  Terms & Conditions
                </a>
              </label>
            </div>
            {fieldErrors.accept_terms &&
              fieldErrors.accept_terms.length > 0 && (
                <div className="text-primary-red text-xs md:text-sm font-bold mx-1">
                  {fieldErrors.accept_terms[0]}
                </div>
              )}
          </div>

          {error && (
            <div className="text-primary-red text-xs md:text-sm font-bold mt-2">
              {error}
            </div>
          )}
        </div>

        <div className="mt-auto flex flex-col items-center">
          <div className="w-full max-w-sm">
            <motion.button
              type="submit"
              className={`btn btn--primary !w-full !py-3 md:!py-4 mt-4 !text-sm md:!text-base font-semibold flex items-center justify-center gap-2 ${
                isLoading ? "opacity-70" : ""
              }`}
              disabled={isLoading}
              whileHover={{
                scale: 1.005,
                boxShadow: "0 4px 12px rgba(145, 74, 196, 0.25)",
                backgroundColor: "rgba(155, 84, 206, 1)",
              }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.15 }}
            >
              {isLoading ? (
                <span className="flex items-center justify-center gap-2">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  <span>Registering</span>
                  <motion.span
                    animate={{ opacity: [0, 1, 0] }}
                    transition={{ repeat: Infinity, duration: 1.5 }}
                  >
                    ...
                  </motion.span>
                </span>
              ) : (
                <>
                  <span>Create New Account</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-4 h-4 md:w-5 md:h-5"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="8.5" cy="7" r="4"></circle>
                    <line x1="20" y1="8" x2="20" y2="14"></line>
                    <line x1="23" y1="11" x2="17" y2="11"></line>
                  </svg>
                </>
              )}
            </motion.button>
          </div>

          <div className="mt-3 md:mt-4 text-center">
            <p className="text-secondary text-xs md:text-sm">
              Already have an account?{" "}
              <button
                type="button"
                onClick={onLoginClick}
                className="text-primary font-semibold hover:underline"
              >
                Login
              </button>
            </p>
          </div>
        </div>
      </form>
    </div>
  );
}
