import Link from "next/link";
import { ReactNode } from "react";
import PriceDisplay from "@/components/ui/PriceDisplay";
import { extractCurrencyInfo } from "@/utils/currencyUtils";

type PlanCardProps = {
  variant: "primary" | "blue" | "green";
  price: number;
  label: string;
  labelForBox: string;
  descForBox: string;
  iconForBox: ReactNode;
  listItems?: ReactNode;
  description: string;
  timeFrame: "Month" | "Year";
  selectedBillingPlan?: string;
  planId?: string; // Updated to string to match new API response
  currency?: string; // Currency code from API (e.g., "aud")
};

export default function PlanCard({
  variant = "primary",
  price,
  label,
  labelForBox,
  descForBox,
  iconForBox,
  listItems,
  description,
  timeFrame,
  selectedBillingPlan = "Month",
  planId,
  currency,
}: PlanCardProps) {
  const renderStyles = (key: string) => {
    switch (key) {
      case "primary":
        return {
          text: "text-primary",
          bg: "!bg-primary",
          bgOpacity: "bg-primary/10",
        };
      case "blue":
        return {
          text: "text-[#7EA2EF]",
          bg: "!bg-[#7EA2EF]",
          bgOpacity: "bg-[#7EA2EF]/10",
        };
      case "green":
        return {
          text: "text-[#4AC452]",
          bg: "!bg-[#4AC452]",
          bgOpacity: "bg-[#4AC452]/10",
        };
      default:
        return {
          text: "text-primary",
          bg: "!bg-primary",
          bgOpacity: "bg-primary/10",
        };
    }
  };

  // Use the price directly from props - the calculation is now done in the parent component
  const displayPrice = price;

  // Determine the time frame text based on the timeFrame prop
  const displayTimeFrame = timeFrame;

  return (
    <div
	      className={`w-full h-full  flex cursor-pointer flex-col bg-white hover:shadow-[0_0_50px_-5px_rgba(145,74,196,0.5)] overflow-hidden rounded-xl duration-500`}
    >
      <div
        className={`w-full ${
          renderStyles(variant).bg
	        } py-2 text-white !text-base md:!text-lg font-extrabold text-center`}
      >
        {label}
      </div>

      <div className="w-full flex-1 flex flex-col justify-between p-6">
        <div>
	          <div className="flex items-end">
	            <PriceDisplay
	              price={displayPrice}
	              size="lg"
	              weight="bold"
	              className={`${renderStyles(variant).text} md:!text-3xl`}
	              currency={extractCurrencyInfo(currency ? { currency } : undefined).symbol}
	              currencyCode={extractCurrencyInfo(currency ? { currency } : undefined).code}
	            />
	            <span className="text-secondary/70 !text-xs md:!text-sm ml-2 leading-none">
	              / {displayTimeFrame}
	            </span>
	          </div>

          <div
            className={`${renderStyles(variant).bgOpacity} my-4 p-4 rounded-lg`}
          >
	            <div className={renderStyles(variant).text}>{iconForBox}</div>
	            <div
	              className={`!text-base md:!text-lg font-extrabold my-1 ${
	                renderStyles(variant).text
	              }`}
	            >
              {labelForBox}
            </div>
	            <p className={`font-semibold text-secondary !text-xs md:!text-sm`}>{descForBox}</p>
          </div>

	          <p className="text-secondary font-medium !text-sm md:!text-base pb-6">{description}</p>
	          <p className="font-black !text-sm md:!text-base mb-3">🛠 What You’ll Get:</p>
          {listItems && (
	            <ul className="text-secondary !text-xs md:!text-sm font-medium pb-6 list-disc list-inside space-y-2">
              {listItems}
            </ul>
          )}
        </div>

        <div className="border-t border-t-light-gray pt-6">
          <Link
            href={`/checkout?plan_id=${planId || ""}`}
            className={`w-full btn btn--primary !border-0 ${
              renderStyles(variant).bg
            } block text-center`}
          >
            Get Started
          </Link>
        </div>
      </div>
    </div>
  );
}
