"use client";

import { useState, useCallback, useEffect, Suspense, useRef } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Image from "next/image";
import { motion } from "framer-motion";
import StripeIcon from "@/ui/icons/payment/StripeIcon";
import { CheckIcon } from "@/ui/icons/general";
import paymentService, {
  PaymentStatusResponse,
} from "@/services/paymentService";
import pricingService, { PricingPlan } from "@/services/pricingService";
import Link from "next/link";
import { useAuthStore } from "@/store/authStore";
import PaymentReceipt from "@/components/payment/PaymentReceipt";
import PaymentError from "@/components/payment/PaymentError";
import PriceDisplay from "@/components/ui/PriceDisplay";
import { extractCurrencyInfo } from "@/utils/currencyUtils";
import ExistingSubscriptionModal from "@/components/modals/ExistingSubscriptionModal";

// Payment method type
type PaymentMethod = "stripe" | null;

// Loading component for Suspense fallback
function CheckoutLoading() {
  const [showTimeout, setShowTimeout] = useState(false);

  // Set a timeout to show an error message after 10 seconds
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setShowTimeout(true);
    }, 10000); // 10 seconds

    return () => clearTimeout(timeoutId);
  }, []);

  return (
    <div className="container mx-auto py-20 px-4 flex flex-col items-center justify-center min-h-[60vh]">
      <div className="max-w-2xl w-full bg-white rounded-xl shadow-lg overflow-hidden p-8">
        <div className="flex flex-col items-center text-center">
          {showTimeout ? (
            <>
              <div className="text-red-500 mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-16 w-16 mx-auto"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h2 className="text-xl md:text-2xl font-bold text-red-600 mb-4 text-center">
                Loading Taking Too Long
              </h2>
              <p className="text-secondary text-center mb-6">
                Loading the checkout page is taking longer than expected. Please
                try refreshing the page.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="btn btn--primary px-6 py-2 mb-4"
              >
                Refresh Page
              </button>
            </>
          ) : (
            <>
              <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-primary mb-8"></div>
              <h2 className="text-xl md:text-2xl font-bold text-secondary mb-4 text-center">
                Loading Checkout
              </h2>
              <p className="text-secondary text-center mb-6">
                Please wait while we prepare your checkout experience...
              </p>
              <div className="w-full bg-gray-100 h-2 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-primary"
                  initial={{ width: "10%" }}
                  animate={{ width: "90%" }}
                  transition={{ duration: 3, ease: "easeInOut" }}
                />
              </div>
              <p className="text-xs text-gray-500 mt-4">
                This may take a few moments. Please do not close this page.
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// Payment success component
function PaymentSuccess({
  paymentStatus,
  transactionId,
}: {
  paymentStatus: PaymentStatusResponse;
  transactionId?: string;
}) {
  return (
    <div className="container mx-auto py-20 px-4 flex flex-col items-center justify-center min-h-[60vh]">
      <PaymentReceipt
        paymentStatus={paymentStatus}
        transactionId={transactionId}
      />
    </div>
  );
}

// Checkout component that uses searchParams
function CheckoutContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const paymentMethod: PaymentMethod = "stripe"; // Only Stripe is supported for now
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [planId, setPlanId] = useState<string | null>(null);
  const [planDetails, setPlanDetails] = useState<PricingPlan | null>(null);
  const [paymentStatus, setPaymentStatus] =
    useState<PaymentStatusResponse | null>(null);
  const [isVerifyingPayment, setIsVerifyingPayment] = useState(false);
  const [showExistingSubscriptionModal, setShowExistingSubscriptionModal] =
    useState(false);

  // Timeout states for loading indicators
  const [dataLoadingTimeout, setDataLoadingTimeout] = useState(false);
  const [verifyingPaymentTimeout, setVerifyingPaymentTimeout] = useState(false);
  const [processingTimeout, setProcessingTimeout] = useState(false);

  // Timeout refs to store and clear timeouts
  const dataLoadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const verifyingPaymentTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get authentication state and actions from auth store
  const { isAuthenticated, openAuthModal, checkAuth, isAuthModalOpen, user } =
    useAuthStore();

  // Track if auth modal was shown to handle redirect on close
  const [authModalShown, setAuthModalShown] = useState(false);

  // Check authentication status on component mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      await checkAuth();

      // If not authenticated and not verifying payment, show login modal
      if (!isAuthenticated && !isVerifyingPayment && !authModalShown) {
        // Mark that we've shown the auth modal
        setAuthModalShown(true);

        // Open auth modal with callback to continue checkout flow after login
        openAuthModal(
          "login-register",
          "general",
          () => {
            // Continue with checkout flow without refreshing the page
            // Re-check auth to ensure we have the latest auth state
            checkAuth().then(() => {
              // Auth status refreshed after authentication
            });
          },
          false
        );
      }
    };

    checkAuthStatus();
  }, [
    checkAuth,
    isAuthenticated,
    isVerifyingPayment,
    openAuthModal,
    setAuthModalShown,
    authModalShown,
  ]);

  // Monitor auth modal state to handle redirects when closed
  useEffect(() => {
    // If the modal was shown but is now closed, and user is still not authenticated
    // and they didn't complete the login process (closed the modal)
    if (authModalShown && !isAuthModalOpen && !isAuthenticated) {
      // Redirect back to pricing page only if they explicitly closed the modal
      router.push("/pricing");
    }
  }, [authModalShown, isAuthModalOpen, isAuthenticated, router]);

  // Check for payment_id in URL and verify payment status
  useEffect(() => {
    if (searchParams) {
      const paymentId = searchParams.get("uid");

      if (paymentId) {
        // We have a payment ID, so we need to verify the payment
        setIsVerifyingPayment(true);

        const verifyPayment = async () => {
          try {
            // Check if user is authenticated for payment verification
            if (!isAuthenticated && !authModalShown) {
              // Mark that we've shown the auth modal
              setAuthModalShown(true);

              // Open auth modal with callback to continue payment verification after login
              openAuthModal(
                "login-register",
                "general",
                () => {
                  // Continue with payment verification without refreshing
                  // Re-check auth to ensure we have the latest auth state
                  checkAuth().then(() => {
                    // Only proceed with verification if we're now authenticated
                    if (isAuthenticated) {
                      verifyPayment();
                    }
                  });
                },
                false
              );
              setIsVerifyingPayment(false);
              return;
            }

            // Call the payment verification API
            const status = await paymentService.checkPaymentStatus(paymentId);

            // Check for specific error message in the response
            if (status && status.error === "Payment record not found.") {
              // Create a failed payment status to display the error properly
              const errorStatus: PaymentStatusResponse = {
                payment_status: "failed",
                status: "failed",
                amount: "",
                plan: "",
                plan_period: "",
                profile: null,
                whitelabel_setting: null,
                expire_date: "",
                data: {},
                message:
                  "Payment record not found. The payment ID may be invalid or the payment record may have been deleted. Please contact support with your payment ID.",
              };

              setPaymentStatus(errorStatus);
              setIsVerifyingPayment(false);
              return;
            }

            // Format the payment status data if needed
            if (status) {
              // Ensure status field exists for compatibility with existing code
              if (!status.status) {
                // Check for "succeeded" or "success" in payment_status
                status.status =
                  status.payment_status === "succeeded" ||
                  status.payment_status === "success"
                    ? "success"
                    : "failed";
              }

              // Add plan_details if not present but we have amount and plan info
              if (!status.plan_details && status.amount) {
                status.plan_details = {
                  name: status.plan || "Premium Plan",
                  period: status.plan_period || "Monthly",
                  price: status.amount,
                };
              }
            }

            // Update the payment status
            setPaymentStatus(status);
          } catch (error: any) {
            // Check for specific error message in the response or 404 status code
            const is404Error = error.response?.status === 404;
            const hasPaymentNotFoundError =
              error.response?.data?.error === "Payment record not found." ||
              error.response?.data?.message === "Payment record not found." ||
              error.response?.data?.detail === "Payment record not found." ||
              (is404Error &&
                error.response?.data &&
                (typeof error.response.data === "string"
                  ? error.response.data.includes("Payment record not found")
                  : JSON.stringify(error.response.data).includes(
                      "Payment record not found"
                    )));

            if (hasPaymentNotFoundError) {
              // Create a failed payment status to display the error properly
              const errorStatus: PaymentStatusResponse = {
                payment_status: "failed",
                status: "failed",
                amount: "",
                plan: "",
                plan_period: "",
                profile: null,
                whitelabel_setting: null,
                expire_date: "",
                data: {},
                message:
                  "Payment record not found. The payment ID may be invalid or the payment record may have been deleted. Please contact support with your payment ID.",
              };

              // Set payment status and immediately stop loading state
              setPaymentStatus(errorStatus);
              setIsVerifyingPayment(false);

              // Clear any verification timeout
              if (verifyingPaymentTimeoutRef.current) {
                clearTimeout(verifyingPaymentTimeoutRef.current);
                verifyingPaymentTimeoutRef.current = null;
              }

              return; // Exit early to prevent further processing
            }
            // Check if it's an authentication error
            else if (
              error.response &&
              error.response.status === 401 &&
              !authModalShown
            ) {
              // Mark that we've shown the auth modal
              setAuthModalShown(true);

              // Open auth modal with callback to retry payment verification after login
              openAuthModal(
                "login-register",
                "general",
                () => {
                  // Retry payment verification after successful login without refreshing
                  // Re-check auth to ensure we have the latest auth state
                  checkAuth().then(() => {
                    // Only proceed with verification if we're now authenticated
                    if (isAuthenticated) {
                      verifyPayment();
                    }
                  });
                },
                false
              );
            } else {
              setError(
                "Failed to verify payment status. Please contact support."
              );
            }
          } finally {
            setIsVerifyingPayment(false);
          }
        };

        verifyPayment();
      } else {
        // No payment ID, check for plan_id
        const planIdParam = searchParams.get("plan_id");
        if (planIdParam) {
          // Plan ID is now a string in the new API format
          if (!planIdParam || planIdParam === "") {
            // Invalid plan ID format
            setError("Invalid plan ID. Please select a valid plan.");
            router.push("/pricing");
          } else {
            setPlanId(planIdParam);
          }
        } else {
          // Redirect back to pricing page if no plan ID is provided
          router.push("/pricing");
        }
      }
    }
  }, [
    searchParams,
    router,
    isAuthenticated,
    authModalShown,
    checkAuth,
    openAuthModal,
  ]);

  // Add timeout for data loading
  useEffect(() => {
    // Clear any existing timeout
    if (dataLoadingTimeoutRef.current) {
      clearTimeout(dataLoadingTimeoutRef.current);
      dataLoadingTimeoutRef.current = null;
    }

    // Reset timeout state when loading state changes
    setDataLoadingTimeout(false);

    // Set a new timeout if we're loading
    if (isDataLoading) {
      dataLoadingTimeoutRef.current = setTimeout(() => {
        setDataLoadingTimeout(true);
        setError(
          "Loading is taking longer than expected. Please try refreshing the page."
        );
      }, 10000); // 10 seconds timeout
    }

    // Cleanup function to clear timeout when component unmounts or loading state changes
    return () => {
      if (dataLoadingTimeoutRef.current) {
        clearTimeout(dataLoadingTimeoutRef.current);
        dataLoadingTimeoutRef.current = null;
      }
    };
  }, [isDataLoading]);

  // Add timeout for payment verification
  useEffect(() => {
    // Clear any existing timeout
    if (verifyingPaymentTimeoutRef.current) {
      clearTimeout(verifyingPaymentTimeoutRef.current);
      verifyingPaymentTimeoutRef.current = null;
    }

    // Reset timeout state when loading state changes
    setVerifyingPaymentTimeout(false);

    // Set a new timeout if we're verifying payment and don't have a payment status yet
    // This prevents the timeout from showing if we already have a payment status (like a 404 error)
    if (isVerifyingPayment && !paymentStatus) {
      verifyingPaymentTimeoutRef.current = setTimeout(() => {
        setVerifyingPaymentTimeout(true);
        setError(
          "Payment verification is taking longer than expected. Please contact support."
        );
        setIsVerifyingPayment(false);
      }, 10000); // 10 seconds timeout
    }

    // Cleanup function to clear timeout when component unmounts or loading state changes
    return () => {
      if (verifyingPaymentTimeoutRef.current) {
        clearTimeout(verifyingPaymentTimeoutRef.current);
        verifyingPaymentTimeoutRef.current = null;
      }
    };
  }, [isVerifyingPayment, paymentStatus]);

  // Add timeout for payment processing
  useEffect(() => {
    // Clear any existing timeout
    if (processingTimeoutRef.current) {
      clearTimeout(processingTimeoutRef.current);
      processingTimeoutRef.current = null;
    }

    // Reset timeout state when loading state changes
    setProcessingTimeout(false);

    // Set a new timeout if we're processing payment
    if (isLoading) {
      processingTimeoutRef.current = setTimeout(() => {
        setProcessingTimeout(true);
        setError(
          "Payment processing is taking longer than expected. Please try again or contact support."
        );
        setIsLoading(false);
      }, 10000); // 10 seconds timeout
    }

    // Cleanup function to clear timeout when component unmounts or loading state changes
    return () => {
      if (processingTimeoutRef.current) {
        clearTimeout(processingTimeoutRef.current);
        processingTimeoutRef.current = null;
      }
    };
  }, [isLoading]);

  // Fetch plan details
  useEffect(() => {
    const fetchPlanDetails = async () => {
      if (planId) {
        setIsDataLoading(true);
        try {
          const pricingData = await pricingService.getPricingData();
          // Find the plan with the matching ID (price ID)
          let foundPlan: PricingPlan | null = null;

          // Search through all plan categories
          Object.keys(pricingData).forEach((category) => {
            // Find a plan with a matching id (price ID)
            const matchingPlan = pricingData[category].find(
              (plan) => plan.id === planId
            );

            if (matchingPlan) {
              // Add the plan name from the category key since the API doesn't include it
              foundPlan = {
                ...matchingPlan,
                name: category, // Use the category key as the plan name
              } as PricingPlan;
            }
          });

          if (foundPlan) {
            setPlanDetails(foundPlan);
          } else {
            setError("Selected plan not found. Please try again.");
            // Redirect to pricing page if plan not found
            router.push("/pricing");
          }
        } catch (error) {
          setError("Failed to load plan details. Please try again.");
        } finally {
          setIsDataLoading(false);
        }
      }
    };

    fetchPlanDetails();
  }, [planId, router]);

  // Separate effect to check for existing subscriptions when user data or plan details change
  useEffect(() => {
    const checkExistingSubscription = () => {
      if (
        isAuthenticated &&
        user?.subscriptions &&
        planDetails &&
        planDetails.name
      ) {
        const hasActiveSubscription = user.subscriptions.some(
          (subscription) => {
            const periodEnd = new Date(subscription.current_period_end);
            const isActive = periodEnd > new Date();

            // Ensure both plan names exist before comparison
            if (!subscription.plan_name || !planDetails.name) {
              return false;
            }

            // Use case-insensitive comparison to handle "White label" vs "White Label"
            const isSamePlan =
              subscription.plan_name.toLowerCase() ===
              planDetails.name.toLowerCase();

            return isActive && isSamePlan;
          }
        );

        if (hasActiveSubscription) {
          setShowExistingSubscriptionModal(true);
        }
      }
    };

    checkExistingSubscription();
  }, [isAuthenticated, user, planDetails]);

  // Plan details are fetched from the API

  // Stripe is the only payment method for now

  // Handle form submission
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      // Check if user is authenticated
      if (!isAuthenticated && !authModalShown) {
        // Mark that we've shown the auth modal
        setAuthModalShown(true);

        // Open auth modal with callback to resubmit the form after login
        openAuthModal(
          "login-register",
          "general",
          () => {
            // Re-check auth to ensure we have the latest auth state
            checkAuth().then(() => {
              // This will be called after successful login - continue with form submission
              // without refreshing the page
              const form = document.getElementById(
                "checkout-form"
              ) as HTMLFormElement;
              if (form) form.requestSubmit();
            });
          },
          false
        );
        return;
      }

      if (!paymentMethod) {
        setError("Please select a payment method");
        return;
      }

      if (!planId) {
        setError("No plan selected. Please go back and select a plan.");
        return;
      }

      // Show loading state
      setIsLoading(true);

      try {
        // Use the price ID from the plan details
        const priceId = planDetails?.id || planId;
        // Create payment session with Stripe - send the price_id as plan_id
        const response = await paymentService.createPaymentSession({
          plan_id: priceId,
        });

        // Handle the API response format which includes redirect_url
        if (response.redirect_url) {
          // Redirect to Stripe checkout using the redirect_url
          window.location.href = response.redirect_url;
        } else if (response.checkout_url) {
          // Alternative format with checkout_url
          window.location.href = response.checkout_url;
        } else if (response.success && response.url) {
          // Fallback to the old format for backward compatibility
          window.location.href = response.url;
        } else {
          setError("Failed to create payment session. Please try again.");
          setIsLoading(false);
        }
      } catch (error: any) {
        // Check if it's an authentication error
        if (
          error.response &&
          error.response.status === 401 &&
          !authModalShown
        ) {
          // Mark that we've shown the auth modal
          setAuthModalShown(true);

          // Open auth modal with callback to resubmit the form after login
          openAuthModal(
            "login-register",
            "general",
            () => {
              // Re-check auth to ensure we have the latest auth state
              checkAuth().then(() => {
                // This will be called after successful login - continue with form submission
                // without refreshing the page
                const form = document.getElementById(
                  "checkout-form"
                ) as HTMLFormElement;
                if (form) form.requestSubmit();
              });
            },
            false
          );
        } else {
          setError(
            "An error occurred while processing your payment. Please try again."
          );
        }

        setIsLoading(false);
      }
    },
    [
      paymentMethod,
      planId,
      isAuthenticated,
      openAuthModal,
      setAuthModalShown,
      authModalShown,
      checkAuth,
      planDetails,
    ]
  );

  // Show payment failed screen if payment verification failed
  // This needs to be checked before the loading screen
  if (
    paymentStatus &&
    (paymentStatus.status === "failed" ||
      paymentStatus.payment_status === "failed")
  ) {
    return (
      <div className="container mx-auto py-20 px-4 flex flex-col items-center justify-center min-h-[60vh]">
        <PaymentError message={paymentStatus.message} />
      </div>
    );
  }

  // Show loading screen while verifying payment
  if (isVerifyingPayment) {
    return (
      <div className="container mx-auto py-20 px-4 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="max-w-2xl w-full bg-white rounded-xl shadow-lg overflow-hidden p-8">
          <div className="flex flex-col items-center text-center">
            {verifyingPaymentTimeout ? (
              <>
                <div className="text-red-500 mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-16 w-16 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h2 className="text-xl md:text-2xl font-bold text-red-600 mb-4 text-center">
                  Verification Taking Too Long
                </h2>
                <p className="text-secondary text-center mb-6">
                  Payment verification is taking longer than expected. Please
                  try refreshing the page or contact support if the issue
                  persists.
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="btn btn--primary px-6 py-2 mb-4"
                >
                  Refresh Page
                </button>
              </>
            ) : (
              <>
                <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-primary mb-8"></div>
                <h2 className="text-xl md:text-2xl font-bold text-secondary mb-4 text-center">
                  Verifying Payment
                </h2>
                <p className="text-secondary text-center mb-6">
                  Please wait while we verify your payment...
                </p>
                <div className="w-full bg-gray-100 h-2 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-primary"
                    initial={{ width: "10%" }}
                    animate={{ width: "90%" }}
                    transition={{ duration: 3, ease: "easeInOut" }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-4">
                  This may take a few moments. Please do not close this page.
                </p>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show payment success screen if payment is verified
  if (
    paymentStatus &&
    (paymentStatus.status === "success" ||
      paymentStatus.payment_status === "succeeded" ||
      paymentStatus.payment_status === "success")
  ) {
    // Get payment ID from URL
    const paymentId = searchParams ? searchParams.get("uid") || "" : "";
    return (
      <PaymentSuccess paymentStatus={paymentStatus} transactionId={paymentId} />
    );
  }

  // Show loading screen while fetching plan data
  if (isDataLoading) {
    return (
      <div className="container mx-auto py-20 px-4 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="max-w-2xl w-full bg-white rounded-xl shadow-lg overflow-hidden p-8">
          <div className="flex flex-col items-center text-center">
            {dataLoadingTimeout ? (
              <>
                <div className="text-red-500 mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-16 w-16 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h2 className="text-xl md:text-2xl font-bold text-red-600 mb-4 text-center">
                  Loading Taking Too Long
                </h2>
                <p className="text-secondary text-center mb-6">
                  Loading plan details is taking longer than expected. Please
                  try refreshing the page or contact support if the issue
                  persists.
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="btn btn--primary px-6 py-2 mb-4"
                >
                  Refresh Page
                </button>
                <Link href="/pricing" className="text-primary hover:underline">
                  Return to Pricing
                </Link>
              </>
            ) : (
              <>
                <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-primary mb-8"></div>
                <h2 className="text-xl md:text-2xl font-bold text-secondary mb-4 text-center">
                  Loading Plan Details
                </h2>
                <p className="text-secondary text-center mb-6">
                  Please wait while we prepare your checkout experience...
                </p>
                <div className="w-full bg-gray-100 h-2 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-primary"
                    initial={{ width: "10%" }}
                    animate={{ width: "90%" }}
                    transition={{ duration: 3, ease: "easeInOut" }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-4">
                  This may take a few moments. Please do not close this page.
                </p>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show error screen if no plan details
  if (!planDetails && !isDataLoading) {
    return (
      <div className="container mx-auto py-20 px-4 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="bg-red-50 p-6 rounded-lg mb-8 text-center">
          <h2 className="text-xl md:text-2xl font-bold text-red-600 mb-4">
            Plan Not Found
          </h2>
          <p className="text-secondary mb-6">
            We couldn't find the plan you selected. Please try again.
          </p>
          <button
            onClick={() => router.push("/pricing")}
            className="btn btn--primary px-6 py-2"
          >
            View Available Plans
          </button>
        </div>
      </div>
    );
  }

  // Payment failed screen is now checked earlier in the component

  return (
    <div className="container mx-auto py-8 px-4 md:py-16 mt-10">
      <div className="max-w-6xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="grid grid-cols-1 md:grid-cols-3">
          {/* Left section - Form */}
          <div className="md:col-span-2 p-6 md:p-10">
            <form id="checkout-form" onSubmit={handleSubmit}>
              {/* Payment Method */}
              <div className="mb-8">
                <h2 className="text-lg md:text-xl font-bold text-secondary mb-4">
                  1 - Select a payment method
                </h2>

                <div className="space-y-4">
                  {/* Stripe - Only payment method enabled for now */}
                  <div className="border rounded-lg p-4 border-primary bg-primary/5">
                    <div className="flex items-center">
                      <div className="w-8 h-8 mr-3 flex items-center justify-center">
                        <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                          <CheckIcon className="w-3 h-3 text-white" />
                        </div>
                      </div>
                      <div className="flex items-center">
                        <span className="text-lg font-medium text-secondary mr-2">
                          Stripe
                        </span>
                        <StripeIcon className="w-6 h-6 text-primary" />
                      </div>
                    </div>
                    <p className="text-xs text-secondary mt-2 pl-11">
                      Secure payment processing with credit/debit cards
                    </p>
                  </div>

                  {/* PayPal - Disabled */}
                  <div className="border border-gray-500 rounded-lg p-4 cursor-not-allowed opacity-50">
                    <div className="flex items-center">
                      <div className="w-8 h-8 mr-3 flex items-center justify-center"></div>
                      <div className="flex items-center">
                        <span className="text-lg font-medium text-secondary mr-2">
                          PayPal
                        </span>
                        <Image
                          src="/images/paypal.png"
                          width={19}
                          height={22}
                          alt="PayPal"
                        />
                      </div>
                    </div>
                    <p className="text-xs text-secondary mt-2 pl-11">
                      Coming soon
                    </p>
                  </div>

                  {/* Cryptocurrency - Disabled */}
                  {/* <div className="border border-gray-500 rounded-lg p-4 cursor-not-allowed opacity-50">
                    <div className="flex items-center">
                      <div className="w-8 h-8 mr-3 flex items-center justify-center"></div>
                      <div className="flex items-center">
                        <span className="text-lg font-medium text-secondary mr-2">
                          Cryptocurrency
                        </span>
                        <div className="flex space-x-1">
                          <span className="text-yellow-500 text-xl">₿</span>
                          <span className="text-blue-500 text-xl">Ξ</span>
                          <span className="text-gray-500 text-xl">Ł</span>
                          <span className="text-yellow-400 text-xl">Ð</span>
                        </div>
                      </div>
                    </div>
                    <p className="text-xs text-secondary mt-2 pl-11">
                      Coming soon
                    </p>
                  </div> */}
                </div>
              </div>

              {/* Error message */}
              {error && (
                <div className="mb-4 p-3 bg-red-50 text-red-500 rounded-lg">
                  {error}
                </div>
              )}

              {/* Pay button moved to order summary section */}
            </form>
          </div>

          {/* Right section - Order Summary */}
          <div className="bg-gray-50 p-6 md:p-10">
            <h2 className="text-xl md:text-2xl font-bold text-secondary mb-6">
              Order Summary
            </h2>
            <div className="space-y-4 mb-8">
              {planDetails ? (
                <>
                  <div className="flex justify-between">
                    <span className="text-secondary">
                      {planDetails.interval === "month" ? "Monthly" : "Annual"}{" "}
                      Plan
                    </span>
                    <PriceDisplay
                      price={planDetails.price}
                      size="md"
                      weight="medium"
                      currency={extractCurrencyInfo(planDetails).symbol}
                      currencyCode={extractCurrencyInfo(planDetails).code}
                    />
                  </div>

                  <div className="border-t border-gray-200 pt-4">
                    <h3 className="font-semibold text-secondary mb-2">
                      Plan Features:
                    </h3>
                    <p className="text-sm text-secondary mb-4">
                      {planDetails.description}
                    </p>
                  </div>

                  <div className="border-t border-gray-200 pt-4 flex justify-between">
                    <span className="text-secondary">Subtotal</span>
                    <PriceDisplay
                      price={planDetails.price}
                      size="md"
                      weight="medium"
                      currency={extractCurrencyInfo(planDetails).symbol}
                      currencyCode={extractCurrencyInfo(planDetails).code}
                    />
                  </div>

                  <div className="border-t border-gray-200 pt-4 flex justify-between">
                    <span className="text-secondary font-bold">Total</span>
                    <PriceDisplay
                      price={planDetails.price}
                      size="lg"
                      weight="bold"
                      currency={extractCurrencyInfo(planDetails).symbol}
                      currencyCode={extractCurrencyInfo(planDetails).code}
                    />
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-4 border-b-4 border-primary mb-4"></div>
                  <p className="text-sm text-secondary text-center">
                    Loading plan details...
                  </p>
                </div>
              )}
            </div>

            {/* GST Tax Disclaimer */}
            <div className="mb-4 text-center">
              <p className="!text-[12px] text-secondary font-bold tracking-wide">
                * GST may be added at checkout where applicable by law
              </p>
            </div>

            {/* Pay Button */}
            {processingTimeout ? (
              <div className="mb-4 p-4 bg-red-50 text-red-600 rounded-lg text-center">
                <p className="font-bold mb-2">Processing Taking Too Long</p>
                <p className="text-sm mb-3">
                  Payment processing is taking longer than expected. Please try
                  again or contact support if the issue persists.
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="btn btn--primary px-4 py-2 text-sm"
                >
                  Refresh Page
                </button>
              </div>
            ) : (
              <motion.button
                type="submit"
                form="checkout-form"
                className={`w-full px-8 py-3 text-lg font-bold rounded-lg relative overflow-hidden ${
                  isLoading || !isAuthenticated
                    ? "btn btn--disabled cursor-not-allowed opacity-50"
                    : "btn btn--primary"
                }`}
                whileHover={
                  isLoading || !isAuthenticated ? {} : { scale: 1.02 }
                }
                whileTap={isLoading || !isAuthenticated ? {} : { scale: 0.98 }}
                disabled={isLoading || !isAuthenticated}
                initial={{
                  boxShadow: isAuthenticated
                    ? "0px 4px 0px 0px rgba(115, 59, 156, 0.7)"
                    : "0px 2px 0px 0px rgba(156, 163, 175, 0.5)",
                }}
                animate={
                  isAuthenticated && !isLoading
                    ? {
                        boxShadow: [
                          "0px 4px 0px 0px rgba(115, 59, 156, 0.7)",
                          "0px 6px 0px 0px rgba(115, 59, 156, 0.5)",
                          "0px 4px 0px 0px rgba(115, 59, 156, 0.7)",
                        ],
                        y: [0, -2, 0],
                        transition: {
                          duration: 2.5,
                          repeat: Infinity,
                          repeatType: "loop",
                          ease: "easeInOut",
                        },
                      }
                    : {}
                }
              >
                {/* Subtle background animation - only when authenticated */}
                {isAuthenticated && !isLoading && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
                    animate={{
                      x: ["-100%", "200%"],
                      transition: {
                        duration: 3,
                        repeat: Infinity,
                        ease: "linear",
                      },
                    }}
                  />
                )}

                {isLoading ? (
                  <div className="flex items-center justify-center relative z-10">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Processing...
                  </div>
                ) : !isAuthenticated ? (
                  <span className="relative z-10">
                    Please Login to Continue
                  </span>
                ) : (
                  <span className="relative z-10">Pay Now</span>
                )}
              </motion.button>
            )}

            {/* Secure payment notice */}
            <div className="mt-4 text-center">
              <p className="!text-xs text-secondary font-bold tracking-wide flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                    clipRule="evenodd"
                  />
                </svg>
                Secure payment - Your data is protected
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Existing Subscription Modal */}
      <ExistingSubscriptionModal
        isOpen={showExistingSubscriptionModal}
        onClose={() => setShowExistingSubscriptionModal(false)}
        planName={planDetails?.name || ""}
      />
    </div>
  );
}

// Export default function with Suspense boundary
export default function CheckoutPage() {
  return (
    <Suspense fallback={<CheckoutLoading />}>
      <CheckoutContent />
    </Suspense>
  );
}
