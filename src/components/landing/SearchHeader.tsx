import SearchBox from "./SearchBox";

export default function SearchHeader() {
  return (
    <div id="analyse" className="mt-8 lg:mt-[143px] px-4 lg:px-0 relative z-10">
      <div className="w-full max-w-[657px] mx-auto mb-6">
        <h1 className="text-secondary font-heading text-center text-2xl lg:text-[36px] font-black leading-[44px] font-heading mb-2">
          SEO <span className="text-primary">Audit</span> & Reporting Tool
        </h1>
        <div className="text-secondary text-center text-lg font-semibold lg:text-xl ">
          At{" "}
          <h2 className="inline text-secondary text-lg font-semibold lg:text-xl">
            SEO Analyser
          </h2>
          , We Deliver Comprehensive Website Audits to Boost Visibility and
          Ranking
        </div>
        <p className="text-secondary text-sm lg:text-base text-center leading-[22px] mt-4">
          Boost your site's visibility and attract more qualified traffic. Our
          all-in-one{" "}
          <strong className="font-semibold tracking-wide">SEO Analyser</strong>{" "}
          uncovers hidden issues, delivers actionable insights, and helps you
          rank higher, faster.
        </p>
      </div>
      <SearchBox />
    </div>
  );
}
