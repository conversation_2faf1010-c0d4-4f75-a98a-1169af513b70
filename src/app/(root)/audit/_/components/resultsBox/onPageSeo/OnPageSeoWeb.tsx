"use client";
import React from "react";
import { OnPageAnalysis } from "@/types/seoAnalyzerTypes";
import { OnPageSeoSection } from "../../pdf/OnPageSeoSection";
import BoxPrimary from "../../BoxPrimary";
import ProgressChart from "@/ui/charts/ProgressChart";
import OverallSection from "../OverallSection";

type OnPageSeoWebProps = {
  results: Partial<OnPageAnalysis>;
};

export default function OnPageSeoWeb({ results }: OnPageSeoWebProps) {
  // Extract the total score and grade - show default values if not available yet
  const totalScore = results?.total_score || { score: 0, grade: "F" };

  // Ensure grade is properly typed for ProgressChart
  const grade =
    (totalScore.grade as
      | "A+"
      | "A"
      | "A-"
      | "B+"
      | "B"
      | "B-"
      | "C+"
      | "C"
      | "C-"
      | "D+"
      | "D"
      | "D-"
      | "F") || "F";

  return (
    <BoxPrimary title="On-Page SEO Results">
      <div className="w-full flex flex-col items-center gap-4 lg:gap-0 lg:flex-row lg:items-start">
        <ProgressChart
          value={grade}
          title="On-Page SEO Score"
          size="lg"
          progressStates={[
            { label: "Grade", value: totalScore.score, isNoColor: false },
          ]}
        />

        <OverallSection
          title={results.overall_title || "On-Page SEO Analysis"}
          description={
            results.overall_description ||
            "Analyzing your page's on-page SEO elements..."
          }
        />
      </div>

      {/* Use the new modular PDF component for detailed analysis */}
      <div className="mt-8">
        <OnPageSeoSection
          onPageSeoData={results}
          brand_name="SEO Analyzer"
          brand_website="seoanalyser.com.au"
          brand_photo={null}
          onImageLoad={() => {}}
          onImageError={() => {}}
        />
      </div>
    </BoxPrimary>
  );
}
