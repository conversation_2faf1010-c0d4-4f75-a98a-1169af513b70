"use client";

import { ReactNode, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import useAuth from '@/hooks/useAuth';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
}

/**
 * ProtectedRoute component
 * Wraps a component that requires authentication
 * If the user is not authenticated, it will show a fallback component or redirect to a specified route
 */
export default function ProtectedRoute({
  children,
  fallback,
  redirectTo = '/',
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, refreshProfile, openAuthModal } = useAuth();
  const router = useRouter();
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);
  
  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      if (!isAuthenticated) {
        await refreshProfile();
      }
      setHasCheckedAuth(true);
    };
    
    checkAuth();
  }, [isAuthenticated, refreshProfile]);
  
  useEffect(() => {
    // If authentication check is complete and user is not authenticated
    if (hasCheckedAuth && !isLoading && !isAuthenticated) {
      if (redirectTo) {
        // Redirect to specified route
        router.push(redirectTo);
      } else {
        // Show auth modal
        openAuthModal('login-register', 'general', null, false);
      }
    }
  }, [hasCheckedAuth, isLoading, isAuthenticated, redirectTo, router, openAuthModal]);
  
  // Show loading state while checking authentication
  if (isLoading || !hasCheckedAuth) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  // If authenticated, show the protected content
  if (isAuthenticated) {
    return <>{children}</>;
  }
  
  // If not authenticated and a fallback is provided, show the fallback
  if (fallback) {
    return <>{fallback}</>;
  }
  
  // Otherwise, render nothing (redirect or auth modal will handle it)
  return null;
}
