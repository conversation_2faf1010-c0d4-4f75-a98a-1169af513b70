import { useAuthStore } from "@/store/authStore";

/**
 * Utility function to require login before performing an action
 * 
 * @param callback Function to execute if user is authenticated
 * @param actionType Type of action requiring authentication ('pdf', 'share', or 'whiteLabel')
 * @returns boolean indicating if the user is authenticated
 */
export function requireLogin(
  callback: () => void,
  actionType: 'pdf' | 'share' | 'whiteLabel' = 'pdf'
): boolean {
  const { isAuthenticated, openAuthModal } = useAuthStore.getState();
  
  if (isAuthenticated) {
    // If user is already authenticated, execute the callback immediately
    callback();
    return true;
  } else {
    // If not authenticated, open the auth modal with the callback
    openAuthModal('login-register', actionType, callback);
    return false;
  }
}

/**
 * React hook version of requireLogin
 * 
 * @param actionType Type of action requiring authentication ('pdf', 'share', or 'whiteLabel')
 * @returns Function that takes a callback and requires login before executing it
 */
export function useRequireLogin(
  actionType: 'pdf' | 'share' | 'whiteLabel' = 'pdf'
) {
  const { isAuthenticated, openAuthModal } = useAuthStore();
  
  return (callback: () => void) => {
    if (isAuthenticated) {
      // If user is already authenticated, execute the callback immediately
      callback();
      return true;
    } else {
      // If not authenticated, open the auth modal with the callback
      openAuthModal('login-register', actionType, callback);
      return false;
    }
  };
}
