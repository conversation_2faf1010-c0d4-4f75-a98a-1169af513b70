import type { <PERSON>ada<PERSON>Route } from 'next'
import { getBlogPosts } from '@/services/blogService'

export const revalidate = 3600 // Cache sitemap for 1 hour

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://seoanalyser.com.au'

async function getAllBlogPages() {
  const collectedPosts: { slug: string; url?: string; publish_timestamp: number }[] = []
  let categories: { slug: string }[] = []
  let page = 1
  let pageSize = 0
  let total = 0

  try {
    // Fetch first page to discover count, categories, and page size
    const first = await getBlogPosts({ page }, revalidate)
    categories = first.categories?.map((c) => ({ slug: c.slug })) || []
    pageSize = first.results.length || 0
    total = first.count || 0
    for (const p of first.results) {
      collectedPosts.push({ slug: p.slug, url: p.url, publish_timestamp: p.publish_timestamp })
    }

    // If we know the pageSize, paginate through the rest; otherwise stop at first
    if (pageSize > 0 && total > pageSize) {
      const totalPages = Math.ceil(total / pageSize)
      for (page = 2; page <= totalPages; page++) {
        const data = await getBlogPosts({ page }, revalidate)
        for (const p of data.results) {
          collectedPosts.push({ slug: p.slug, url: p.url, publish_timestamp: p.publish_timestamp })
        }
      }
    }

    return { posts: collectedPosts, categories, total, pageSize }
  } catch (_e) {
    // Fail soft: return what we have (possibly empty) so sitemap still renders
    return { posts: collectedPosts, categories, total, pageSize }
  }
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const entries: MetadataRoute.Sitemap = []

  const now = new Date()
  const push = (path: string, lastModified?: Date | string, changeFrequency?: MetadataRoute.Sitemap[number]['changeFrequency'], priority?: number) => {
    entries.push({
      url: `${SITE_URL}${path}`,
      lastModified: lastModified ?? now,
      changeFrequency,
      priority,
    })
  }

  // Static routes
  push('/', now, 'weekly', 1)
  push('/aboutUs', now, 'monthly', 0.7)
  push('/contactUs', now, 'monthly', 0.7)
  push('/blog', now, 'daily', 0.8)

  // Blog dynamic content
  const { posts, categories, total, pageSize } = await getAllBlogPages()

  // Blog pagination pages
  if (pageSize > 0 && total > pageSize) {
    const totalPages = Math.ceil(total / pageSize)
    for (let i = 2; i <= totalPages; i++) {
      push(`/blog/page/${i}`, now, 'daily', 0.6)
    }
  }

  // Category pages
  for (const cat of categories) {
    push(`/blog/${cat.slug}`, now, 'daily', 0.6)
  }

  // Post pages
  for (const post of posts) {
    const lastMod = new Date(post.publish_timestamp * 1000)
    let categoryFromUrl: string | undefined
    if (post.url) {
      try {
        const u = new URL(post.url, SITE_URL)
        const parts = u.pathname.split('/').filter(Boolean)
        // Expecting: [ 'blog', '{category}', '{slug}' ]
        if (parts[0] === 'blog' && parts.length >= 3) {
          categoryFromUrl = parts[1]
        }
      } catch {}
    }

    const path = categoryFromUrl
      ? `/blog/${categoryFromUrl}/${post.slug}`
      : `/blog/${post.slug}`
    push(path, lastMod, 'weekly', 0.6)
  }

  return entries
}
