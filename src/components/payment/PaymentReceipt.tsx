"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CopyIcon, CheckIcon, ChevronDownIcon } from "@/ui/icons/general";
import {
  PaymentStatusResponse,
  SubscriptionData,
  UserProfileData,
} from "@/services/paymentService";
import Link from "next/link";
import PriceDisplay from "@/components/ui/PriceDisplay";
import { extractCurrencyInfo } from "@/utils/currencyUtils";

interface PaymentReceiptProps {
  paymentStatus: PaymentStatusResponse;
  transactionId?: string;
  returnHome?: boolean;
  currency?: string; // Currency code from API (e.g., "aud")
}

/**
 * A professional payment receipt component that displays transaction details
 * with copy functionality for transaction ID and reference numbers.
 * Enhanced for mobile with improved UX and purple-themed styling.
 */
export default function PaymentReceipt({
  paymentStatus,
  transactionId,
  returnHome = true,
  currency,
}: PaymentReceiptProps) {
  const [copySuccess, setCopySuccess] = useState<string | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(true);
  const [transactionDate, setTransactionDate] = useState<string>("");

  // Helper function to format dates
  const formatDate = useCallback((dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (error) {
      return dateString || new Date().toLocaleString();
    }
  }, []);

  // Helper function to get subscription data from profile
  const getSubscriptionData = useCallback((): SubscriptionData | null => {
    if (
      paymentStatus.profile &&
      paymentStatus.profile.subscriptions &&
      paymentStatus.profile.subscriptions.length > 0
    ) {
      return paymentStatus.profile.subscriptions[0]; // Get the first subscription
    }
    return null;
  }, [paymentStatus.profile]);

  // Helper function to get user profile data
  const getUserProfileData = useCallback((): UserProfileData | null => {
    if (paymentStatus.profile && paymentStatus.profile.email) {
      return paymentStatus.profile as UserProfileData;
    }
    return null;
  }, [paymentStatus.profile]);

  // Format date to a readable format and set transaction date only once on mount
  useEffect(() => {
    // Get transaction date (from payment status or current date)
    const formattedDate = paymentStatus.data?.created_at
      ? formatDate(paymentStatus.data.created_at)
      : formatDate(new Date().toISOString());

    setTransactionDate(formattedDate);
  }, [paymentStatus.data?.created_at, formatDate]);

  // Get transaction ID (from URL parameter only)
  const txnId = transactionId || "";

  // Copy text to clipboard with useCallback to prevent re-renders
  const copyToClipboard = useCallback((text: string, type: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setCopySuccess(type);
        // Reset copy success message after 2 seconds
        setTimeout(() => setCopySuccess(null), 2000);
      })
      .catch((err) => {
        // Copy failed silently
      });
  }, []);

  // Get amount with currency (calculated once on mount)
  const [amount, setAmount] = useState<string>("0");
  const [currencyCode, setCurrencyCode] = useState<string>("usd");

  useEffect(() => {
    const subscriptionData = getSubscriptionData();

    if (subscriptionData) {
      // Use subscription data for amount and currency
      setAmount(subscriptionData.price_amount.toString());
      setCurrencyCode(subscriptionData.price_currency);
    } else {
      // Fallback to existing logic
      const amountStr =
        paymentStatus.plan_details?.price || paymentStatus.amount || "0";
      // Extract numeric value from amount string
      const numericAmount = amountStr.replace(/[^0-9.]/g, "") || "0";
      setAmount(numericAmount);
      setCurrencyCode(currency || "usd");
    }
  }, [
    paymentStatus.plan_details?.price,
    paymentStatus.amount,
    currency,
    getSubscriptionData,
  ]);

  // Toggle details section with useCallback to prevent re-renders
  const toggleDetails = useCallback(() => {
    setDetailsOpen((prev) => !prev);
  }, []);

  return (
    <div className="w-full max-w-3xl mx-auto overflow-hidden px-4 sm:px-0">
      {/* Receipt Header - Success Banner */}
      <motion.div
        initial={{ y: -10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.4 }}
        className="bg-white p-4 sm:p-6 my-3 sm:my-4 border border-gray-200 rounded-xl shadow-[0_4px_20px_rgba(145,74,196,0.08)] text-center"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="w-14 h-14 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-[0_0_15px_rgba(52,199,89,0.2)]"
        >
          <CheckIcon className="w-8 h-8 sm:w-10 sm:h-10 text-green-600" />
        </motion.div>
        <h2 className="text-lg sm:text-xl font-medium text-green-600 mb-3 sm:mb-4">
          Payment Success!
        </h2>
        <div className="mb-1 sm:mb-2">
          <PriceDisplay
            price={amount}
            size="xl"
            weight="bold"
            className="text-xl sm:text-3xl text-secondary"
            currency={extractCurrencyInfo({ currency: currencyCode }).symbol}
            currencyCode={extractCurrencyInfo({ currency: currencyCode }).code}
          />
        </div>
      </motion.div>

      {/* Subscription Details Section - Show if subscription data is available */}
      {getSubscriptionData() && (
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.175 }}
          className="bg-white rounded-xl border border-gray-200 shadow-[0_4px_20px_rgba(145,74,196,0.1)] mt-4 sm:mt-5"
        >
          <div className="w-full flex justify-between items-center p-3 sm:p-5 border-b border-gray-100">
            <span className="font-medium text-secondary text-sm sm:text-lg flex items-center">
              <span className="w-1 h-4 sm:h-5 bg-purple-500 rounded-full mr-2 block"></span>
              Subscription Details
            </span>
          </div>
          <div className="p-3 sm:p-5 space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-secondary text-xs sm:text-sm">Plan</span>
              <span className="text-secondary font-medium text-xs sm:text-sm">
                {getSubscriptionData()?.plan_name}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-secondary text-xs sm:text-sm">
                Billing Interval
              </span>
              <span className="text-secondary font-medium text-xs sm:text-sm">
                {getSubscriptionData()?.price_interval_count}{" "}
                {getSubscriptionData()?.price_interval}
                {getSubscriptionData()?.price_interval_count !== 1 ? "s" : ""}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-secondary text-xs sm:text-sm">
                Current Period
              </span>
              <span className="text-secondary font-medium text-xs sm:text-sm">
                {getSubscriptionData()?.current_period_start &&
                  formatDate(getSubscriptionData()!.current_period_start)}{" "}
                -{" "}
                {getSubscriptionData()?.current_period_end &&
                  formatDate(getSubscriptionData()!.current_period_end)}
              </span>
            </div>
            {getSubscriptionData()?.trial_start &&
              getSubscriptionData()?.trial_end && (
                <div className="flex justify-between items-center">
                  <span className="text-secondary text-xs sm:text-sm">
                    Trial Period
                  </span>
                  <span className="text-secondary font-medium text-xs sm:text-sm">
                    {formatDate(getSubscriptionData()!.trial_start!)} -{" "}
                    {formatDate(getSubscriptionData()!.trial_end!)}
                  </span>
                </div>
              )}
            <div className="flex justify-between items-center border-t border-gray-100 pt-3">
              <span className="text-secondary text-xs sm:text-sm font-medium">
                Amount
              </span>
              <PriceDisplay
                price={getSubscriptionData()?.price_amount.toString() || "0"}
                size="sm"
                weight="bold"
                className="text-primary text-xs sm:text-sm"
                currency={
                  extractCurrencyInfo({
                    currency: getSubscriptionData()?.price_currency || "usd",
                  }).symbol
                }
                currencyCode={
                  extractCurrencyInfo({
                    currency: getSubscriptionData()?.price_currency || "usd",
                  }).code
                }
              />
            </div>
          </div>
        </motion.div>
      )}

      {/* Payment Details Section - Collapsible */}
      <motion.div
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.4, delay: 0.175 }}
        className="bg-white rounded-xl border border-gray-200 shadow-[0_4px_20px_rgba(145,74,196,0.1)] mt-4 sm:mt-5"
      >
        <button
          onClick={toggleDetails}
          className="w-full flex justify-between items-center p-3 sm:p-5 hover:bg-primary/5 transition-colors rounded-t-xl"
          aria-expanded={detailsOpen}
          aria-controls="payment-details-content"
        >
          <span className="font-medium text-secondary text-sm sm:text-lg flex items-center">
            <span className="w-1 h-4 sm:h-5 bg-primary rounded-full mr-2 block"></span>
            Payment Details
          </span>
          <motion.div
            animate={{ rotate: detailsOpen ? 180 : 0 }}
            transition={{ duration: 0.3, type: "spring", stiffness: 200 }}
          >
            <ChevronDownIcon className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
          </motion.div>
        </button>

        <AnimatePresence>
          {detailsOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              id="payment-details-content"
              className="border-t border-gray-100 overflow-hidden"
            >
              {/* Reference Number Row */}
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-3 sm:p-5 hover:bg-gray-50 border-b border-gray-50">
                <div className="text-secondary text-xs sm:text-sm mb-1 sm:mb-0">
                  Ref Number
                </div>
                <div className="flex items-center">
                  <span className="text-secondary font-medium mr-2 text-xs sm:text-sm break-all">
                    {txnId}
                  </span>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => copyToClipboard(txnId, "refNum")}
                    className="p-1 sm:p-1.5 rounded-md bg-primary/10 hover:bg-primary/20 transition-colors relative z-10"
                    title="Copy reference number"
                  >
                    {copySuccess === "refNum" ? (
                      <CheckIcon className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
                    ) : (
                      <CopyIcon className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
                    )}
                    {copySuccess === "refNum" && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0 }}
                        className="absolute -top-6 sm:-top-7 right-0 z-20"
                      >
                        <span className="text-[10px] sm:text-xs text-white bg-primary/90 px-1.5 py-0.5 sm:px-2 sm:py-1 rounded shadow-md whitespace-nowrap">
                          Copied!
                        </span>
                      </motion.div>
                    )}
                  </motion.button>
                </div>
              </div>

              {/* Payment Status Row */}
              <div className="flex justify-between items-center p-3 sm:p-5 hover:bg-gray-50 border-b border-gray-50">
                <div className="text-secondary text-xs sm:text-sm">
                  Payment Status
                </div>
                <div className="flex items-center bg-green-50 px-2 py-0.5 sm:px-3 sm:py-1 rounded-full">
                  <CheckIcon className="w-3 h-3 sm:w-4 sm:h-4 text-green-600 mr-1" />
                  <span className="text-green-600 font-medium text-[10px] sm:text-sm">
                    Success
                  </span>
                </div>
              </div>

              {/* Payment Time Row */}
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-3 sm:p-5 hover:bg-gray-50 border-b border-gray-50">
                <div className="text-secondary text-xs sm:text-sm mb-1 sm:mb-0">
                  Payment Time
                </div>
                <div className="text-secondary font-medium text-xs sm:text-sm">
                  {transactionDate}
                </div>
              </div>

              {/* Total Payment Row */}
              <div className="flex justify-between items-center p-3 sm:p-5 font-medium bg-gray-50 rounded-b-xl">
                <div className="text-secondary text-xs sm:text-sm">
                  Total Payment
                </div>
                <PriceDisplay
                  price={amount}
                  size="md"
                  weight="bold"
                  className="text-primary text-sm sm:text-base"
                  currency={
                    extractCurrencyInfo({ currency: currencyCode }).symbol
                  }
                  currencyCode={
                    extractCurrencyInfo({ currency: currencyCode }).code
                  }
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* User Profile Section - Show if profile data is available */}
      {/* {getUserProfileData() && (
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          className="bg-white rounded-xl border border-gray-200 shadow-[0_4px_20px_rgba(145,74,196,0.1)] mt-4 sm:mt-5"
        >
          <div className="w-full flex justify-between items-center p-3 sm:p-5 border-b border-gray-100">
            <span className="font-medium text-secondary text-sm sm:text-lg flex items-center">
              <span className="w-1 h-4 sm:h-5 bg-blue-500 rounded-full mr-2 block"></span>
              Account Information
            </span>
          </div>
          <div className="p-3 sm:p-5 space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-secondary text-xs sm:text-sm">Email</span>
              <span className="text-secondary font-medium text-xs sm:text-sm">
                {getUserProfileData()?.email}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-secondary text-xs sm:text-sm">Name</span>
              <span className="text-secondary font-medium text-xs sm:text-sm">
                {getUserProfileData()?.first_name}{" "}
                {getUserProfileData()?.last_name}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-secondary text-xs sm:text-sm">
                Account Status
              </span>
              <div className="flex items-center">
                {getUserProfileData()?.is_verified ? (
                  <>
                    <CheckIcon className="w-3 h-3 sm:w-4 sm:h-4 text-green-600 mr-1" />
                    <span className="text-green-600 font-medium text-[10px] sm:text-sm">
                      Verified
                    </span>
                  </>
                ) : (
                  <span className="text-orange-600 font-medium text-[10px] sm:text-sm">
                    Unverified
                  </span>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      )} */}

      {/* Return to Home Button - only shown when returnHome is true */}
      {returnHome && (
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.3 }}
          className="mt-5 sm:mt-6 mb-6 sm:mb-8"
        >
          <motion.div
            whileTap={{ scale: 0.98 }}
            transition={{ duration: 0.2 }}
            className="w-full rounded-lg overflow-hidden"
          >
            <Link
              href="/"
              className="w-full rounded-lg bg-primary text-white font-bold block text-center py-3  sm:py-3.5 text-sm sm:text-base hover:bg-primary/90 transition-all duration-200 shadow-[0_4px_12px_rgba(145,74,196,0.15)]"
            >
              Return to Home
            </Link>
          </motion.div>
        </motion.div>
      )}

      {/* Custom styles for shadows and animations */}
      <style jsx>{`
        @keyframes pulse-purple {
          0% {
            box-shadow: 0 0 0 0 rgba(145, 74, 196, 0.4);
          }
          70% {
            box-shadow: 0 0 0 10px rgba(145, 74, 196, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(145, 74, 196, 0);
          }
        }
      `}</style>
    </div>
  );
}
