{"status": "partial", "result": {"desktop_screenshot_url": "http://seoanalyser.com.au/api/screenshots/screenshot:xdd6144adfe746ea:desktop/", "onpage_analysis": {"title_tag": {"title": "DigitalApex Agency - Web Design | SEO | Advertising", "length": 51, "is_optimal_length": true, "pass": true, "description": "The title tag is displayed in search results and browser tabs, influencing clicks and SEO.", "recommendation": null, "importance": "Title tags are critical for defining page relevance and influencing CTR. Prioritize keywords at the beginning.", "blog": "", "score": 9}, "meta_description": {"content": "From startups to enterprises we help ambitious brands grow faster and increase conversions through expert web development", "length": 121, "is_optimal_length": true, "pass": true, "description": "The meta description summarizes page content in search results, influencing click-through rates.", "recommendation": null, "importance": "Meta descriptions influence CTR. Compelling, keyword-rich descriptions improve visibility and engagement.", "blog": "", "score": 5}, "serp_preview": {"pass": true, "url": "https://digitalapex.com.au", "title": "DigitalApex Agency - Web Design | SEO | Advertising", "caption": "From startups to enterprises we help ambitious brands grow faster and increase conversions through expert web development", "favicon": "https://digitalapex.com.au/wp-content/uploads/2025/04/cropped-DigitalApex_Favicon-32x32.png", "description": "Simulates how your page might appear in search results (SERP). Title and description influence user clicks.", "recommendation": null, "importance": "The SERP preview directly impacts click-through rates. Optimizing title and meta description is key, though search engines may generate their own snippets.", "blog": "", "score": 5}, "language": {"pass": true, "declared": "en-US", "hreflang": {"exists": false, "count": 0, "tags": [], "errors": []}, "description": "Checks for HTML 'lang' attribute to specify content language.", "recommendation": null, "importance": "'lang' attribute aids accessibility and search engine understanding. Hreflang tags (not checked here) are for international SEO.", "blog": "", "score": 3}, "headers": {"pass": true, "h1": {"count": 1, "content": ["From SEO to Sales Funnels   inDigitalApex"], "pass": true, "description": "The H1 tag should define the main topic of the page.", "recommendation": null, "importance": "A single, relevant H1 tag is crucial for signaling the primary page topic to search engines."}, "other_headers": {"h2": {"count": 20, "content": ["Hello There!", "Information", "Connect Us", "100+ Trusted Clients Over Worldwide", "Where Creative Meets Conversion", "From Click to Conversion — We Build What Performs", "Design Smarter, Rank Higher, <PERSON><PERSON>er", "Driving Growth Through Data-Driven Marketing & Digital Solutions", "Our Best Digital Solutions", "Web Design & Development", "Search Engine Optimisation", "Online Advertising", "Data-Driven Marketing Analytics", "Where Creativity Meets Technology: Our Talents for solutions", "How DigitalApex Innovate", "Discovery & Consultation", "Solution Design & Ideation", "Development & Integration", "Optimisation & Growth", "Transform Your Brand with DigitalApex Insights That Drive Growth"]}, "h3": {"count": 8, "content": ["Web Design & Development", "Search Engine Optimisation (SEO)", "Performance-Based Online Advertising", "Data-Driven Marketing", "The Impact of Our Services", "<PERSON>", "<PERSON>", "<PERSON>"]}, "h4": {"count": 0, "content": []}, "h5": {"count": 0, "content": []}, "h6": {"count": 0, "content": []}}, "hierarchy_recommendation": null, "blog": "", "score": 13}, "keyword_consistency": {"pass": false, "keywords": [{"keyword": "digitalapex", "frequency": 14, "in_title": true, "in_meta_description": false, "in_headings": true, "in_footer": false}, {"keyword": "digital", "frequency": 11, "in_title": false, "in_meta_description": false, "in_headings": true, "in_footer": false}, {"keyword": "drive", "frequency": 10, "in_title": false, "in_meta_description": false, "in_headings": true, "in_footer": false}, {"keyword": "design", "frequency": 9, "in_title": true, "in_meta_description": false, "in_headings": true, "in_footer": false}, {"keyword": "team", "frequency": 9, "in_title": false, "in_meta_description": false, "in_headings": false, "in_footer": false}, {"keyword": "performance", "frequency": 9, "in_title": false, "in_meta_description": false, "in_headings": true, "in_footer": false}, {"keyword": "brand", "frequency": 8, "in_title": false, "in_meta_description": true, "in_headings": true, "in_footer": false}, {"keyword": "marketing", "frequency": 8, "in_title": false, "in_meta_description": false, "in_headings": true, "in_footer": false}, {"keyword": "strategy", "frequency": 8, "in_title": false, "in_meta_description": false, "in_headings": false, "in_footer": false}, {"keyword": "seo", "frequency": 7, "in_title": true, "in_meta_description": false, "in_headings": true, "in_footer": false}], "phrases": [{"phrase": "data driven", "frequency": 4, "in_title": false, "in_meta_description": false, "in_headings": false, "in_footer": false}, {"phrase": "view details", "frequency": 4, "in_title": false, "in_meta_description": false, "in_headings": false, "in_footer": false}, {"phrase": "driven marketing", "frequency": 3, "in_title": false, "in_meta_description": false, "in_headings": true, "in_footer": false}, {"phrase": "digital marketing", "frequency": 3, "in_title": false, "in_meta_description": false, "in_headings": false, "in_footer": false}, {"phrase": "design development", "frequency": 3, "in_title": false, "in_meta_description": false, "in_headings": false, "in_footer": false}], "recommendation": {"text": "Top keywords 'team' don't appear in title, meta description, or headings. Include these important terms in key page elements to improve topical relevance.", "priority": "High"}, "description": "Evaluates if prominent keywords from body content are consistently used in title, meta description, and headings.", "importance": "Using main keywords in key HTML elements (title, H1, meta description) reinforces topic relevance to search engines.", "score": 7}, "image_alt_attributes": {"pass": true, "total_images": 41, "images_with_alt": 40, "images_without_alt": 1, "percent_missing": 2, "missing_alt_images_sample": [{"src": "", "element": "<img alt=\"\" src=\"\"/>"}], "description": "Checks if <img> tags have non-empty 'alt' attributes, which describe image content for accessibility and search engines.", "recommendation": null, "importance": "Alt text is crucial for accessibility (screen readers) and helps search engines understand image content. Missing alt text negatively impacts both.", "blog": "", "score": 4}, "canonical_tag": {"pass": true, "canonical_url": "https://digitalapex.com.au/", "description": "The canonical tag specifies the preferred version of a page for search engines, consolidating signals and preventing duplicate content.", "recommendation": null, "importance": "Crucial for preventing duplicate content issues, consolidating link equity, and ensuring the correct page version is indexed and ranked.", "blog": "", "score": 4}, "noindex_tag": {"pass": false, "content": "index, follow, max-snippet:-1, max-video-preview:-1, max-image-preview:large", "description": "Checks for a 'noindex' directive within the robots or googlebot meta tag. 'pass: true' means the page IS NOT indexable according to this tag.", "recommendation": {"text": "Page is indexable via robots meta tag (no 'noindex' directive found).", "priority": "Low"}, "importance": "The 'noindex' directive explicitly tells search engines not to include this page in their index. Incorrect use can remove important pages from search results.", "blog": "", "score": 2}, "ssl_enabled": {"pass": true, "description": "Checks if the website connection uses HTTPS encryption.", "recommendation": null, "importance": "HTTPS is essential for security, user trust, and SEO rankings. Google uses HTTPS as a ranking signal.", "blog": "", "score": 5}, "analytics": {"pass": true, "detected_tools": ["Google Analytics", "Google Tag Manager"], "description": "Checks for common web analytics tracking codes (Google Analytics, GTM, etc.).", "recommendation": null, "importance": "Analytics provide essential data for understanding user behavior, traffic sources, and conversion paths, informing SEO and content strategy.", "blog": "", "score": 4}, "schema_markup": {"pass": true, "formats_found": ["rdfa"], "detected_types_count": {}, "common_types": [], "description": "Checks for Schema.org structured data markup (JSON-LD, Microdata, RDFa), which helps search engines understand content context.", "recommendation": null, "importance": "Schema markup enables rich results in SERPs (ratings, FAQs, etc.), improving visibility and CTR. JSON-LD is Google's preferred format.", "blog": "", "score": 5}, "https_redirect": {"pass": true, "uses_permanent_redirect": true, "status_code": 301, "description": "Checks if the non-secure HTTP version of the URL automatically redirects to the secure HTTPS version using a permanent (301/308) redirect.", "recommendation": null, "importance": "A permanent redirect from HTTP to HTTPS is crucial for security, user trust, consolidating SEO signals, and preventing duplicate content.", "blog": "", "score": 5}, "robots_txt": {"pass": true, "url": "https://digitalapex.com.au/robots.txt", "content": "User-agent: *\nDisallow: /wp-admin/\nAllow: /wp-admin/admin-ajax.php\n\nSitemap: https://digitalapex.com.au/sitemap_index.xml\n", "error": null, "description": "The robots.txt file instructs search engine crawlers which parts of your site should or shouldn't be crawled.", "recommendation": null, "importance": "Controls crawler access, prevents crawling of sensitive areas, and can specify sitemap locations. Misconfiguration can block important content.", "blog": "", "score": 4}, "xml_sitemap": {"pass": true, "sitemap_urls_found": ["https://digitalapex.com.au/sitemap_index.xml", "https://digitalapex.com.au/sitemap.xml"], "sitemap_urls_in_robots": ["https://digitalapex.com.au/sitemap_index.xml"], "error": null, "description": "Checks for XML sitemaps, which list important URLs to help search engine discovery and crawling.", "recommendation": null, "importance": "XML sitemaps help ensure search engines can find and crawl all relevant pages, especially on large or complex sites. They are crucial for efficient indexing.", "blog": "", "score": 5}, "noindex_header": {"pass": false, "content": "", "description": "Checks for a 'noindex' directive in the X-Robots-Tag HTTP header. 'pass: true' means the page IS NOT indexable according to this header.", "recommendation": {"text": "No 'noindex' directive found in X-Robots-Tag header. Page is indexable via header.", "priority": "Low"}, "importance": "The X-Robots-Tag header can prevent indexing, similar to a meta robots tag. It's often used for non-HTML files.", "blog": "", "score": 2}, "content_amount": {"pass": true, "word_count": 512, "text_html_ratio_percent": 3, "description": "Evaluates the amount of visible textual content. Sufficient, high-quality content is crucial for SEO and user engagement.", "recommendation": {"text": "Word count (512) is adequate but could be expanded for competitive topics. Aim for 800-1500+ words for better depth. Low text-to-HTML ratio (3%) suggests potential code bloat. Simplify HTML structure if possible.", "priority": "Medium"}, "importance": "Content quantity signals topical depth to search engines. While quality matters most, very thin content (<300 words) often struggles to rank. Aim for comprehensive coverage relevant to the topic.", "blog": "", "score": 4}, "total_score": {"score": 86, "grade": "B+"}, "overall_title": "Your On-Page SEO is Good", "overall_description": "Your page has good on-page SEO, but there are some areas that could be improved for even better results."}, "usability_analysis": {"device_rendering": {"pass": true, "screenshot_urls": {"desktop": "http://seoanalyser.com.au/api/screenshots/screenshot:xdd6144adfe746ea:desktop/", "tablet": "http://seoanalyser.com.au/api/screenshots/screenshot:xdd6144adfe746ea:tablet/", "mobile": "http://seoanalyser.com.au/api/screenshots/screenshot:xdd6144adfe746ea:mobile/"}, "available_devices": ["desktop", "tablet", "mobile"], "expected_devices": ["desktop", "tablet", "mobile"], "description": "Device rendering checks if your website displays correctly across different devices: desktop, tablet, and mobile. This test captures screenshots of your site on each device type to verify proper rendering.", "importance": "Properly rendered content across all device types is essential for user experience and SEO. Search engines like Google use mobile-first indexing, making mobile rendering particularly important. According to Google's Mobile-First Indexing guidelines, Google predominantly uses the mobile version of content for indexing and ranking. Google's Page Experience guidelines also emphasize responsive design as a core factor.", "recommendation": null, "blog": "", "score": 18}, "viewport_usage": {"pass": true, "has_viewport_meta": true, "viewport_content": "width=device-width, initial-scale=1", "is_responsive": true, "description": "The viewport meta tag instructs browsers on how to control the page's dimensions and scaling. It's essential for responsive web design, ensuring that web pages render well on a variety of devices.", "importance": "Proper configuration of the viewport meta tag is crucial for mobile usability and accessibility. It allows content to adapt to different screen sizes, improving readability and user experience. Google's Mobile-Friendly Test and Mobile Usability guidelines specifically require proper viewport configuration. Additionally, search engines like Google consider mobile-friendliness as a ranking factor in their Page Experience guidelines.", "recommendation": null, "blog": "", "score": 16}, "flash_usage": {"pass": true, "count": 0, "elements": [], "description": "Adobe Flash Player reached its end-of-life on December 31, 2020. As of January 12, 2021, Adobe has blocked Flash content from running in Flash Player, and major browsers have disabled Flash Player from running after the EOL date.", "importance": "Continuing to use Flash content poses significant security risks, as it is no longer supported or updated. Google's guidelines have long discouraged Flash usage, and Chrome stopped supporting Flash in 2021. Modern web standards like HTML5 provide secure and efficient alternatives that align with Google's best practices.", "recommendation": null, "blog": "", "score": 6}, "iframes_usage": {"pass": true, "count": 0, "iframe_sources": [], "description": "The <iframe> element allows embedding of external content into a webpage. However, improper use can lead to security vulnerabilities such as clickjacking and cross-site scripting (XSS).", "importance": "Ensuring that iframes are used securely is crucial to protect users from potential attacks. Proper configuration helps maintain the integrity and security of your website.", "recommendation": null, "blog": "", "score": 6}, "iframe_protection": {"pass": false, "has_x_frame_options": false, "x_frame_options_value": "", "has_csp_frame_protection": false, "description": "The 'Content-Security-Policy' header with the 'frame-ancestors' directive specifies which origins are permitted to embed the page using frames. This is essential for preventing clickjacking attacks.", "importance": "Using 'frame-ancestors' in CSP provides fine-grained control over framing policies and is supported by modern browsers. It supersedes the older 'X-Frame-Options' header. Google's security guidelines emphasize Content Security Policy as a key defense mechanism against clickjacking attacks.", "recommendation": {"text": "Implement the 'Content-Security-Policy' header with the 'frame-ancestors' directive to control which origins can embed your content and prevent clickjacking. For example:\nContent-Security-Policy: frame-ancestors 'none';", "priority": "High"}, "blog": "", "score": 0}, "favicon_presence": {"pass": true, "has_favicon": true, "has_apple_touch_icon": true, "favicon_paths": ["https://digitalapex.com.au/wp-content/uploads/2025/04/cropped-DigitalApex_Favicon-32x32.png", "https://digitalapex.com.au/wp-content/uploads/2025/04/cropped-DigitalApex_Favicon-192x192.png", "https://digitalapex.com.au/wp-content/uploads/2025/04/cropped-DigitalApex_Favicon-180x180.png"], "apple_touch_icon_paths": ["https://digitalapex.com.au/wp-content/uploads/2025/04/cropped-DigitalApex_Favicon-180x180.png"], "description": "Favicons are small icons that represent your website in browser tabs, bookmarks, and search results. They contribute to brand identity and user trust.", "importance": "A well-designed favicon enhances your website's professionalism and can improve visibility in search engine results.", "recommendation": null, "blog": "", "score": 3}, "email_privacy": {"pass": false, "exposed_email_count": 2, "exposed_emails_sample": ["<EMAIL>", "<EMAIL>"], "description": "Detects plain text email addresses within the page's HTML content (text, mailto links, scripts). Exposed emails are easily collected by automated bots for spamming purposes.", "importance": "Protecting email addresses from harvesting helps reduce spam received by those addresses and enhances user privacy and security.", "recommendation": {"text": "Plain text email addresses were found on the page. These can be harvested by spam bots. Consider replacing them with a contact form or using obfuscation techniques (e.g., JavaScript encoding, HTML entity encoding) to protect them.", "priority": "Medium"}, "blog": "", "score": 0}, "font_legibility": {"pass": false, "small_font_issue_count": 2, "elements_analysed_inline": 1, "declarations_analysed_css": 1527, "problematic_elements": [], "css_issues": [{"selector": ".nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe", "size": "11.0px", "context": "text", "severity": "Medium", "source": "internal <style> #7"}, {"selector": ".nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff", "size": "11.0px", "context": "text", "severity": "Medium", "source": "internal <style> #7"}], "has_external_css": true, "external_css_total_links": 36, "external_css_attempted_fetch": 5, "external_css_analysed_count": 5, "external_css_fetch_errors": 0, "description": "Checks if text on your page is large enough to be easily readable. Small text can be hard to read, especially on mobile devices or for people with visual difficulties.", "importance": "Readable font sizes are key for a good user experience and web accessibility. Ensure no important text is smaller than 12px. This helps users engage with your content effectively. Google's Mobile Usability guidelines emphasize text legibility, and WCAG accessibility standards (which Google supports) recommend minimum font sizes for better accessibility.", "recommendation": {"text": "Found 2 instance(s) where text might be too small. For good readability, all text should generally be 12px or larger (or its equivalent in units like em, rem, pt).", "priority": "Medium"}, "blog": "", "score": 17}, "tap_target_sizing": {"pass": true, "total_interactive_elements": 77, "problematic_elements_count": 0, "problematic_elements": [], "has_touch_specific_css": false, "touch_media_queries": [], "description": "Tap targets are interactive elements users interact with on touchscreens. Best practices recommend a minimum size of 48x48 CSS pixels with adequate spacing (at least 8px) to prevent accidental taps and improve usability, especially for users with motor impairments.", "importance": "Properly sized tap targets significantly improve mobile usability, reducing user frustration and errors. This contributes to a better user experience and aligns with accessibility guidelines (WCAG 2.5.5 Target Size - Level AAA). Google's Mobile Usability guidelines specifically recommend minimum tap target sizes of 48x48 pixels, and this is a factor in their mobile-friendly testing.", "recommendation": null, "blog": "", "score": 17}, "total_score": {"score": 83, "grade": "B"}, "overall_title": "Your Usability is Good", "overall_description": "Your website has good usability, but there are some areas that could be improved for an even better user experience.", "timing": {"total_run_all_time": 0.5048384666442871, "content_fetch_and_parse_time": 0.015448570251464844, "synchronous_analyses_time": 0.014220952987670898, "font_legibility_analysis_time": 0.47496771812438965}}, "localseo_analysis": {"google_business": {"pass": false, "has_gmb_references": false, "has_gmb_links": false, "has_gmb_embeds": false, "has_gbp_mentions": false, "gmb_urls": [], "gmb_embeds": [], "description": "This analysis checks for Google Business Profile integration. Links and embeds improve local search visibility and provide location context for visitors.", "recommendation": {"text": "Add Google Maps links or embeds to connect your website with your Google Business Profile and strengthen local signals.", "priority": "High"}, "importance": "Google Business Profile references strengthen local search signals, build user trust, and enhance location-based search performance. According to Google's guidelines for improving local ranking (https://support.google.com/business/answer/7091), Google Business Profile integration is crucial for local SEO success. Google's business representation guidelines (https://support.google.com/business/answer/3038177) emphasize the importance of consistent business information across all platforms.", "blog": "", "score": 0}, "overall_title": "Local SEO Analysis", "overall_description": "Assesses your website's local search optimization, focusing on Google Business Profile integration and other local ranking factors to help improve visibility in geographically-targeted searches."}, "technology_review_analysis": {"ssl_enabled": {"pass": true, "description": "SSL (Secure Sockets Layer) encrypts the connection between your website and visitors, protecting sensitive data and improving security.", "importance": "SSL is crucial for website security, user trust, and SEO. Google uses HTTPS as a ranking signal, and browsers warn users when visiting non-HTTPS sites.", "recommendation": null, "blog": "", "score": 20}, "robots_meta": {"noindex": false, "content": "index, follow, max-snippet:-1, max-video-preview:-1, max-image-preview:large", "description": "The robots meta tag tells search engines whether they should index your page and follow its links.", "importance": "The robots meta tag is present and allows indexing and following (e.g., 'index, follow' or no 'noindex' directive), which is generally good for SEO.", "recommendation": null, "blog": "", "score": 18}, "dns_servers": {"pass": true, "nameservers": ["becky.ns.cloudflare.com", "jerry.ns.cloudflare.com"], "count": 2, "description": "DNS servers (nameservers) are responsible for translating your domain name to an IP address.", "recommendation": null, "importance": "Your multiple DNS servers provide good redundancy, protecting your website from DNS-related outages.", "blog": "", "score": 7}, "web_server": {"pass": true, "server": "Unknown", "description": "The web server software handles HTTP requests and serves your website content to visitors.", "recommendation": null, "importance": "Hiding server information (like 'Server' and 'X-Powered-By' headers) is a good security practice as it avoids revealing specific software versions that might have known vulnerabilities.", "blog": "", "score": 10}, "charset": {"pass": true, "charset": "UTF-8", "source": "meta tag", "is_standard": true, "description": "Character encoding defines how characters are stored in your HTML document and affects how special characters display.", "importance": "UTF-8 supports international characters and is the recommended encoding for the modern web, ensuring proper display of content across all languages.", "recommendation": null, "blog": "", "score": 14}, "dmarc_record": {"pass": true, "record": "v=DMARC1; p=reject; rua=mailto:<EMAIL>; fo=1; adkim=s; aspf=s", "policy": "reject", "description": "DMARC (Domain-based Message Authentication, Reporting & Conformance) helps protect your domain from email spoofing and phishing attacks.", "importance": "Your DMARC 'reject' policy provides maximum protection against email spoofing by instructing receiving servers to reject suspicious emails.", "recommendation": null, "blog": "", "score": 5}, "spf_record": {"pass": true, "record": "v=spf1 +a +mx include:digitalapex.com.au.spf.auto.dnssmarthost.net -all", "policy_strength": "strict", "description": "SPF (Sender Policy Framework) specifies which servers are authorized to send email on behalf of your domain.", "importance": "Your '-all' directive provides maximum protection by instructing receiving servers to reject emails from unauthorized sources.", "recommendation": null, "blog": "", "score": 5}, "server_ip": {"pass": true, "ip": "*************", "all_ips": ["*************", "*************"], "description": "The server IP address is the numerical label assigned to your web server that allows users to connect to your website.", "importance": "Multiple IP addresses indicate load balancing or geographic distribution, which can improve reliability and performance.", "recommendation": null, "blog": "", "score": 7}, "technologies": {"pass": true, "technologies": [{"name": "WordPress", "version": "Unknown", "category": "CMS"}, {"name": "j<PERSON><PERSON><PERSON>", "version": ".", "category": "JavaScript Library"}, {"name": "Bootstrap", "version": ".", "category": "UI Framework"}, {"name": "Angular", "version": "Detected", "category": "JavaScript Framework"}, {"name": "Google Analytics", "version": "Detected", "category": "Analytics"}, {"name": "Google Tag Manager", "version": "Detected", "category": "Tag Management"}], "technology_count": 6, "description": "Analysis of the technologies and frameworks used by your website, including CMS platforms, JavaScript libraries, analytics tools, and infrastructure services.", "recommendations": null, "importance": "Your technology stack affects website performance, security, and SEO. Regular maintenance of these technologies will ensure optimal website health. CMS and website platforms should be kept updated to prevent security vulnerabilities. JavaScript frameworks can impact page load performance and SEO if not optimized properly. ", "blog": "", "score": 14}, "total_score": {"score": 100, "grade": "A+"}, "overall_title": "Your Technology Review is Excellent", "overall_description": "Your website demonstrates excellent Technology Review. Only minor improvements may be needed."}, "social_analysis": {"facebook": {"pass": true, "page_url": "https://www.facebook.com/profile.php?id=61576569500146", "pixel_id": null, "description": "Facebook is the largest social network. A business presence helps with brand visibility and customer engagement.", "recommendation": null, "importance": "Facebook presence can boost local SEO, brand visibility, and drive referral traffic.", "score": 7}, "twitter": {"pass": false, "profile_url": "Not Found", "description": "Twitter (X) is a real-time information network for diverse conversations.", "recommendation": {"text": "Twitter Card meta tags detected but no profile link found. Add a visible link to your Twitter/X profile.", "priority": "Medium"}, "importance": "Twitter helps with brand visibility, content distribution, customer service, and social signals.", "score": 0}, "instagram": {"pass": true, "profile_url": "https://www.instagram.com/digitalapex.com.au/", "description": "Instagram is a visual platform for showcasing products, services, and company culture.", "recommendation": null, "importance": "Instagram is valuable for visual brands, building identity, and audience engagement through imagery.", "score": 5}, "linkedin": {"pass": true, "profile_url": "https://www.linkedin.com/in/digital-apex-aaa284365?utm_source=share&amp;utm_campaign=share_via&amp;utm_content=profile&amp;utm_medium=android_app", "description": "LinkedIn is a professional networking platform valuable for B2B marketing.", "recommendation": null, "importance": "LinkedIn establishes credibility, improves B2B marketing, and builds business relationships.", "score": 5}, "youtube": {"pass": false, "channel_url": "Not Found", "channel_id": null, "channel_name": null, "description": "YouTube is the second largest search engine, ideal for video marketing.", "recommendation": {"text": "No YouTube channel detected. Consider creating one for video content like demos or tutorials.", "priority": "Low"}, "importance": "A YouTube channel can boost SEO and engagement, especially when videos are embedded or linked.", "blog": "", "statistics": {"subscriberCount": 0, "videoCount": 0, "viewCount": 0}, "score": 0}, "telegram": {"pass": false, "channel_url": "Not Found", "description": "Telegram is a messaging platform popular for channels and group communication.", "recommendation": {"text": "No Telegram presence detected. Consider creating a channel for direct communication with your audience.", "priority": "Low"}, "importance": "Telegram channels can provide direct communication with your audience and build community.", "score": 0}, "social_meta_tags": {"pass": false, "og_tags_present": true, "og_tags_count": 7, "og_tags": {"og:locale": "en_US", "og:type": "website", "og:title": "DigitalApex Agency - Web Design | SEO | Advertising", "og:description": "From startups to enterprises we help ambitious brands grow faster and increase conversions through expert web development", "og:url": "https://digitalapex.com.au/", "og:site_name": "DigitalApex | Web Design | SEO | Advertising", "og:updated_time": "2025-06-22T12:49:28+10:00"}, "missing_og_tags": ["og:image"], "twitter_tags_present": true, "twitter_tags_count": 7, "twitter_tags": {"twitter:card": "summary_large_image", "twitter:title": "DigitalApex Agency - Web Design | SEO | Advertising", "twitter:description": "From startups to enterprises we help ambitious brands grow faster and increase conversions through expert web development", "twitter:label1": "Written by", "twitter:data1": "<EMAIL>", "twitter:label2": "Time to read", "twitter:data2": "3 minutes"}, "missing_twitter_tags": [], "description": "Social meta tags (Open Graph, Twitter Cards) control how content appears when shared, influencing clicks.", "recommendation": {"text": "Missing critical Open Graph tags: og:image. Implement these tags for better social sharing appearance.", "priority": "Medium"}, "importance": "Proper social tags increase visibility and engagement when content is shared, driving referral traffic.", "score": 26}, "share_buttons": {"pass": true, "button_count": 7, "description": "Generic social sharing buttons allow users to easily distribute your content.", "recommendation": null, "importance": "Social sharing increases content visibility and referral traffic.", "score": 15}, "addthis_detected": {"pass": false, "description": "AddThis is a popular third-party service for social sharing buttons.", "recommendation": {"text": "Consider using a sharing service like AddThis if you need a wide range of sharing options easily implemented.", "priority": "Low"}, "importance": "AddThis provides comprehensive sharing tools and analytics.", "score": 0}, "sharethis_detected": {"pass": false, "description": "ShareThis is another widely used service for implementing social sharing functionalities.", "recommendation": {"text": "ShareThis offers an alternative for easy social sharing button integration.", "priority": "Low"}, "importance": "ShareThis can simplify adding sharing buttons and tracking their usage.", "score": 0}, "total_score": {"score": 58, "grade": "C-"}, "overall_title": "Improve Your Social Media Presence", "overall_description": "Your website has some social media integration, but there are several areas that need attention to improve visibility and engagement."}, "performance_analysis": {"javascript_errors": {"pass": true, "error_count": 0, "errors": [], "recommendation": null, "description": "Checks for JavaScript errors logged to the browser console during page load.", "importance": "Console errors can break page functionality, negatively impact user experience, and sometimes interfere with search engine crawling or rendering.", "blog": "", "score": 1}, "deprecated_html": {"pass": true, "count": 0, "elements_by_tag": {}, "elements": [], "description": "Identifies outdated HTML elements that may not be supported or render correctly in modern browsers.", "recommendation": null, "importance": "Using deprecated tags indicates outdated practices, potentially affecting browser compatibility, accessibility, and maintainability. While direct SEO impact is low, it reflects code quality.", "blog": "", "score": 3}, "compression": {"pass": false, "compression_type": "br", "size_mb": 0.122, "compressed_size_mb": 0.122, "compression_ratio": 0, "recommendation": {"text": "Compression (br) is enabled but the ratio (0.0%) is low. Review server configuration or content.", "priority": "Medium"}, "description": "Checks if server-side compression (like gzip or Brotli) is enabled for the HTML document, reducing its transfer size.", "importance": "Compression significantly reduces file sizes, leading to faster downloads, lower bandwidth usage, and improved page load speed, which is a key SEO factor.", "blog": "", "score": 6}, "resource_count": {"html": 1, "js": 30, "css": 36, "images": 32, "fonts": 0, "iframes": 0, "other": 2, "total": 101, "pass": false, "recommendation": {"text": "The page loads a very high number of resources (101). Aggressively reduce requests through techniques like file combination, sprites, lazy loading, and removing unused assets.", "priority": "High"}, "description": "Counts the number of external resources (CSS, JS, images, fonts, etc.) requested by the page.", "importance": "Each resource requires a separate HTTP request. Reducing the number of requests minimizes network latency and speeds up page load, improving user experience and SEO.", "blog": "", "score": 1}, "amp": {"is_amp_page": false, "has_amp_link": false, "amp_url": null, "pass": true, "recommendation": null, "description": "Checks if the page is an AMP page itself or links to a separate AMP version.", "importance": "AMP (Accelerated Mobile Pages) is a framework for creating fast-loading mobile pages. While not mandatory, valid AMP pages can receive preferential treatment in some mobile search results (like carousels).", "blog": "", "score": 0}, "page_size": {"pass": false, "total_estimated_size_mb": 9.51, "size_category": "Very Heavy", "breakdown_estimated_mb": {"html_mb": 0.14, "est_js_mb": 2.93, "est_css_mb": 1.76, "est_images_mb": 4.69}, "recommendation": {"text": "Estimated total page size is 9.51 MB. Aggressively optimize all assets, remove unused resources, and consider code splitting to improve load performance.", "priority": "High"}, "description": "Provides a rough estimate of the total page size (HTML + estimated resources) based on the number of assets linked.", "importance": "Page size directly impacts load time. Smaller pages load faster, improving user experience and potentially boosting SEO rankings (Core Web Vitals). Aim for < 3MB.", "blog": "", "score": 1}, "inline_styles": {"pass": false, "elements_with_style": 56, "total_elements": 804, "inline_style_percentage": 7, "total_style_size_kb": 2.7, "most_common_properties": {"transform": 21, "opacity": 18, "translate": 10, "rotate": 10, "scale": 10, "display": 10, "text-align": 8, "position": 6, "width": 5, "transform-origin": 5}, "style_examples": [{"tag": "html", "style": "scroll-behavior: smooth; overscroll-behavior: none;"}, {"tag": "body", "style": "height: 8601px; overscroll-behavior: none; scroll-behavior: auto;"}, {"tag": "path", "style": "transition: stroke-dashoffset 10ms linear; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 30..."}, {"tag": "div", "style": "inset: 0px; width: 100%; height: 100%; position: fixed; overflow: hidden;"}, {"tag": "div", "style": "translate: none; rotate: none; scale: none; transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, ..."}], "recommendation": {"text": "Moderate use of inline styles (56 elements, 7.0%). Moving styles to external CSS files improves caching and simplifies updates.", "priority": "Medium"}, "description": "analyses the usage of inline style attributes within HTML elements.", "importance": "Inline styles prevent CSS caching, increase HTML file size, and make maintenance harder. Prioritizing external stylesheets improves performance and code organization.", "blog": "", "score": 2}, "performance_timing": {"pass": true, "time_to_first_byte_s": 0.05, "recommendation": null, "description": "Measures the Time To First Byte (TTFB), indicating server responsiveness.", "importance": "TTFB is a key metric reflecting server and network performance. Slow TTFB directly impacts user experience and Core Web Vitals (LCP often depends on it). Aim for < 0.5s.", "blog": "", "score": 15}, "http_protocol": {"pass": true, "protocol_used": "HTTP/2", "alt_svc_header": "h3=\":443\"; ma=86400", "supports_http2": true, "supports_http3": true, "recommendation": null, "description": "Checks the HTTP protocol version used (HTTP/1.1, HTTP/2) and looks for HTTP/3 support via the Alt-Svc header.", "importance": "HTTP/2 and HTTP/3 offer significant performance advantages over HTTP/1.1, such as request multiplexing and header compression, leading to faster page loads.", "blog": "", "score": 8}, "image_optimisation": {"pass": false, "total_images": 41, "next_gen_images_count": 7, "missing_alt_count": 0, "missing_dimensions_count": 23, "problematic_images_sample": [{"src": "https://digitalapex.com.au/wp-content/uploads/2025/03/Digital-Apex-logo-m.png", "alt_missing": false, "dims_missing": true, "not_next_gen": true}, {"src": "", "alt_missing": false, "dims_missing": true, "not_next_gen": true}, {"src": "", "alt_missing": false, "dims_missing": true, "not_next_gen": true}, {"src": "", "alt_missing": false, "dims_missing": true, "not_next_gen": true}, {"src": "", "alt_missing": false, "dims_missing": true, "not_next_gen": true}], "recommendation": {"text": "34 of 41 images are not using next-gen formats (WebP, AVIF). Convert images for better compression and faster loading. 23 of 41 images are missing explicit width/height attributes. Add dimensions to prevent layout shifts (CLS).", "priority": "High"}, "description": "Checks if images use modern formats (WebP, AVIF), have 'alt' text, and specify dimensions.", "importance": "Optimized images load faster (improving LCP), alt text aids SEO and accessibility, and dimensions prevent layout shifts (improving CLS). These are crucial for user experience and Core Web Vitals.", "blog": "", "score": 4}, "minification": {"pass": false, "total_js": 30, "unminified_js_count": 12, "unminified_js_samples": ["https://www.googletagmanager.com/gtag/js?id=GT-M6J8Q587", "https://accounts.google.com/gsi/client", "https://digitalapex.com.au/wp-content/plugins/feux-core/assets/js/feux-core-widget.js?ver=**********", "https://digitalapex.com.au/wp-content/plugins/contact-form-7/includes/swv/js/index.js?ver=6.1", "https://digitalapex.com.au/wp-content/plugins/contact-form-7/includes/js/index.js?ver=6.1"], "total_css": 36, "unminified_css_count": 21, "unminified_css_samples": ["https://digitalapex.com.au/wp-content/plugins/contact-form-7/includes/css/styles.css?ver=6.1", "https://digitalapex.com.au/wp-content/plugins/feux-core/assets/css/wpr-core.css?ver=1.0.0", "https://digitalapex.com.au/wp-content/themes/feux/assets/css/feux-custom.css?ver=**********", "https://digitalapex.com.au/wp-content/themes/feux/assets/css/feux-custom.css?ver=**********", "https://digitalapex.com.au/wp-content/themes/feux/assets/css/feux-custom.css?ver=**********"], "recommendation": {"text": "12 JS file(s) do not appear to be minified. 21 CSS file(s) do not appear to be minified. Minify these assets to reduce file size and improve load times. Check build process or plugins.", "priority": "Medium"}, "description": "Checks if linked JavaScript and CSS filenames suggest they are minified (contain '.min.').", "importance": "Minification removes unnecessary characters (whitespace, comments) from code, reducing file size and leading to faster downloads and parsing.", "blog": "", "score": 0}, "total_score": {"score": 41, "grade": "D"}, "overall_title": "Your Performance Needs Improvement", "overall_description": "Your website has significant performance issues. Address the key recommendations to improve speed and user experience.", "overall_recommendation": "The page loads a very high number of resources (101). Aggressively reduce requests through techniques like file combination, sprites, lazy loading, and removing unused assets. Estimated total page size is 9.51 MB. Aggressively optimize all assets, remove unused resources, and consider code splitting to improve load performance."}, "links_analysis": {"total_score": {"score": 3, "grade": "F"}, "overall_title": "Your Backlink Profile Needs Development", "overall_description": "Your website needs to focus on building a stronger backlink profile. Start with the key recommendations to establish authority and improve search engine visibility.", "overall_backlinks": {"overall_title": "Backlinks Overview: Needs Improvement", "overall_description": "This site has 0 total backlinks from 0 unique domains. There are 2 broken links identified on the analyzed page, and an estimated 0 backlinks with readable anchor text.", "metrics": {"total_backlinks": {"value": 0, "description": "The total number of external pages linking to your entire root domain.", "importance": "A high number of backlinks from diverse, authoritative sources is a strong positive ranking signal.", "recommendation": "Low total backlinks. Focus on acquiring more high-quality backlinks."}, "domain_authority": {"value": 1, "description": "Domain Authority is a score predicting a website's ranking strength based on its overall backlink profile.", "importance": "Higher Domain Authority scores correlate with a greater ability to rank. It's a comparative metric, best used against competitors.", "recommendation": "Domain Authority is low. Focus on overall SEO, quality content, and authoritative backlinks to improve."}, "broken_links_count": {"value": 2, "description": "The number of broken links (e.g., 404 errors) found on the analyzed page during the on-page crawl.", "importance": "Broken links on your page create a poor user experience and can hinder search engine crawlers.", "recommendation": "Found 2 broken/problematic link(s) (22%) out of 9 checked on this page. Fix these to improve user experience and SEO."}, "friendly_links_percentage": {"value": 100, "description": "Percentage of text links on this page with anchor text deemed easily readable (based on Flesch Reading Ease).", "importance": "Readable anchor text improves user experience and helps search engines understand the context of the linked page.", "recommendation": null}}}, "domain_insight": {"overall_title": "Domain & Page Authority: Needs Attention", "overall_description": "The domain has a Domain Authority of 1 and this page has a Page Authority of 1. The domain's Spam Score is -1%.", "metrics": {"domain_authority": {"value": 1, "description": "Domain Authority is a score predicting a website's ranking strength based on its overall backlink profile.", "importance": "Higher Domain Authority scores correlate with a greater ability to rank. It's a comparative metric, best used against competitors.", "recommendation": "Domain Authority is low. Focus on overall SEO, quality content, and authoritative backlinks to improve."}, "page_authority": {"value": 1, "description": "Page Authority predicts the ranking strength of a specific page.", "importance": "Page Authority is influenced by links to that specific page and is useful for gauging individual page strength.", "recommendation": "Page Authority for this specific URL is low. Enhance its content and internal/external links."}, "spam_score": {"value": -1, "description": "Spam Score represents the percentage of sites with similar features to yours that have been found to be penalized or banned by Google.", "importance": "A low spam score is desirable. High scores may indicate a risky backlink profile or on-page issues.", "recommendation": "Spam Score could not be determined. This might be due to a new domain or API issues."}}}, "backlinks_detail": {"overall_title": "Backlink Type Analysis: Data Needed", "overall_description": "Analysis shows 0 dofollow links and 0 nofollow links from 0 unique referring domains. Premium users see more detailed samples.", "metrics": {"total": {"value": 0, "description": "The total number of external pages linking to your entire root domain.", "importance": "A high number of backlinks from diverse, authoritative sources is a strong positive ranking signal.", "recommendation": "Low total backlinks. Focus on acquiring more high-quality backlinks."}, "nofollow_links": {"value": 0, "description": "Count of 'nofollow' links in your backlink profile. Nofollow links typically don't pass link equity.", "importance": "While not directly boosting SEO like dofollow links, nofollow links can still drive traffic and are part of a natural link profile.", "recommendation": "No backlink data available."}, "dofollow_links": {"value": 0, "description": "Count of 'dofollow' (or standard) links in your backlink profile. These links pass link equity.", "importance": "Dofollow links are crucial for SEO as they contribute to your site's authority and ranking potential.", "recommendation": "No backlink data available."}, "unique_domain_count": {"value": 0, "description": "The number of unique root domains that link to your website.", "importance": "Links from a wide range of different domains are generally better than many links from a few domains. It indicates broader recognition.", "recommendation": "Low unique referring domains. Diversify your backlink sources."}}, "top_backlinks_sample": [], "is_premium_user": false, "upgrade_message": null}, "friendly_links": {"overall_title": "Anchor Text Readability: Excellent Readability", "overall_description": "Based on 33 analyzed text links on this page, 100% have easily readable anchor text. 0% may be less readable (0 links sampled as potentially unfriendly).", "metrics": {"friendly_links_percentage": {"value": 100, "description": "Percentage of text links on this page with anchor text deemed easily readable (based on Flesch Reading Ease).", "importance": "Readable anchor text improves user experience and helps search engines understand the context of the linked page.", "recommendation": null}, "unfriendly_links_percentage": {"value": 0, "description": "Percentage of text links on this page with anchor text that may be less readable.", "importance": "High percentage might indicate overly complex or unclear anchor text.", "recommendation": null}, "unfriendly_links_count_sample": {"value": 0, "description": "Number of links in the sample identified as potentially having less readable anchor text.", "importance": "Highlights specific links that may need review for clarity.", "recommendation": null}, "analyzed_text_links_on_page": {"value": 33, "description": "Total number of text-based links on this page that were analyzed for anchor text readability.", "importance": "Provides context for the readability percentages.", "recommendation": null}}, "readability_analysis_summary": "Analyzed 33 text links. 100% deemed readable.", "unfriendly_links_samples": []}, "broken_links": {"overall_title": "On-Page Broken Links: 2 Broken", "overall_description": "Checked 9 links on this page (up to 75 links). Found 2 broken or problematic links (22% of checked).", "metrics": {"links_checked_on_page": {"value": 9, "description": "Number of links on this specific page that were checked for broken status (e.g., 404 errors, timeouts).", "importance": "Understanding the scope of the broken link check.", "recommendation": null}, "broken_count_on_page": {"value": 2, "description": "Number of links found to be broken or problematic during the on-page check.", "importance": "Broken links directly harm user experience and can negatively impact SEO.", "recommendation": "Found 2 broken/problematic link(s) (22%) out of 9 checked on this page. Fix these to improve user experience and SEO."}, "broken_percentage_on_page": {"value": 22, "description": "Percentage of checked links on this page that were found to be broken.", "importance": "A high percentage indicates significant on-page linking issues.", "recommendation": "Found 2 broken/problematic link(s) (22%) out of 9 checked on this page. Fix these to improve user experience and SEO."}}, "broken_analysis_summary": "Found 2 broken link(s) out of 9 checked.", "broken_links_detail_sample": [{"url": "https://www.facebook.com/profile.php?id=61576569500146", "status": "broken", "error_code": "400"}, {"url": "https://www.linkedin.com/in/digital-apex-aaa284365?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app", "status": "broken", "error_code": "999"}]}}, "pagespeed_analysis": "in_progress", "pagespeed_mobile_analysis": "in_progress", "child_pages": {"pages": ["/", "/about/", "/blog-grid/", "/contact/", "/faq/", "/services/"], "total_count": 6}}}