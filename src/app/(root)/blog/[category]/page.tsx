import CategoryPageClient from "./CategoryPageClient";
import { Suspense } from "react";
import blogService from "@/services/blogService";

// Category page ISR: revalidates every 10 minutes
export const revalidate = 600;

interface Params {
  params: Promise<{ category: string }>;
}

export default async function CategoryPage({ params }: Params) {
  const { category } = await params;

  try {
    // Fetch only the first page (10 posts) for SSG
    const data = await blogService.getCategoryPosts(category, 1, 600);
    
    const categoryName = data.categories?.find(cat => cat.slug === category)?.name || decodeURIComponent(category);
    
    const initialPosts = (data.results || []).map((post) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      author: post.author,
      snippet: post.snippet || "",
      publish_timestamp: post.publish_timestamp,
      url: post.url,
      tags: post.tags,
      cover_image: post.cover_image ?? null,
      category: { name: categoryName, slug: category },
    }));

    return (
      <Suspense fallback={null}>
        <CategoryPageClient
          categorySlug={category}
          initialPosts={initialPosts}
          initialCategories={data.categories || []}
          initialCount={data.count || 0}
          initialPage={1}
          initialNextPageUrl={data.next}
          initialPreviousPageUrl={data.previous}
        />
      </Suspense>
    );
  } catch (e) {
    // On failure, render client page which will fetch on mount
    return <CategoryPageClient categorySlug={category} />;
  }
}

// Prebuild all category pages to make navigation instant
export async function generateStaticParams(): Promise<Array<{ category: string }>> {
  try {
    const categories = await blogService.getCategories();
    return categories.map((c) => ({ category: c.slug }));
  } catch {
    return [];
  }
}
