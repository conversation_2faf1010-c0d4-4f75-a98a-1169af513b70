import { Metadata } from "next";
import { Suspense } from "react";
import StaticBlogPageClient from "./StaticBlogPageClient";
import blogService from "@/services/blogService";

// Blog index with ISR: revalidates every 5 minutes
export const revalidate = 300;

// Generate metadata for the blog page
export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "SEO Analyser Blog | Expert SEO Tips, Guides & Industry Insights",
    description:
      "Discover expert SEO tips, comprehensive guides, and latest industry insights. Learn keyword research, on-page optimization, technical SEO, and digital marketing strategies to boost your website's rankings.",
    keywords: [
      "SEO Analyser",
      "SEO blog",
      "SEO tips",
      "SEO guides",
      "keyword research",
      "on-page SEO",
      "technical SEO",
      "backlink building",
      "local SEO",
      "SEO audit",
      "search engine optimization",
      "website optimization",
      "SEO strategies",
      "SEO best practices",
    ],
    openGraph: {
      title: "SEO Blog | Expert SEO Tips, Guides & Industry Insights",
      description:
        "Discover expert SEO tips, comprehensive guides, and latest industry insights. Learn keyword research, on-page optimization, technical SEO, and digital marketing strategies to boost your website's rankings.",
      type: "website",
      url: "https://seoanalyser.com.au/blog",
      siteName: "SEO Analyser",
      images: [
        {
          url: "https://seoanalyser.com.au/images/appLogo.svg",
          width: 1200,
          height: 630,
          alt: "SEO Analyser Blog - Expert SEO Tips and Guides",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      site: "@SEO_ANALYSER",
      creator: "@SEO_ANALYSER",
      title: "SEO Blog | Expert SEO Tips, Guides & Industry Insights",
      description:
        "Discover expert SEO tips, comprehensive guides, and latest industry insights. Learn keyword research, on-page optimization, technical SEO, and digital marketing strategies to boost your website's rankings.",
      images: ["https://seoanalyser.com.au/images/appLogo.svg"],
    },
    alternates: {
      canonical: "https://seoanalyser.com.au/blog",
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

// Server component that renders the client component with initial data (ISR)
const BlogPage = async () => {
  try {
    // Fetch only the first page (10 posts) for SSG
    const data = await blogService.getBlogPosts({ page: 1 }, 300);
    
    const initialPosts = (data.results || []).map((post) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      author: post.author,
      snippet: post.snippet || "",
      publish_timestamp: post.publish_timestamp,
      url: post.url,
      tags: post.tags,
      cover_image: post.cover_image ?? null,
    }));

    const initialCategories = data.categories || [];
    const initialCount = data.count;
    const initialNextPageUrl = data.next;
    const initialPreviousPageUrl = data.previous;

    return (
      <Suspense fallback={null}>
        <StaticBlogPageClient
          initialPosts={initialPosts}
          initialCategories={initialCategories}
          initialCount={initialCount}
          initialNextPageUrl={initialNextPageUrl}
          initialPreviousPageUrl={initialPreviousPageUrl}
          initialPage={1}
        />
      </Suspense>
    );
  } catch (error) {
    // If server fetch fails, fall back to client-only rendering
    return (
      <Suspense fallback={null}>
        <StaticBlogPageClient
          initialPosts={[]}
          initialCategories={[]}
          initialCount={0}
          initialNextPageUrl={null}
          initialPreviousPageUrl={null}
          initialPage={1}
        />
      </Suspense>
    );
  }
};

export default BlogPage;
