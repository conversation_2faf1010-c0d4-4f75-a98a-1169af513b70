"use client";

import React, { useEffect, useState, Suspense } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { BlogCardProps } from "../components/BlogCard";
import BlogCard from "../components/BlogCard";
import SearchInput from "../components/SearchInput";
import CategoryList from "../components/CategoryList";
import { Pagination } from "../components/Pagination";
import BannerScroll from "../components/BannerScroll";
import BlogCardSkeleton from "../components/BlogCardSkeleton";
import BannerScrollSkeleton from "../components/BannerScrollSkeleton";
import CategoryHeaderSkeleton from "../components/CategoryHeaderSkeleton";
import http from "@/services/httpService";

interface Category {
  name: string;
  slug: string;
}

interface BlogApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Array<{
    id: number;
    title: string;
    slug: string;
    author: { id: number; email: string; display_name: string };
    publish_timestamp: number;
    snippet: string;
    url: string;
    tags: string[];
    cover_image: string | null;
  }>;
  categories: Category[];
}

interface Props {
  categorySlug: string;
  initialPosts?: BlogCardProps[];
  initialCategories?: Category[];
  initialCount?: number;
  initialPage?: number;
  initialNextPageUrl?: string | null;
  initialPreviousPageUrl?: string | null;
}

const POSTS_PER_PAGE = 10;

// Separate component for search params to wrap in Suspense
function CategoryPageWithSearchParams({
  categorySlug,
  initialPosts,
  initialCategories,
  initialCount,
  initialPage,
  initialNextPageUrl,
  initialPreviousPageUrl,
}: Props) {
  const searchParams = useSearchParams();

  const [blogPosts, setBlogPosts] = useState<BlogCardProps[]>(
    initialPosts || []
  );
  const [categories, setCategories] = useState<Category[]>(
    initialCategories || []
  );
  const [loading, setLoading] = useState(!initialPosts);
  const [error, setError] = useState<string | null>(null);
  const [categoryName, setCategoryName] = useState<string>(
    initialCategories?.find((c) => c.slug === categorySlug)?.name ||
      decodeURIComponent(categorySlug)
  );
  const [currentPage, setCurrentPage] = useState(initialPage || 1);
  const [totalPages, setTotalPages] = useState(
    initialCount ? Math.ceil(initialCount / POSTS_PER_PAGE) : 1
  );
  const [nextPageUrl, setNextPageUrl] = useState<string | null>(initialNextPageUrl ?? null);
  const [previousPageUrl, setPreviousPageUrl] = useState<string | null>(initialPreviousPageUrl ?? null);

  const pageParam = searchParams?.get("page") || "1";

  useEffect(() => {
    const fetchData = async () => {
      const currentPageNum = Number(pageParam) || 1;

      // If we have initial data and are on first page, skip fetching
      if (initialPosts && currentPageNum === 1) {
        setCurrentPage(currentPageNum);
        return;
      }

      setLoading(true);
      setError(null);
      try {
        const response = await http.get(
          `/api/blog/category/${encodeURIComponent(categorySlug)}/?${
            currentPageNum > 1 ? `page=${currentPageNum}` : ""
          }`,
          { useAuth: false }
        );
        const data: BlogApiResponse = response.data;
        const catName =
          data.categories.find((cat: Category) => cat.slug === categorySlug)
            ?.name || decodeURIComponent(categorySlug);

        const posts: BlogCardProps[] = data.results.map((post) => ({
          id: post.id,
          title: post.title,
          slug: post.slug,
          author: post.author,
          snippet: post.snippet || "",
          publish_timestamp: post.publish_timestamp,
          url: post.url,
          tags: post.tags,
          cover_image: post.cover_image,
          category: { name: catName, slug: categorySlug },
        }));

        setBlogPosts(posts);
        setCategories(data.categories);
        setCategoryName(catName);
        setCurrentPage(currentPageNum);
        setTotalPages(Math.ceil((data.count || 0) / POSTS_PER_PAGE));
        setNextPageUrl(data.next);
        setPreviousPageUrl(data.previous);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to load category posts"
        );
        try {
          const catResponse = await http.get("/api/blog/categories/", {
            useAuth: false,
          });
          setCategories(catResponse.data.categories);
        } catch {}
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [categorySlug, pageParam]);

  return (
    <div className="w-full mt-8 lg:mt-[84px] mb-12 container">
      <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1 flex flex-col gap-4">
          <SearchInput />
          <CategoryList categories={categories} currentCategory={categorySlug} />
        </div>

        <div className="lg:col-span-3">
          {loading && (
            <>
              <CategoryHeaderSkeleton />
              <BannerScrollSkeleton />
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
                {Array.from({ length: 6 }).map((_, index) => (
                  <BlogCardSkeleton key={index} />
                ))}
              </div>
            </>
          )}

          {error && !loading && (
            <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
              <h2 className="text-xl font-bold text-red-700 mb-2">
                Error Loading Category
              </h2>
              <p className="text-red-600 mb-4">
                We're having trouble loading the blog posts for this category.
                Please try again later.
              </p>
              <Link href="/blog" className="text-primary hover:underline">
                ← Back to Blog
              </Link>
            </div>
          )}

          {!loading && !error && (
            <>
              <div className="mb-6">
                <div className="flex flex-wrap gap-2 mb-4">
                  <Link
                    href="/blog"
                    className="text-primary hover:underline inline-block"
                  >
                    Blog
                  </Link>
                  <span className="text-gray-400">/</span>
                  <span className="text-gray-600 capitalize">
                    {categoryName}
                  </span>
                </div>
                <h1 className="text-secondary font-black text-2xl lg:text-4xl mt-2">
                  {categoryName}
                </h1>
                <p className="text-gray-600 mt-2">
                  Browse all blog posts in the {categoryName} category.
                </p>
              </div>

              {blogPosts.length >= 3 && (
                <BannerScroll blogData={blogPosts.slice(0, 3)} />
              )}

              {blogPosts.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
                    {blogPosts.map((post) => (
                      <BlogCard key={post.id} {...post} />
                    ))}
                  </div>
                  
                  {/* Pagination */}
                  {totalPages > 1 && (
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      nextPageUrl={nextPageUrl}
                      previousPageUrl={previousPageUrl}
                    />
                  )}
                </>
              ) : (
                <div className="p-6 bg-gray-50 border border-gray-200 rounded-lg text-center">
                  <h2 className="text-xl font-bold text-gray-700 mb-2">
                    No Posts Found in This Category
                  </h2>
                  <p className="text-gray-600">
                    There are currently no blog posts available in the {" "}
                    {categoryName} category. Please check back later or browse
                    other categories.
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// Main export component with Suspense wrapper
export default function CategoryPageClient(props: Props) {
  return (
    <Suspense fallback={
      <div className="w-full mt-8 lg:mt-[84px] mb-12 container">
        <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1 flex flex-col gap-4">
            <SearchInput />
            <CategoryList categories={props.initialCategories || []} currentCategory={props.categorySlug} />
          </div>
          <div className="lg:col-span-3">
            <CategoryHeaderSkeleton />
            <BannerScrollSkeleton />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6 auto-rows-fr">
              {Array.from({ length: 6 }).map((_, index) => (
                <BlogCardSkeleton key={index} />
              ))}
            </div>
          </div>
        </div>
      </div>
    }>
      <CategoryPageWithSearchParams {...props} />
    </Suspense>
  );
}

