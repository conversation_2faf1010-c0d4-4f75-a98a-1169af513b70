import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  // Get the response
  const response = NextResponse.next();

  // (development check removed - unused)

  // Add security headers
  response.headers.set("X-Frame-Options", "SAMEORIGIN");
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set(
    "Permissions-Policy",
    "camera=(), microphone=(), geolocation=()"
  );

  // Comprehensive CORS headers for all requests
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "X-Requested-With, Content-Type, Authorization, X-Device-ID"
  );
  response.headers.set("Access-Control-Expose-Headers", "X-Device-ID");

  // Handle preflight requests
  if (request.method === "OPTIONS") {
    return new NextResponse(null, { status: 200, headers: response.headers });
  }

  // Comprehensive Content Security Policy - Single source of truth
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.googletagmanager.com https://*.google-analytics.com https://*.google.com https://*.gstatic.com https://static.cloudflareinsights.com https://*.doubleclick.net https://*.hotjar.com https://*.hotjar.io",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https: http:",
    "font-src 'self' data: https://fonts.gstatic.com",
    "connect-src 'self' https://seoanalyser.com.au https://*.google-analytics.com https://*.google.com https://*.googletagmanager.com https://*.doubleclick.net https://*.gstatic.com https://*.hotjar.com https://*.hotjar.io wss://*.hotjar.com https://api.seoanalyser.com.au",
    "frame-src 'self' https://*.google.com https://*.googletagmanager.com https://*.doubleclick.net",
    "object-src 'none'",
    "base-uri 'self'",
  ].join("; ");

  response.headers.set("Content-Security-Policy", csp);

  // Add Strict-Transport-Security header
  response.headers.set(
    "Strict-Transport-Security",
    "max-age=31536000; includeSubDomains; preload"
  );

  // Prevent browsers from serving stale HTML for SSG/ISR pages.
  // We allow CDN/server-side caching (Edge/Next) via the default Next.js behavior,
  // but instruct downstream browsers to always revalidate HTML (no-store can
  // be too aggressive for some setups). Use 'no-cache' so the browser must
  // revalidate with the server before using a cached copy.
  // Only set for HTML responses or when the pathname looks like a page.
  try {
    const accept = request.headers.get("accept") || "";
    const pathname = request.nextUrl?.pathname || request.url || "";

    const looksLikeHtmlRequest =
      accept.includes("text/html") ||
      /^(\/|\/blog|\/posts|\/dashboard)/.test(pathname);

    if (looksLikeHtmlRequest) {
      // Instruct browsers to revalidate on each request but allow intermediates to cache
      // for short duration. This avoids the stale-page issue where browser serves old SSG
      // HTML until user hard-refreshes.
      response.headers.set(
        "Cache-Control",
        "no-cache, max-age=600, must-revalidate"
      );
    }
  } catch (_e) {
    // If headers access fails for any reason, keep going without breaking responses.
  }

  return response;
}

// Apply middleware to all routes
export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
