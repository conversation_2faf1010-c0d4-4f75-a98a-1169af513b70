import { motion } from "framer-motion";
import {
  CheckIcon,
  CrossIcon,
  ChainIcon,
  DocumentCrossIcon,
} from "@/ui/icons/general";
import StateCard from "../../StateCard";
import OverallSection from "../OverallSection";
import ShowMoreSection from "../usability/ShowMoreSection";
import Table from "@/ui/Table";
import { LinksAnalysisData } from "./types";
import {
  formatMetricValue,
  formatMetricValueWithPrefix,
  getMetricInfo,
  getActiveData,
} from "./utils";

type FriendlyLinksTabProps = {
  data: LinksAnalysisData;
  itemVariants: any;
};

export default function FriendlyLinksTab({
  data,
  itemVariants,
}: FriendlyLinksTabProps) {
  const friendlyLinks = data.friendly_links;
  const activeData = getActiveData("friendly_links", data);

  if (!friendlyLinks) {
    return (
      <div className="col-span-full p-4 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-red-700 text-sm font-medium">
          ⚠️ Something went wrong while getting friendly links data
        </p>
      </div>
    );
  }

  return (
    <>
      <motion.div variants={itemVariants}>
        <OverallSection
          title={activeData.title}
          description={activeData.description}
        />
      </motion.div>

      <motion.div
        variants={itemVariants}
        className="w-full grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4"
      >
        <StateCard
          icon={<CheckIcon className="w-6 h-6" />}
          value={formatMetricValue(
            friendlyLinks,
            "friendly_links_percentage",
            0,
            "percentage"
          )}
          label="Friendly Links %"
        />
        <StateCard
          icon={<CrossIcon className="w-6 h-6" />}
          value={formatMetricValue(
            friendlyLinks,
            "unfriendly_links_percentage",
            0,
            "percentage"
          )}
          label="Unfriendly Links %"
        />
        <StateCard
          icon={<ChainIcon className="w-6 h-6" />}
          value={formatMetricValue(
            friendlyLinks,
            "analyzed_text_links_on_page",
            0,
            "count"
          )}
          label="Links Analyzed"
        />
        <StateCard
          icon={<CrossIcon className="w-6 h-6" />}
          value={formatMetricValue(
            friendlyLinks,
            "unfriendly_links_count_sample",
            0,
            "count"
          )}
          label="Unfriendly Samples"
        />
      </motion.div>

      {/* Detailed sections */}
      <div className="space-y-6 mt-6">
        {/* Friendly Links Percentage Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Friendly Link Percentage"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    friendlyLinks,
                    "friendly_links_percentage",
                    0,
                    "percentage"
                  )}
                </div>
                {getMetricInfo(friendlyLinks, "friendly_links_percentage")
                  ?.description ||
                  "Percentage of text links on this page with anchor text deemed easily readable (based on Flesch Reading Ease)."}
              </div>
            }
            importance={
              getMetricInfo(friendlyLinks, "friendly_links_percentage")
                ?.importance
            }
            recommendation={
              getMetricInfo(friendlyLinks, "friendly_links_percentage")
                ?.recommendation || undefined
            }
            icon={<CheckIcon className="w-12 h-12 text-green-600" />}
          />
        </div>

        {/* Analyzed Text Links Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Analyzed Text Links"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    friendlyLinks,
                    "analyzed_text_links_on_page",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(friendlyLinks, "analyzed_text_links_on_page")
                  ?.description ||
                  "Total number of text-based links on this page that were analyzed for anchor text readability."}
              </div>
            }
            importance={
              getMetricInfo(friendlyLinks, "analyzed_text_links_on_page")
                ?.importance
            }
            recommendation={
              getMetricInfo(friendlyLinks, "analyzed_text_links_on_page")
                ?.recommendation || undefined
            }
            icon={<ChainIcon className="w-12 h-12 text-blue-600" />}
          />
        </div>

        {/* Unfriendly Links Percentage Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Unfriendly Links Percentage"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    friendlyLinks,
                    "unfriendly_links_percentage",
                    0,
                    "percentage"
                  )}
                </div>
                {getMetricInfo(friendlyLinks, "unfriendly_links_percentage")
                  ?.description ||
                  "Percentage of text links on this page with anchor text that may be less readable."}
              </div>
            }
            importance={
              getMetricInfo(friendlyLinks, "unfriendly_links_percentage")
                ?.importance
            }
            recommendation={
              getMetricInfo(friendlyLinks, "unfriendly_links_percentage")
                ?.recommendation || undefined
            }
            icon={<CrossIcon className="w-12 h-12 text-orange-600" />}
          />
        </div>

        {/* Unfriendly Links Count Sample Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Unfriendly Links Sample"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    friendlyLinks,
                    "unfriendly_links_count_sample",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(friendlyLinks, "unfriendly_links_count_sample")
                  ?.description ||
                  "Number of links in the sample identified as potentially having less readable anchor text."}
              </div>
            }
            importance={
              getMetricInfo(friendlyLinks, "unfriendly_links_count_sample")
                ?.importance
            }
            recommendation={
              getMetricInfo(friendlyLinks, "unfriendly_links_count_sample")
                ?.recommendation || undefined
            }
            icon={<DocumentCrossIcon className="w-12 h-12 text-red-600" />}
          >
            {/* Unfriendly Links Table */}
            {friendlyLinks?.unfriendly_links_samples &&
              friendlyLinks.unfriendly_links_samples.length > 0 && (
                <div className="mt-4">
                  <h5 className="font-medium text-gray-900 mb-3">
                    Links That Could Be Improved
                  </h5>
                  <Table>
                    <Table.Header>
                      <th className="pb-4 whitespace-nowrap">URL</th>
                      <th className="text-center pb-4">Anchor Text</th>
                      <th className="text-center pb-4">Readability Score</th>
                    </Table.Header>
                    <Table.Body>
                      {friendlyLinks.unfriendly_links_samples.map(
                        (item, index) => (
                          <Table.Row key={index}>
                            <td className="py-4 pr-2 sm:pr-4 break-words">
                              <a
                                href={item.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-primary hover:underline text-xs sm:text-sm"
                              >
                                {item.url || "N/A"}
                              </a>
                            </td>
                            <td className="py-4 break-words text-xs sm:text-sm text-center max-w-[200px]">
                              {item.anchor_text || "N/A"}
                            </td>
                            <td className="py-4 text-center text-xs sm:text-sm">
                              {item.readability_score?.toFixed(1) || "N/A"}
                            </td>
                          </Table.Row>
                        )
                      )}
                    </Table.Body>
                  </Table>
                </div>
              )}
          </ShowMoreSection>
        </div>
      </div>
    </>
  );
}
