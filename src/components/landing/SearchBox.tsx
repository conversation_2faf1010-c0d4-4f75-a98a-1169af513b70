"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuditStore } from "@/store/auditStore";

import { SearchIcon } from "@/ui/icons/action";
import TooltipBottom from "@/ui/TooltipBottom";

// Lightweight URL validation function
const validateUrl = (url: string): string | null => {
  if (!url.trim()) {
    return "Please enter website address";
  }

  const urlPattern =
    /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]{2,})(\/[^\s]*)?$/;
  if (!urlPattern.test(url.trim())) {
    return "Please enter a valid website address";
  }

  return null;
};

export default function SearchBox() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [urlValue, setUrlValue] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [touched, setTouched] = useState(false);
  const router = useRouter();
  const setWebsiteUrl = useAuditStore((s) => s.setWebsiteUrl);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUrlValue(value);

    // Clear error when user starts typing
    if (error && value.trim()) {
      setError(null);
    }
  };

  const handleInputBlur = () => {
    setIsFocused(false);
    setTouched(true);

    // Validate on blur if touched
    if (touched && urlValue.trim()) {
      const validationError = validateUrl(urlValue);
      setError(validationError);
    }
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);

    const validationError = validateUrl(urlValue);
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Extract only the domain from the URL
      const url = urlValue.trim();

      // Regex to extract domain from various URL formats
      // Handles: protocol://www.domain.com/path, www.domain.com/path, domain.com/path
      // Supports all TLDs, subdomains, and international domains
      const domainRegex =
        /^(?:https?:\/\/)?(?:www\.)?([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)(?:\/.*)?$/;

      const match = url.match(domainRegex);

      if (match && match[1]) {
        const domain = match[1];
        // Store the domain and navigate to /audit
        setWebsiteUrl(domain);
        router.push("/audit");
      } else {
        // Fallback: if regex fails, try to extract domain manually
        const cleanUrl = url.replace(/^https?:\/\//, "").replace(/^www\./, "");
        const domainPart = cleanUrl.split("/")[0]?.split("?")[0]?.split("#")[0];
        if (domainPart) {
          setWebsiteUrl(domainPart);
          router.push("/audit");
        }
      }
    } catch (_error) {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-[962px] mx-auto relative">
      <div>
        <div className="relative">
          <form
            onSubmit={onSubmit}
            className={`w-full bg-white/80 backdrop-blur-[4px] p-3 lg:p-4 rounded-2xl flex items-center gap-4 relative z-20  transition-all duration-200 ${
              isFocused ? "shadow-md ring-2 ring-primary/65" : ""
            }`}
          >
            <div className="flex-1 relative">
              {error && touched && (
                <div className="absolute top-full -bottom-3.5 left-0 right-0 mt-1 z-30 animate-in fade-in slide-in-from-top-1 duration-200 max-w-72">
                  <TooltipBottom
                    open={!!error}
                    message={error}
                    className="w-full"
                  />
                </div>
              )}
              <input
                type="text"
                value={urlValue}
                onChange={handleInputChange}
                name="urlName"
                placeholder="Enter your website URL..."
                aria-label="Website URL"
                className="w-full px-2 lg:px-4 py-2 appearance-none bg-transparent focus:outline-none font-bold text-secondary placeholder-secondary/40 text-sm lg:text-base [&:-webkit-autofill]:!bg-transparent [&:-webkit-autofill]:!text-secondary [&:-webkit-autofill]:!shadow-[0_0_0_30px_rgba(255,255,255,0.8)_inset]"
                onFocus={() => setIsFocused(true)}
                onBlur={handleInputBlur}
              />
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className={`bg-[#914AC4] hover:bg-[#7d3fa8] hover:scale-[1.03] active:scale-[0.97] !w-[60px] !h-[44px] lg:!w-[80px] lg:!h-[48px] rounded-lg flex items-center justify-center transition-all duration-200 px-4 lg:px-6 ml-2 ${
                isSubmitting ? "opacity-80" : ""
              }`}
              aria-label="Analyze website"
            >
              <div className="w-5 h-5 lg:w-6 lg:h-6 flex items-center justify-center">
                {isSubmitting ? (
                  <div className="w-5 h-5 lg:w-6 lg:h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <SearchIcon className="w-5 h-5 lg:w-6 lg:h-6 text-white" />
                )}
              </div>
            </button>
          </form>

          <p className="px-4 text-secondary text-xs lg:!text-sm  lg:text-left pt-4 lg:pt-3">
            Enter your website URL to get your free audit...
          </p>
        </div>

        <ShapeUnderSearch />
        <ShapeSearch2 />
      </div>
    </div>
  );
}

function ShapeUnderSearch() {
  return (
    <svg
      width="40"
      height="58"
      viewBox="0 0 40 58"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="absolute top-[53px] right-[-18px] z-0 floating-animate hidden lg:block"
    >
      <path
        d="M6.73278 2.67338C8.10381 0.918542 10.3069 0.028439 12.5121 0.338367L20.8161 1.50541C23.0214 1.81534 24.8938 3.27821 25.728 5.34298L28.8693 13.118C29.7035 15.1827 29.3728 17.5357 28.0018 19.2905L22.8391 25.8985C21.4681 27.6533 19.265 28.5434 17.0597 28.2335L8.75576 27.0664C6.55051 26.7565 4.67812 25.2936 3.8439 23.2289L0.702606 15.4539C-0.131614 13.3891 0.199075 11.0361 1.57011 9.2813L6.73278 2.67338Z"
        fill="#914AC4"
      />
      <path
        d="M33.2654 50.3401C33.6353 49.8666 34.2296 49.6265 34.8246 49.7101L37.0649 50.025C37.6599 50.1086 38.165 50.5033 38.3901 51.0603L39.2376 53.1579C39.4626 53.715 39.3734 54.3498 39.0035 54.8232L37.6107 56.606C37.2408 57.0794 36.6464 57.3196 36.0515 57.2359L33.8112 56.9211C33.2162 56.8375 32.711 56.4428 32.486 55.8857L31.6385 53.7881C31.4134 53.2311 31.5026 52.5963 31.8725 52.1228L33.2654 50.3401Z"
        fill="#914AC4"
      />
    </svg>
  );
}

function ShapeSearch2() {
  return (
    <svg
      width="50"
      height="58"
      viewBox="0 0 50 58"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-[38px] h-[46px] lg:w-[50px] lg:h-[58px] absolute bottom-[-65px] left-[-30px] lg:left-[-40px] z-0 floating-animate"
    >
      <path
        opacity="0.3"
        d="M38.5 1.1547C39.7376 0.440169 41.2624 0.440169 42.5 1.1547L47.1603 3.8453C48.3979 4.55983 49.1603 5.88034 49.1603 7.3094V12.6906C49.1603 14.1197 48.3979 15.4402 47.1603 16.1547L42.5 18.8453C41.2624 19.5598 39.7376 19.5598 38.5 18.8453L33.8397 16.1547C32.6021 15.4402 31.8397 14.1197 31.8397 12.6906V7.3094C31.8397 5.88034 32.6021 4.55983 33.8397 3.8453L38.5 1.1547Z"
        fill="#AD73D9"
      />
      <g opacity="0.8" filter="url(#filter0_f_0_1)">
        <path
          d="M15.9 23.7898C17.8183 22.6823 20.1817 22.6823 22.1 23.7898L29.3234 27.9602C31.2417 29.0677 32.4234 31.1145 32.4234 33.3296V41.6704C32.4234 43.8855 31.2417 45.9323 29.3234 47.0398L22.1 51.2102C20.1817 52.3177 17.8183 52.3177 15.9 51.2102L8.67661 47.0398C6.75832 45.9323 5.57661 43.8855 5.57661 41.6704L5.57661 33.3296C5.57661 31.1145 6.75832 29.0677 8.67661 27.9602L15.9 23.7898Z"
          fill="#914AC4"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_0_1"
          x="0.0596504"
          y="17.4422"
          width="37.8807"
          height="40.1156"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="2.75847"
            result="effect1_foregroundBlur_0_1"
          />
        </filter>
      </defs>
    </svg>
  );
}
