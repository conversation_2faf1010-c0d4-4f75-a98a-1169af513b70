@import "tailwindcss";

/* FactorA Font Face Declarations with fallback fonts */
@font-face {
  font-family: "FactorA";
  src: url("/fonts/FactorA/FactorA-Regular.woff2") format("woff2"),
    url("/fonts/FactorA/FactorA-Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "FactorA";
  src: url("/fonts/FactorA/FactorAMedium-Regular.woff2") format("woff2"),
    url("/fonts/FactorA/FactorAMedium-Regular.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "FactorA";
  src: url("/fonts/FactorA/FactorA-Bold.woff2") format("woff2"),
    url("/fonts/FactorA/FactorA-Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "FactorA Light";
  src: url("/fonts/FactorA/FactorALight-Regular.woff2") format("woff2"),
    url("/fonts/FactorA/FactorALight-Regular.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@theme {
  --font-sans: var(--font-nunito-sans);
  --color-primary: var(--color-primary);
  --color-secondary: var(--color-secondary);
  --color-background: var(--background);
  --color-light-gray: var(--color-light-gray);
  --color-light-gray-2: var(--color-light-gray-2);
  --color-light-gray-3: var(--color-light-gray-3);
  --color-light-gray-4: var(--color-light-gray-4);
  --color-light-gray-5: var(--color-light-gray-5);
  --color-light-gray-6: var(--color-light-gray-6);
  --color-primary-gray: var(--color-primary-gray);
  --color-light-blue: var(--color-light-blue);
  --color-primary-yellow: var(--color-primary-yellow);
  --color-primary-red: var(--color-primary-red);
  --color-primary-red-2: var(--color-primary-red-2);
  --color-primary-green: var(--color-primary-green);
  --color-primary-green-2: var(--color-primary-green-2);
  --color-primary-orange: var(--color-primary-orange);
  --color-primary-pink: var(--color-primary-pink);
}

/* max-width: 1240px; */
@utility container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1240px;

  @media (max-width: 64rem) {
    padding-inline: 16px;
  }

  @media (min-width: 64rem) and (max-width: 1300px) {
    padding-inline: 40px;
  }

  @media (min-width: 1300px) {
    padding-inline: 0;
  }
}

:root {
  --background: rgba(244, 244, 244, 1);
  --color-primary: rgba(145, 74, 196, 1);
  --color-secondary: rgba(52, 64, 84, 1);
  --color-light-gray: rgba(224, 224, 224, 1);
  --color-light-gray-2: rgba(234, 234, 234, 1);
  --color-light-gray-3: rgba(108, 117, 125, 1);
  --color-light-gray-4: rgba(175, 175, 175, 1);
  --color-light-gray-5: rgba(205, 205, 205, 1);
  --color-light-gray-6: rgba(244, 244, 244, 1);
  --color-primary-gray: rgba(95, 102, 108, 1);
  --color-light-blue: rgba(212, 242, 255, 1);
  --color-primary-yellow: rgba(255, 204, 0, 1);
  --color-primary-red: rgba(255, 59, 48, 1);
  --color-primary-red-2: rgba(246, 110, 112, 1);
  --color-primary-green: rgba(52, 199, 89, 1);
  --color-primary-green-2: rgba(67, 176, 119, 1);
  --color-primary-orange: rgba(255, 168, 24, 1);
  --color-primary-pink: rgba(255, 165, 218, 1);
}

@layer components {
  .max-w-full__customeLG {
    @apply lg:!max-w-full min-[1300px]:!px-10;
  }

  .btn {
    @apply rounded-lg py-3 px-6 border flex items-center justify-center gap-[9px] cursor-pointer relative overflow-hidden;
  }

  .btn--sm {
    @apply !px-4 !py-2;
  }

  .btn--primary {
    @apply bg-primary border-primary text-white font-bold !leading-[21px] text-base hover:opacity-80 duration-200;
  }

  .btn--outline-light {
    @apply !py-2 !text-sm !border-light-gray !text-secondary/70;
  }

  .btn--outline {
    @apply border-secondary text-secondary font-bold !leading-[21px] text-base;
  }

  .btn--primary__outline {
    @apply bg-primary/10 border-primary text-primary font-bold !leading-[21px] text-base;
    transition-duration: 700ms;
  }

  .btn--primary__outline::before {
    content: "";
    @apply absolute inset-0 w-0 h-full bg-gradient-to-r from-primary to-primary opacity-10;
    transition-duration: 700ms;
  }

  .btn--primary__outline * {
    @apply z-10;
  }

  .btn--primary__outline:hover {
    @apply text-white;
  }

  .btn--primary__outline:hover::before {
    @apply w-full opacity-100;
  }

  .btn--secondary {
    @apply text-secondary bg-secondary/10 border-transparent text-sm !leading-[19px] font-black;
  }

  .badge {
    @apply p-2 rounded-lg flex items-center justify-center border border-light-gray text-secondary/80 text-xs font-medium;
  }

  .badge--primary {
    @apply border-primary text-primary bg-primary/10;
  }

  .badge--danger {
    @apply border-primary-red text-primary-red bg-primary-red/15 font-semibold;
  }

  .badge--warning {
    @apply border-primary-yellow text-[#c7a00e] bg-primary-yellow/20 font-semibold;
  }

  .badge--success {
    @apply border-primary-green text-primary-green bg-primary-green/15 font-semibold;
  }

  /* Recommendation badges */
  .recommendation-badge {
    @apply min-w-[70px] sm:min-w-[90px] md:min-w-[100px] text-center py-1 px-2 whitespace-nowrap text-xs font-semibold transition-all duration-100 ease-in-out;
  }

  .recommendation-category {
    @apply bg-gray-100 text-gray-700;
  }

  .recommendation-category.active {
    @apply bg-primary/15 text-primary;
  }

  /* Improved recommendation section styling */
  .recommendations-container {
    @apply shadow-md rounded-lg overflow-hidden;
  }

  .recommendation-item {
    @apply transition-all duration-300 hover:shadow-md;
  }

  table thead tr th {
    @apply text-xs font-semibold text-secondary text-left break-words;
  }

  table thead tr th:first-child {
    @apply pl-2 sm:pl-4;
  }

  table tbody tr td {
    @apply text-sm text-secondary text-left first:pl-2 sm:first:pl-4 overflow-hidden last:pr-2 sm:last:pr-4 first:rounded-l-lg last:rounded-r-lg first:border-l last:border-r border-y border-light-gray group-odd:!border-0 break-words;
  }

  .textField__input {
    @apply appearance-auto border outline-0 border-light-gray p-4 rounded-lg text-secondary placeholder-light-gray;
  }
}
html {
  scroll-behavior: smooth;
}
html,
body {
  padding: 0;
  margin: 0;
  min-height: 100vh;
  user-select: none;
  background-color: var(--color-background);
}

body {
  overflow-x: hidden;
}

* {
  user-select: text;
}

button {
  cursor: pointer;
  outline: none;
}

article h1 {
  font-size: 56px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
  @apply font-bold mt-8 mb-4;
  letter-spacing: -0.025em;
}

article h2 {
  font-size: 42px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
  @apply font-bold mt-6 mb-3;
  letter-spacing: -0.025em;
}

article h3 {
  font-size: 32px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
  @apply font-semibold mt-4 mb-3;
  letter-spacing: -0.02em;
}

/* Mobile font sizes */
@media (max-width: 768px) {
  article h1 {
    font-size: 32px;
  }

  article h2 {
    font-size: 26px;
  }

  article h3 {
    font-size: 24px;
  }
}

article h4 {
  @apply text-lg font-semibold mt-1 mb-2;
  letter-spacing: -0.015em;
}

article h5 {
  @apply text-base font-semibold mt-1 mb-2;
  letter-spacing: -0.01em;
}

article h6 {
  @apply text-sm font-medium mt-2 mb-1;
  letter-spacing: -0.005em;
}
article p {
  font-weight: 400;
}
article span {
  font-weight: 400;
}
article strong {
  font-weight: 800 !important;
  letter-spacing: 0.01rem;
}

/* Enhanced strong text styling for blog content */
article strong,
article.ck-content strong,
.ck-content strong {
  font-weight: 800 !important;
  letter-spacing: 0.01rem;
  color: inherit;
}

/* Strong text within spans */
article span strong,
article.ck-content span strong,
.ck-content span strong {
  font-weight: 800 !important;
  letter-spacing: 0.01rem;
}

/* Ensure nested strong elements maintain proper weight */
article p strong,
article li strong,
article div strong,
article h1 strong,
article h2 strong,
article h3 strong,
article h4 strong,
article h5 strong,
article h6 strong {
  font-weight: 800 !important;
  letter-spacing: 0.01rem;
}

/* Handle spans inside strong elements with inline styles */
article strong span,
article.ck-content strong span,
.ck-content strong span,
strong span[style*="color"],
strong span[style*="background-color"],
article strong span[style],
article.ck-content strong span[style],
.ck-content strong span[style] {
  font-weight: 800 !important;
  letter-spacing: 0.01rem;
}

/* Maximum specificity for problematic inline styled spans in strong */
article strong span[style*="color: hsl"],
article strong span[style*="background-color: transparent"],
.ck-content strong span[style*="color: hsl"],
.ck-content strong span[style*="background-color: transparent"] {
  font-weight: 800 !important;
  letter-spacing: 0.01rem;
}
/* Only apply margin and centering to images within article content, not banner images */
.ck-content img,
article img {
  @apply my-1 mx-auto rounded-lg;
}

article figure {
  width: 100% !important;
  display: flex;
  justify-content: center;
}

/* Typography size variants */
article h1.huge,
article h1 .text-huge {
  font-size: 56px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

article h2.huge,
article h2 .text-huge {
  font-size: 42px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

article h2.big,
article h2 .text-big {
  font-size: 32px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

article h2.default,
article h2 .text-default {
  font-size: 24px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

article h3.huge,
article h3 .text-huge {
  font-size: 32px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

article h3.big,
article h3 .text-big {
  font-size: 28px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

article h3.default,
article h3 .text-default {
  font-size: 24px;
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

/* Mobile typography size variants */
@media (max-width: 768px) {
  article h1.huge,
  article h1 .text-huge {
    font-size: 32px;
  }

  article h2.huge,
  article h2 .text-huge {
    font-size: 26px;
  }

  article h3.huge,
  article h3 .text-huge {
    font-size: 24px;
  }
}

article p.huge,
article p .text-huge {
  font-size: 23.4px;
  line-height: 1.7;
}

article p.big,
article p .text-big {
  font-size: 18.2px;
  line-height: 1.7;
}

article p.default,
article p .text-default {
  font-size: 13px;
  line-height: 1.7;
}

/* Enhanced line height for main blog content */
article p {
  line-height: 1.8;
  margin-bottom: 0.8em;
  font-family: Inter, Ubuntu, Helvetica, Arial;
}

/* Blog article content styling */
article.blog-content p {
  line-height: 1.8;
  margin-bottom: 1.5em;
  font-family: Inter, Ubuntu, Helvetica, Arial;
}

article.blog-content {
  line-height: 1.7;
}

/* List items in blog content */
article.blog-content ul li,
article.blog-content ol li {
  line-height: 1.7;
  margin-bottom: 0.5em;
  font-family: Inter, Ubuntu, Helvetica, Arial;
}

/* General list items in article */
article ul li,
article ol li {
  font-family: Inter, Ubuntu, Helvetica, Arial;
}

/* Bullet point marker colors for article lists */
article ul,
article ol {
  list-style-position: outside;
  padding-left: 2.5rem; /* reduced indent to decrease gap between marker and text */
  margin-left: 0;
}

article ul {
  list-style-type: disc;
}

article ol {
  list-style-type: decimal;
}

article ul li,
article ol li {
  display: list-item;
  padding-left: 0; /* rely on ul/ol padding for indent */
  margin: 0.4em 0; /* spacing between items */
}

/* Nested lists get additional indent */
article ul ul,
article ol ol,
article ul ol,
article ol ul {
  padding-left: 1.25rem;
}

article ul li::marker {
  color: #914ac4;
  font-size: 1.5em;
  font-weight: bold;
  line-height: 1;
  vertical-align: baseline;
}

article ol li::marker {
  color: #914ac4;
  font-size: 1.5em;
  font-weight: bold;
  line-height: 1;
  vertical-align: baseline;
}

/* Nested list markers */
article ul ul li::marker {
  color: #914ac4;
  font-size: 1.5em;
  font-weight: bold;
  line-height: 1;
  vertical-align: baseline;
}

article ul ul ul li::marker {
  color: #914ac4;
  font-size: 1.5em;
  font-weight: bold;
  line-height: 1;
  vertical-align: baseline;
}

article ol ol li::marker {
  color: #914ac4;
  font-size: 1.5em;
  font-weight: bold;
  line-height: 1;
  vertical-align: baseline;
}

article ol ul li::marker {
  color: #914ac4;
  font-size: 1.5em;
  font-weight: bold;
  line-height: 1;
  vertical-align: baseline;
}

article ul ol li::marker {
  color: #914ac4;
  font-size: 1.5em;
  font-weight: bold;
  line-height: 1;
  vertical-align: baseline;
}

/* Mobile font sizes for text content */
@media (max-width: 768px) {
  article p,
  article.blog-content p {
    font-size: 14px;
  }

  article ul li,
  article ol li,
  article.blog-content ul li,
  article.blog-content ol li {
    font-size: 14px;
  }
}

/* Blockquotes in blog content */
article.blog-content blockquote {
  line-height: 1.6;
  margin: 1.5em 0;
  padding-left: 1em;
  border-left: 4px solid var(--color-primary);
}

/* Blog page titles and headings - FactorA font for title text in blog routes only */
.blog-page h1,
.blog-page h2,
.blog-page h3 {
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

/* Blog single page title - High specificity to override Tailwind */
.blog-single-page h1,
.blog-single-title,
h1.blog-single-title,
.blog-single-page .blog-single-title,
.text-secondary.font-black.blog-single-title,
h1.text-secondary.font-black.blog-single-title {
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif !important;
}

/* Blog card titles */
.blog-card h3,
.blog-card h2,
.blog-card h1 {
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

/* Banner blog item titles */
.banner-blog-item h3,
.banner-blog-item h2,
.banner-blog-item h1 {
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif;
}

/* Override any conflicting styles for blog single title - Maximum specificity */
.blog-single-page div h1.blog-single-title.text-secondary.font-black,
h1[class*="blog-single-title"] {
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif !important;
  font-display: swap !important;
}
.force-font {
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif !important;
}
.blog-single-title {
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif !important;
}
h1.blogTitle {
  font-family: "FactorA", var(--font-space-grotesk), "Space Grotesk",
    "Inter var", ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica,
    Arial, sans-serif !important;
  font-weight: 700 !important;
  font-size: 56px !important;
  color: var(--color-secondary) !important;
  margin-top: 0.5rem !important;
  line-height: 1.2 !important;
}

@media (max-width: 768px) {
  h1.blogTitle {
    font-size: 32px !important;
  }
}

/* Article link styling with primary color and hover effects */
article a,
article.blog-content a,
.ck-content a {
  color: var(--color-primary);
  text-decoration: underline;
  text-decoration-color: var(--color-primary);
  text-underline-offset: 3px;
  text-decoration-thickness: 1px;
  transition: all 0.3s ease;
  font-weight: 500;
}

article a:hover,
article.blog-content a:hover,
.ck-content a:hover {
  color: rgba(165, 104, 216, 1);
  text-decoration-color: rgba(165, 104, 216, 1);
  text-decoration-thickness: 2px;
  filter: brightness(1.2);
}

/* Links within paragraphs */
article p a,
article.blog-content p a,
.ck-content p a {
  color: var(--color-primary);
  text-decoration: underline;
  text-decoration-color: var(--color-primary);
  text-underline-offset: 3px;
  text-decoration-thickness: 1px;
  transition: all 0.3s ease;
  font-weight: 500;
}

article p a:hover,
article.blog-content p a:hover,
.ck-content p a:hover {
  color: rgba(165, 104, 216, 1);
  text-decoration-color: rgba(165, 104, 216, 1);
  text-decoration-thickness: 2px;
  filter: brightness(1.2);
}

/* Links within list items */
article ul li a,
article ol li a,
article.blog-content ul li a,
article.blog-content ol li a,
.ck-content ul li a,
.ck-content ol li a {
  color: var(--color-primary);
  text-decoration: underline;
  text-decoration-color: var(--color-primary);
  text-underline-offset: 3px;
  text-decoration-thickness: 1px;
  transition: all 0.3s ease;
  font-weight: 500;
}

article ul li a:hover,
article ol li a:hover,
article.blog-content ul li a:hover,
article.blog-content ol li a:hover,
.ck-content ul li a:hover,
.ck-content ol li a:hover {
  color: rgba(165, 104, 216, 1);
  text-decoration-color: rgba(165, 104, 216, 1);
  text-decoration-thickness: 2px;
  filter: brightness(1.2);
}

/* Links within headings */
article h1 a,
article h2 a,
article h3 a,
article h4 a,
article h5 a,
article h6 a,
article.blog-content h1 a,
article.blog-content h2 a,
article.blog-content h3 a,
article.blog-content h4 a,
article.blog-content h5 a,
article.blog-content h6 a,
.ck-content h1 a,
.ck-content h2 a,
.ck-content h3 a,
.ck-content h4 a,
.ck-content h5 a,
.ck-content h6 a {
  color: var(--color-primary);
  text-decoration: underline;
  text-decoration-color: var(--color-primary);
  text-underline-offset: 4px;
  text-decoration-thickness: 2px;
  transition: all 0.3s ease;
  font-weight: inherit;
}

article h1 a:hover,
article h2 a:hover,
article h3 a:hover,
article h4 a:hover,
article h5 a:hover,
article h6 a:hover,
article.blog-content h1 a:hover,
article.blog-content h2 a:hover,
article.blog-content h3 a:hover,
article.blog-content h4 a:hover,
article.blog-content h5 a:hover,
article.blog-content h6 a:hover,
.ck-content h1 a:hover,
.ck-content h2 a:hover,
.ck-content h3 a:hover,
.ck-content h4 a:hover,
.ck-content h5 a:hover,
.ck-content h6 a:hover {
  color: rgba(165, 104, 216, 1);
  text-decoration-color: rgba(165, 104, 216, 1);
  text-decoration-thickness: 3px;
  filter: brightness(1.2);
}

/* Links within blockquotes */
article blockquote a,
article.blog-content blockquote a,
.ck-content blockquote a {
  color: var(--color-primary);
  text-decoration: underline;
  text-decoration-color: var(--color-primary);
  text-underline-offset: 3px;
  text-decoration-thickness: 1px;
  transition: all 0.3s ease;
  font-weight: 500;
}

article blockquote a:hover,
article.blog-content blockquote a:hover,
.ck-content blockquote a:hover {
  color: rgba(165, 104, 216, 1);
  text-decoration-color: rgba(165, 104, 216, 1);
  text-decoration-thickness: 2px;
  filter: brightness(1.2);
}

/* Focus states for accessibility */
article a:focus,
article.blog-content a:focus,
.ck-content a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 2px;
}

/* Active state */
article a:active,
article.blog-content a:active,
.ck-content a:active {
  color: rgba(165, 104, 216, 1);
  filter: brightness(1.3);
  transform: translateY(1px);
}
