import React, { memo, useMemo } from "react";
import BoxPrimary from "../BoxPrimary";
import { MonitorIcon, ImageIcon } from "@/ui/icons/general";
import ProgressChart from "@/ui/charts/ProgressChart";
import ScoreMeter from "@/ui/charts/ScoreMeter";
import { formatUrlForDisplay } from "@/utils/urlUtils";

// Define Grade type from ProgressChart
type Grade =
  | "A+"
  | "A"
  | "A-"
  | "B+"
  | "B"
  | "B-"
  | "C+"
  | "C"
  | "C-"
  | "D+"
  | "D"
  | "D-"
  | "F";

// Define TotalScore type
type TotalScore = {
  score?: number;
  grade?: Grade;
};

// Define RecommendationType
type RecommendationType = {
  text: string;
  priority: string;
};

// Define SocialAnalysisType
type SocialAnalysisType = {
  total_score?: TotalScore;
  facebook?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  twitter?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  instagram?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  linkedin?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  youtube?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  telegram?: {
    pass?: boolean;
    channel_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
};

// Define PerformanceAnalysisType
type PerformanceAnalysisType = {
  total_score?: TotalScore;
  javascript_errors?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  compression?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  resource_count?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  [key: string]: unknown;
};

// Define LinksAnalysisType
type LinksAnalysisType = {
  total_score?: TotalScore;
  backlinks?: {
    pass?: boolean;
    total_backlinks?: number;
    unique_domains?: number;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  competitors?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  mentions?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  [key: string]: unknown;
};

// Define PageSpeedAnalysisType
type PageSpeedAnalysisType = {
  "Google's Core Web Vitals"?: {
    is_optimized?: boolean;
    "Largest Contentful Paint (LCP)"?: number;
    "Interaction to Next Paint (INP)"?: number | null;
    "Cumulative Layout Shift (CLS)"?: number;
    description?: string;
    importance?: string;
    recommendation?: RecommendationType;
  };
  performance_desktop?: {
    pass?: boolean;
    performance_score?: number;
    recommendation?: RecommendationType;
    description?: string;
    importance?: string;
    blog?: string;
  };
  [key: string]: unknown;
};

type AuditProps = {
  results: {
    desktop_screenshot_url?: string;
    onpage_analysis?:
      | {
          total_score?: TotalScore;
        }
      | null
      | "in_progress";
    usability_analysis?:
      | {
          total_score?: TotalScore;
        }
      | null
      | "in_progress";
    technology_review_analysis?:
      | {
          total_score?: TotalScore;
        }
      | null
      | "in_progress";
    social_analysis?: SocialAnalysisType | null | "in_progress";
    performance_analysis?:
      | (PerformanceAnalysisType & {
          total_score?: TotalScore;
        })
      | null
      | "in_progress";
    links_analysis?:
      | (LinksAnalysisType & {
          total_score?: TotalScore;
        })
      | null
      | "in_progress";
    localseo_analysis?:
      | {
          total_score?: TotalScore;
        }
      | null
      | "in_progress";
    pagespeed_analysis?: PageSpeedAnalysisType;
  };
  urlName: string;
};

// Simple Screenshot component with no loading logic
const SimpleScreenshot = memo(({ src }: { src: string }) => {
  return (
    <img
      src={src}
      alt="Website Screenshot"
      className="w-full h-full object-cover object-top rounded-2xl"
    />
  );
});

SimpleScreenshot.displayName = "SimpleScreenshot";

// Individual chart components that render independently - removed unnecessary memoization
const OnPageChart = memo(
  ({
    analysis,
    onClick,
  }: {
    analysis: { total_score?: TotalScore } | null | "in_progress";
    onClick: () => void;
  }) => {
    // Extract score and grade directly without memoization to allow immediate updates
    const score =
      analysis && typeof analysis === "object" && analysis !== null
        ? analysis.total_score?.score ?? 0
        : 0;
    const grade =
      analysis && typeof analysis === "object" && analysis !== null
        ? (analysis.total_score?.grade as Grade) ?? "F"
        : "F";

    return (
      <ProgressChart
        value={grade}
        title="On Page SEO"
        progressStates={[
          { label: "Score", value: score, isNoColor: true },
          { label: "Target", value: 100 },
        ]}
        onClick={onClick}
      />
    );
  }
);

const TechSeoChart = memo(
  ({
    analysis,
    onClick,
  }: {
    analysis: { total_score?: TotalScore } | null | "in_progress";
    onClick: () => void;
  }) => {
    // Extract score and grade directly without memoization to allow immediate updates
    const score =
      analysis && typeof analysis === "object" && analysis !== null
        ? analysis.total_score?.score ?? 0
        : 0;
    const grade =
      analysis && typeof analysis === "object" && analysis !== null
        ? (analysis.total_score?.grade as Grade) ?? "F"
        : "F";

    return (
      <ProgressChart
        value={grade}
        title="Technology Review"
        progressStates={[
          { label: "Score", value: score, isNoColor: true },
          { label: "Target", value: 100 },
        ]}
        onClick={onClick}
      />
    );
  }
);

const SocialChart = memo(
  ({
    analysis,
    onClick,
  }: {
    analysis: SocialAnalysisType | null | "in_progress";
    onClick: () => void;
  }) => {
    // Extract score and grade directly without memoization to allow immediate updates
    const score =
      analysis && typeof analysis === "object" && analysis !== null
        ? analysis.total_score?.score ?? 0
        : 0;
    const grade =
      analysis && typeof analysis === "object" && analysis !== null
        ? (analysis.total_score?.grade as Grade) ?? "F"
        : "F";

    return (
      <ProgressChart
        value={grade}
        title="Social Media"
        progressStates={[
          { label: "Score", value: score, isNoColor: true },
          { label: "Target", value: 100 },
        ]}
        onClick={onClick}
      />
    );
  }
);

const UsabilityChart = memo(
  ({
    analysis,
    onClick,
  }: {
    analysis: { total_score?: TotalScore } | null | "in_progress";
    onClick: () => void;
  }) => {
    // Extract score and grade directly without memoization to allow immediate updates
    const score =
      analysis && typeof analysis === "object" && analysis !== null
        ? analysis.total_score?.score ?? 0
        : 0;
    const grade =
      analysis && typeof analysis === "object" && analysis !== null
        ? (analysis.total_score?.grade as Grade) ?? "F"
        : "F";

    return (
      <ProgressChart
        value={grade}
        title="Usability"
        progressStates={[
          { label: "Score", value: score, isNoColor: true },
          { label: "Target", value: 100 },
        ]}
        onClick={onClick}
      />
    );
  }
);

const PerformanceChart = memo(
  ({
    analysis,
    onClick,
  }: {
    analysis:
      | (PerformanceAnalysisType & { total_score?: TotalScore })
      | null
      | "in_progress";
    onClick: () => void;
  }) => {
    // Extract score and grade directly without memoization to allow immediate updates
    const score =
      analysis && typeof analysis === "object" && analysis !== null
        ? analysis.total_score?.score ?? 0
        : 0;
    const grade =
      analysis && typeof analysis === "object" && analysis !== null
        ? (analysis.total_score?.grade as Grade) ?? "F"
        : "F";

    return (
      <ProgressChart
        value={grade}
        title="Performance"
        progressStates={[
          { label: "Score", value: score, isNoColor: true },
          { label: "Target", value: 100 },
        ]}
        onClick={onClick}
      />
    );
  }
);

OnPageChart.displayName = "OnPageChart";
TechSeoChart.displayName = "TechSeoChart";
SocialChart.displayName = "SocialChart";
UsabilityChart.displayName = "UsabilityChart";
PerformanceChart.displayName = "PerformanceChart";

function Audit({ results, urlName }: AuditProps) {
  // Helper function to safely parse JSON strings (same as SideAudit)
  const parseJsonField = (field: any) => {
    if (typeof field === "string") {
      try {
        return JSON.parse(field);
      } catch (e) {
        return field;
      }
    }
    return field;
  };

  // Parse the data fields (same logic as SideAudit)
  const parsedOnpageAnalysis = parseJsonField(results.onpage_analysis);
  const parsedUsabilityAnalysis = parseJsonField(results.usability_analysis);
  const parsedTechnologyAnalysis = parseJsonField(
    results.technology_review_analysis
  );
  const parsedSocialAnalysis = parseJsonField(results.social_analysis);
  const parsedPerformanceAnalysis = parseJsonField(
    results.performance_analysis
  );
  // Note: parsedLinksAnalysis and parsedLocalSeoAnalysis not used in this component

  // Memoize the cleaned URL name
  const cleanUrlName = useMemo(() => formatUrlForDisplay(urlName), [urlName]);

  // Memoize time calculation to prevent unnecessary recalculations on re-renders
  const time = useMemo(() => {
    const today = new Date();
    return {
      day: today.getDate(),
      month: today.toLocaleString("default", { month: "short" }),
      year: today.getFullYear(),
      hours: today.getHours(),
      minutes: today.getMinutes().toString().padStart(2, "0"),
    };
  }, []); // Empty dependency array means this only runs once

  // Calculate scores directly using parsed data for immediate updates
  const onPageScore =
    parsedOnpageAnalysis &&
    typeof parsedOnpageAnalysis === "object" &&
    parsedOnpageAnalysis !== null
      ? parsedOnpageAnalysis.total_score?.score ?? 0
      : 0;

  const usabilityScore =
    parsedUsabilityAnalysis &&
    typeof parsedUsabilityAnalysis === "object" &&
    parsedUsabilityAnalysis !== null
      ? parsedUsabilityAnalysis.total_score?.score ?? 0
      : 0;

  const techSeoScore =
    parsedTechnologyAnalysis &&
    typeof parsedTechnologyAnalysis === "object" &&
    parsedTechnologyAnalysis !== null
      ? parsedTechnologyAnalysis.total_score?.score ?? 0
      : 0;

  const socialScore =
    parsedSocialAnalysis &&
    typeof parsedSocialAnalysis === "object" &&
    parsedSocialAnalysis !== null
      ? parsedSocialAnalysis.total_score?.score ?? 0
      : 0;

  const performanceScore =
    parsedPerformanceAnalysis &&
    typeof parsedPerformanceAnalysis === "object" &&
    parsedPerformanceAnalysis !== null
      ? parsedPerformanceAnalysis.total_score?.score ?? 0
      : 0;

  // Calculate overall score as simple average of all scores (excluding links)
  const scores = [
    onPageScore,
    usabilityScore,
    techSeoScore,
    socialScore,
    performanceScore,
  ];
  const overallScore = Math.round(
    scores.reduce((a, b) => a + b, 0) / scores.length
  );

  // Note: We've removed the unused getGradeFromScore function since it's not needed

  // Get summary based on overall score - commented out as it's not currently used
  // const getSummary = (score: number): string => {
  //   if (score >= 80) return "Your site is performing well";
  //   if (score >= 60) return "Your site needs some improvements";
  //   if (score >= 40) return "Your site needs significant improvements";
  //   return "Your site needs critical attention";
  // };

  // Calculate analysis text directly for immediate updates
  const getAnalysisText = () => {
    const issues = [];

    if (onPageScore < 70) {
      issues.push("on-page SEO elements like meta tags, headings, and content");
    }

    if (usabilityScore < 70) {
      issues.push(
        "usability factors including mobile responsiveness and font legibility"
      );
    }

    if (techSeoScore < 70) {
      issues.push(
        "Technology SEO aspects such as SSL, server configuration, and schema markup"
      );
    }

    if (socialScore < 70) {
      issues.push(
        "social media integration including Open Graph tags, Twitter cards, and social platform connections"
      );
    }

    if (performanceScore < 70) {
      issues.push(
        "performance factors like page speed, resource optimisation, and loading times"
      );
    }

    if (issues.length === 0) {
      return "Your website is performing well across all key SEO areas. Continue monitoring and making minor improvements to maintain your competitive edge.";
    } else if (issues.length === 1) {
      return `Your website needs improvement in ${issues[0]}. Focus on addressing these issues to improve your overall SEO performance.`;
    } else {
      const lastIssue = issues.pop();
      return `Your website needs improvement in ${issues.join(
        ", "
      )} and ${lastIssue}. Addressing these areas will significantly boost your SEO performance.`;
    }
  };

  // Navigation functions for ProgressChart clicks
  const navigateToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <BoxPrimary title={`SEO Audit Overview For  ${cleanUrlName}`}>
      <div className="flex items-center gap-2 py-2  ">
        <MonitorIcon
          strokeColor="#34405480"
          className="w-6 h-6 text-secondary"
        />
        <div className="text-secondary font-semibold">Your Desktop View</div>
      </div>{" "}
      <div className="w-full aspect-video relative rounded-2xl overflow-hidden">
        {results?.desktop_screenshot_url ? (
          <SimpleScreenshot src={results.desktop_screenshot_url} />
        ) : (
          <div className="w-full h-full rounded-2xl bg-gray-200 animate-pulse flex items-center justify-center">
            <ImageIcon className="w-16 h-16 text-gray-400" />
          </div>
        )}
      </div>
      <div className="mt-6">
        <div className="text-sm lg:text-base font-bold text-gray-700">
          Report Generated at {time.day} {time.month} {time.year} - {time.hours}
          :{time.minutes}
        </div>
      </div>
      <div className="w-full bg-secondary/5 text-sm text-secondary/80 mt-6 p-4 lg:p-6 rounded-lg">
        <p>{getAnalysisText()}</p>
        <div className="my-1">
          Find More by checking out the{" "}
          <a
            href="#Recommendations"
            className="mx-1 font-bold text-purple-800 "
          >
            Recommendations
          </a>
        </div>
      </div>
      <div className="mt-4 lg:mt-6 flex flex-col items-center justify-center lg:flex-row lg:items-end gap-8 mb-5.5">
        <ScoreMeter
          score={overallScore}
          title="Overall Performance"
          size="lg"
        />
      </div>
      <div className="mt-4 lg:mt-6 flex flex-col items-center justify-center lg:flex-row lg:items-end gap-8 mb-5.5">
        <OnPageChart
          analysis={parsedOnpageAnalysis ?? null}
          onClick={() => navigateToSection("onPageSEO")}
        />
        <TechSeoChart
          analysis={parsedTechnologyAnalysis ?? null}
          onClick={() => navigateToSection("technology")}
        />
        <SocialChart
          analysis={parsedSocialAnalysis ?? null}
          onClick={() => navigateToSection("social")}
        />
      </div>
      <div className="mt-4 lg:mt-6 flex flex-col items-center justify-center lg:flex-row lg:items-center lg:justify-center gap-8 mb-5.5">
        <UsabilityChart
          analysis={parsedUsabilityAnalysis ?? null}
          onClick={() => navigateToSection("usability")}
        />
        <PerformanceChart
          analysis={parsedPerformanceAnalysis ?? null}
          onClick={() => navigateToSection("performance")}
        />
      </div>
    </BoxPrimary>
  );
}

// Export the memoized component to prevent unnecessary re-renders
const MemoizedAudit = memo(Audit);
MemoizedAudit.displayName = "Audit";
export default MemoizedAudit;
