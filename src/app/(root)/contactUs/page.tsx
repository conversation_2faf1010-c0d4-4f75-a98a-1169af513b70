import { Metadata } from "next";
import ContactDetails from "./_components/ContactDetails";
import ContactBanner from "./_components/ContactBanner";

export const metadata: Metadata = {
  title: "Contact Us | SEO Analyser - Get Expert SEO Support",
  description:
    "Contact SEO Analyser for expert support, custom plans, and enterprise solutions. Get help with SEO audits, white-label reports, and website optimization.",
  keywords: [
    "contact SEO Analyser",
    "SEO support",
    "SEO consultation",
    "custom SEO plans",
    "enterprise SEO",
    "SEO help",
  ],
  openGraph: {
    title: "Contact Us | SEO Analyser - Get Expert SEO Support",
    description:
      "Contact SEO Analyser for expert support, custom plans, and enterprise solutions. Get help with SEO audits, white-label reports, and website optimization.",
    url: "https://seoanalyser.com.au/contactUs",
  },
  alternates: {
    canonical: "https://seoanalyser.com.au/contactUs",
  },
};

export default function ContactUs() {
  return (
    <div className="w-full mb-12 pb-12">
      <ContactBanner />
      <div className="container">
        <ContactDetails />
      </div>
    </div>
  );
}
