"use client";
import React from "react";
import { OnPageAnalysis } from "@/types/seoAnalyzerTypes";
import {
  SectionHeader,
  SectionScoreBox,
  DataRow,
  RecommendationCard,
} from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";
import { formatUrlForDisplay } from "@/utils/urlUtils";

export interface OnPageSeoSectionProps {
  onPageSeoData: OnPageAnalysis;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const OnPageSeoSection: React.FC<OnPageSeoSectionProps> = ({
  onPageSeoData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "SEO ANALYSER"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoOnPage")}
        onLogoError={() => onImageError?.("sectionLogoOnPage")}
        sectionId="onpage-details"
      />

      <SectionHeader
        title="On-Page SEO Audit"
        brandName={brand_name}
        brandWebsite={brand_website}
      />

      {/* Section Score Box */}
      {onPageSeoData.total_score && (
        <SectionScoreBox
          scoreGrade={onPageSeoData.total_score}
          title="On-Page SEO Score"
          description={
            onPageSeoData.overall_description ||
            "This score reflects how well your page is optimized for search engines, including title tags, meta descriptions, content structure, and keyword usage."
          }
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Title Tag Analysis */}
        {onPageSeoData.title_tag && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Title Tag Audit
              </h3>
            </div>

            <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-50/50 rounded-xl mb-4 border border-gray-100">
              <p className="text-sm font-medium text-gray-800 break-words leading-relaxed">
                "{onPageSeoData.title_tag.title || "No title found"}"
              </p>
            </div>

            <div className="space-y-1 bg-gray-50/50 rounded-lg p-3">
              <DataRow
                label="Length"
                value={`${onPageSeoData.title_tag.length || 0} characters`}
              />
              <DataRow
                label="Optimal Length"
                value={
                  onPageSeoData.title_tag.is_optimal_length ? "✓ Yes" : "✗ No"
                }
                important={!onPageSeoData.title_tag.is_optimal_length}
              />

              {onPageSeoData.title_tag.description && (
                <DataRow
                  label="Status"
                  value={onPageSeoData.title_tag.description}
                />
              )}
            </div>

            {/* Title Tag Recommendation */}
            {onPageSeoData.title_tag.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.title_tag.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Meta Description Analysis */}
        {onPageSeoData.meta_description && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Meta Description Audit
              </h3>
            </div>

            <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-50/50 rounded-xl mb-4 border border-gray-100">
              <p className="text-sm font-medium text-gray-800 break-words leading-relaxed">
                "
                {onPageSeoData.meta_description.content ||
                  "No meta description found"}
                "
              </p>
            </div>

            <div className="space-y-1 bg-gray-50/50 rounded-lg p-3">
              <DataRow
                label="Length"
                value={`${
                  onPageSeoData.meta_description.length || 0
                } characters`}
              />
              <DataRow
                label="Optimal Length"
                value={
                  onPageSeoData.meta_description.is_optimal_length
                    ? "✓ Yes"
                    : "✗ No"
                }
              />
            </div>

            {/* Meta Description Recommendation */}
            {onPageSeoData.meta_description.recommendation && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <RecommendationCard
                  recommendation={onPageSeoData.meta_description.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Page break before Search Engine Preview and Content Audit sections */}
      <div className=" mt-8 ">
        {/* SERP Preview */}
        {onPageSeoData.serp_preview && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Search Engine Preview
              </h3>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
              <h4 className="text-blue-600 text-lg font-medium mb-1 truncate">
                {onPageSeoData.serp_preview.title ||
                  onPageSeoData.title_tag?.title ||
                  "Page Title"}
              </h4>
              <p className="text-green-700 text-sm mb-2 truncate">
                {
                  onPageSeoData.serp_preview.url
                    .replace(/^https?:\/\//, "") // Remove http:// or https://
                    .replace(/^www\./, "") // Remove www.
                }
              </p>
              <p className="text-gray-600 text-sm line-clamp-3">
                {onPageSeoData.serp_preview.caption ||
                  onPageSeoData.meta_description?.content ||
                  "No description available"}
              </p>
            </div>
            <div className="mt-3 space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Preview Status"
                value={
                  onPageSeoData.serp_preview.pass
                    ? "✓ Good"
                    : "⚠ Needs Improvement"
                }
                important={!onPageSeoData.serp_preview.pass}
              />
            </div>

            {/* SERP Preview Recommendation */}
            {onPageSeoData.serp_preview.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.serp_preview.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Content Analysis */}
        {onPageSeoData.content_amount && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200 mt-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Content Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Word Count"
                value={`${onPageSeoData.content_amount.word_count || 0} words`}
              />
              <DataRow
                label="Text/HTML Ratio"
                value={`${
                  onPageSeoData.content_amount.text_html_ratio_percent || 0
                }%`}
              />
              <DataRow
                label="Content Status"
                value={
                  onPageSeoData.content_amount.pass
                    ? "✓ Sufficient"
                    : "⚠ Insufficient"
                }
              />
            </div>
            {onPageSeoData.content_amount.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {onPageSeoData.content_amount.description}
                </p>
              </div>
            )}

            {/* Content Analysis Recommendation */}
            {onPageSeoData.content_amount.recommendation && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <RecommendationCard
                  recommendation={onPageSeoData.content_amount.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"></div>

      {/* Headers Analysis - synchronized with main audit component */}
      {onPageSeoData.headers && (
        <div className="mt-6">
          <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
            Headers Structure Audit
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Header Distribution Chart - using correct data structure */}
            <div className="p-3 md:p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
              <div className="flex items-center gap-2 md:gap-3 mb-2 md:mb-4">
                <div className="w-1 md:w-1.5 h-4 md:h-6 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                <h4 className="font-bold text-gray-800 text-sm md:text-base">
                  Header Distribution
                </h4>
              </div>
              <div className="flex items-end justify-around h-20 md:h-32 mb-2 md:mb-4">
                {/* H1 Bar */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-blue-500 w-4 md:w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.h1?.count || 0) * 20,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-1 md:mt-2 font-medium">H1</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.h1?.count || 0}
                  </span>
                </div>
                {/* H2 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-green-500 w-4 md:w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h2?.count || 0) *
                          5,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-1 md:mt-2 font-medium">H2</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h2?.count || 0}
                  </span>
                </div>
                {/* H3 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-yellow-500 w-4 md:w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h3?.count || 0) *
                          3,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-1 md:mt-2 font-medium">H3</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h3?.count || 0}
                  </span>
                </div>
                {/* H4 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-orange-500 w-4 md:w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h4?.count || 0) *
                          2,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-1 md:mt-2 font-medium">H4</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h4?.count || 0}
                  </span>
                </div>
                {/* H5 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-red-500 w-4 md:w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h5?.count || 0) *
                          2,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-1 md:mt-2 font-medium">H5</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h5?.count || 0}
                  </span>
                </div>
                {/* H6 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-purple-500 w-4 md:w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h6?.count || 0) *
                          2,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-1 md:mt-2 font-medium">H6</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h6?.count || 0}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <DataRow
                  label="H1 Status"
                  value={
                    onPageSeoData.headers.h1?.pass ? "✓ Good" : "⚠ Issues Found"
                  }
                />
                <DataRow
                  label="Total Headers"
                  value={`${
                    (onPageSeoData.headers.h1?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h2?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h3?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h4?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h5?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h6?.count || 0)
                  }`}
                />
              </div>
            </div>

            {/* Header Details - synchronized with main audit component */}
            <div className="p-3 md:p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
              <div className="flex items-center gap-2 md:gap-3 mb-2 md:mb-4">
                <div className="w-1 md:w-1.5 h-4 md:h-6 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                <h4 className="font-bold text-gray-800 text-sm md:text-base">
                  Header Content
                </h4>
              </div>
              <div className="space-y-2 md:space-y-4">
                {/* Removed scrollbar - max-h-80 overflow-y-auto */}
                {/* H1 Content */}
                {onPageSeoData.headers.h1?.content &&
                  onPageSeoData.headers.h1.content.length > 0 && (
                    <div>
                      <h5 className="text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-2">
                        H1 Headers:
                      </h5>
                      <ul className="list-disc pl-3 md:pl-5 space-y-0.5 md:space-y-1">
                        {onPageSeoData.headers.h1.content.map((item, index) => (
                          <li
                            key={index}
                            className="text-xs md:text-sm text-gray-600 leading-tight"
                          >
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                {/* H2 Content */}
                {onPageSeoData.headers.other_headers?.h2?.content &&
                  onPageSeoData.headers.other_headers.h2.content.length > 0 && (
                    <div>
                      <h5 className="text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-2">
                        H2 Headers:
                      </h5>
                      <ul className="list-disc pl-3 md:pl-5 space-y-0.5 md:space-y-1">
                        {onPageSeoData.headers.other_headers.h2.content
                          .slice(0, 5)
                          .map((item, index) => (
                            <li
                              key={index}
                              className="text-xs md:text-sm text-gray-600 leading-tight"
                            >
                              {item}
                            </li>
                          ))}
                        {onPageSeoData.headers.other_headers.h2.content.length >
                          5 && (
                          <li className="text-xs text-gray-500">
                            ... and{" "}
                            {onPageSeoData.headers.other_headers.h2.content
                              .length - 5}{" "}
                            more
                          </li>
                        )}
                      </ul>
                    </div>
                  )}

                {/* H3 Content */}
                {onPageSeoData.headers.other_headers?.h3?.content &&
                  onPageSeoData.headers.other_headers.h3.content.length > 0 && (
                    <div>
                      <h5 className="text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-2">
                        H3 Headers:
                      </h5>
                      <ul className="list-disc pl-3 md:pl-5 space-y-0.5 md:space-y-1">
                        {onPageSeoData.headers.other_headers.h3.content
                          .slice(0, 3)
                          .map((item, index) => (
                            <li
                              key={index}
                              className="text-xs md:text-sm text-gray-600 leading-tight"
                            >
                              {item}
                            </li>
                          ))}
                        {onPageSeoData.headers.other_headers.h3.content.length >
                          3 && (
                          <li className="text-xs text-gray-500">
                            ... and{" "}
                            {onPageSeoData.headers.other_headers.h3.content
                              .length - 3}{" "}
                            more
                          </li>
                        )}
                      </ul>
                    </div>
                  )}
              </div>

              <div className="mt-4 pt-3 border-t space-y-2">
                <DataRow
                  label="Hierarchy Status"
                  value={
                    onPageSeoData.headers.pass
                      ? "✓ Good"
                      : "⚠ Needs Improvement"
                  }
                />
              </div>

              {/* Headers Recommendation */}
              {onPageSeoData.headers.hierarchy_recommendation && (
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <RecommendationCard
                    recommendation={
                      onPageSeoData.headers.hierarchy_recommendation
                    }
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Keyword Consistency Analysis - matching main component structure */}
      {onPageSeoData.keyword_consistency && (
        <div className="mt-6">
          <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
            Keyword Consistency Audit
          </h3>

          {/* Summary Section */}
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200 mb-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h4 className="font-bold text-gray-800 text-xl print:text-lg">
                Keyword Consistency Summary
              </h4>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Total Keywords"
                value={onPageSeoData.keyword_consistency.keywords?.length || 0}
              />
              <DataRow
                label="Total Phrases"
                value={onPageSeoData.keyword_consistency.phrases?.length || 0}
              />
              <DataRow
                label="Status"
                value={
                  onPageSeoData.keyword_consistency.pass
                    ? "✓ Good"
                    : "⚠ Needs Improvement"
                }
                important={!onPageSeoData.keyword_consistency.pass}
              />
            </div>
            {onPageSeoData.keyword_consistency.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {onPageSeoData.keyword_consistency.description}
                </p>
              </div>
            )}

            {/* Keyword Consistency Recommendation */}
            {onPageSeoData.keyword_consistency.recommendation && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <RecommendationCard
                  recommendation={
                    onPageSeoData.keyword_consistency.recommendation
                  }
                />
              </div>
            )}
          </div>

          {/* Individual Keywords Table */}
          {onPageSeoData.keyword_consistency.keywords &&
            onPageSeoData.keyword_consistency.keywords.length > 0 && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200 mb-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-1.5 h-6 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                  <h4 className="font-bold text-gray-800 text-base">
                    Individual Keywords
                  </h4>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="p-2 text-left font-medium text-gray-700">
                          Keyword
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Title
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Meta Description
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Headings
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Page Frequency
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {onPageSeoData.keyword_consistency.keywords
                        .slice(0, 15)
                        .map((keyword, index) => (
                          <tr key={index} className="border-b border-gray-100">
                            <td className="p-2 font-medium text-gray-800">
                              {keyword.keyword}
                            </td>
                            <td className="p-2 text-center">
                              {keyword.in_title ? (
                                <span className="text-green-600 font-bold">
                                  ✓
                                </span>
                              ) : (
                                <span className="text-red-600 font-bold">
                                  ✗
                                </span>
                              )}
                            </td>
                            <td className="p-2 text-center">
                              {keyword.in_meta_description ? (
                                <span className="text-green-600 font-bold">
                                  ✓
                                </span>
                              ) : (
                                <span className="text-red-600 font-bold">
                                  ✗
                                </span>
                              )}
                            </td>
                            <td className="p-2 text-center">
                              {keyword.in_headings ? (
                                <span className="text-green-600 font-bold">
                                  ✓
                                </span>
                              ) : (
                                <span className="text-red-600 font-bold">
                                  ✗
                                </span>
                              )}
                            </td>
                            <td className="p-2 text-center font-medium">
                              {keyword.frequency}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                  {onPageSeoData.keyword_consistency.keywords.length > 15 && (
                    <p className="text-xs text-gray-500 mt-2 text-center">
                      Showing top 15 of{" "}
                      {onPageSeoData.keyword_consistency.keywords.length}{" "}
                      keywords
                    </p>
                  )}
                </div>
              </div>
            )}

          {/* Phrases Table */}
          {onPageSeoData.keyword_consistency.phrases &&
            onPageSeoData.keyword_consistency.phrases.length > 0 && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-1.5 h-6 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                  <h4 className="font-bold text-gray-800 text-base">Phrases</h4>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="p-2 text-left font-medium text-gray-700">
                          Phrase
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Title
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Meta Description
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Headings
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Page Frequency
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {onPageSeoData.keyword_consistency.phrases
                        .slice(0, 15)
                        .map((phrase, index) => (
                          <tr key={index} className="border-b border-gray-100">
                            <td className="p-2 font-medium text-gray-800">
                              {phrase.phrase}
                            </td>
                            <td className="p-2 text-center">
                              {phrase.in_title ? (
                                <span className="text-green-600 font-bold">
                                  ✓
                                </span>
                              ) : (
                                <span className="text-red-600 font-bold">
                                  ✗
                                </span>
                              )}
                            </td>
                            <td className="p-2 text-center">
                              {phrase.in_meta_description ? (
                                <span className="text-green-600 font-bold">
                                  ✓
                                </span>
                              ) : (
                                <span className="text-red-600 font-bold">
                                  ✗
                                </span>
                              )}
                            </td>
                            <td className="p-2 text-center">
                              {phrase.in_headings ? (
                                <span className="text-green-600 font-bold">
                                  ✓
                                </span>
                              ) : (
                                <span className="text-red-600 font-bold">
                                  ✗
                                </span>
                              )}
                            </td>
                            <td className="p-2 text-center font-medium">
                              {phrase.frequency}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                  {onPageSeoData.keyword_consistency.phrases.length > 15 && (
                    <p className="text-xs text-gray-500 mt-2 text-center">
                      Showing top 15 of{" "}
                      {onPageSeoData.keyword_consistency.phrases.length} phrases
                    </p>
                  )}
                </div>
              </div>
            )}

          {/* No data message */}
          {(!onPageSeoData.keyword_consistency.keywords ||
            onPageSeoData.keyword_consistency.keywords.length === 0) &&
            (!onPageSeoData.keyword_consistency.phrases ||
              onPageSeoData.keyword_consistency.phrases.length === 0) && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <p className="text-gray-500 text-sm text-center">
                  No keyword or phrase data available
                </p>
              </div>
            )}
        </div>
      )}

      {/* Additional SEO Elements */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Schema Markup Analysis */}
        {onPageSeoData.schema_markup && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Schema Markup
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Status"
                value={
                  onPageSeoData.schema_markup.pass
                    ? "✓ Detected"
                    : "✗ Not Found"
                }
                important={!onPageSeoData.schema_markup.pass}
              />
              {onPageSeoData.schema_markup.formats_found &&
                onPageSeoData.schema_markup.formats_found.length > 0 && (
                  <DataRow
                    label="Formats Found"
                    value={onPageSeoData.schema_markup.formats_found.join(", ")}
                  />
                )}
              {onPageSeoData.schema_markup.common_types &&
                onPageSeoData.schema_markup.common_types.length > 0 && (
                  <DataRow
                    label="Schema Types"
                    value={onPageSeoData.schema_markup.common_types.join(", ")}
                  />
                )}
            </div>
            {onPageSeoData.schema_markup.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {onPageSeoData.schema_markup.description}
                </p>
              </div>
            )}
            {onPageSeoData.schema_markup.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.schema_markup.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Language Analysis */}
        {onPageSeoData.language && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Language Declaration
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Declared Language"
                value={onPageSeoData.language.declared || "Not specified"}
              />
              <DataRow
                label="Status"
                value={
                  onPageSeoData.language.description || "Language analysis"
                }
              />
            </div>
            {onPageSeoData.language.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.language.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Image Alt Attributes Analysis - Full Width */}
      {onPageSeoData.image_alt_attributes && (
        <div className="mt-8 p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
            <h3 className="font-bold text-gray-800 text-xl print:text-lg">
              Image Alt Attributes
            </h3>
          </div>

          {/* Summary Statistics */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm font-semibold text-gray-700">
                Total Images
              </p>
              <p className="text-lg font-bold text-primary">
                {onPageSeoData.image_alt_attributes.total_images || 0}
              </p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm font-semibold text-gray-700">
                With Alt Text
              </p>
              <p className="text-lg font-bold text-green-600">
                {onPageSeoData.image_alt_attributes.images_with_alt || 0}
              </p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm font-semibold text-gray-700">
                Missing Alt Text
              </p>
              <p className="text-lg font-bold text-red-600">
                {onPageSeoData.image_alt_attributes.images_without_alt || 0}
              </p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm font-semibold text-gray-700">
                Missing Percentage
              </p>
              <p className="text-lg font-bold text-red-600">
                {onPageSeoData.image_alt_attributes.percent_missing || 0}%
              </p>
            </div>
          </div>

          {/* Images Missing Alt Text Table - Two Columns */}
          {onPageSeoData.image_alt_attributes.missing_alt_images_sample &&
            onPageSeoData.image_alt_attributes.missing_alt_images_sample
              .length > 0 && (
              <div className="mt-6">
                <h4 className="font-semibold text-gray-800 mb-3 pb-2 border-b border-gray-200">
                  Images Missing Alt Text:
                </h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {Array.isArray(
                    onPageSeoData.image_alt_attributes.missing_alt_images_sample
                  )
                    ? onPageSeoData.image_alt_attributes.missing_alt_images_sample
                        .slice(0, 20)
                        .map((img: any, index: number) => (
                          <div
                            key={index}
                            className="p-4 bg-white rounded-lg border border-gray-200 "
                          >
                            {typeof img === "string" ? (
                              <div className="flex items-start gap-3">
                                <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                <div className="flex-1">
                                  <p className="text-sm text-gray-800 break-all">
                                    <span className="font-semibold text-gray-900">
                                      Source:
                                    </span>{" "}
                                    {img}
                                  </p>
                                </div>
                              </div>
                            ) : (
                              <div className="space-y-3">
                                {/* Header with status indicator */}
                                <div className="flex items-start gap-3">
                                  <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                  <div className="flex-1">
                                    <p className="text-sm text-gray-900 break-all">
                                      <span className="font-semibold">
                                        Image Source:
                                      </span>{" "}
                                      <span className="text-gray-700">
                                        {img.src || "Empty/No source"}
                                      </span>
                                    </p>
                                  </div>
                                </div>

                                {/* Alt text status */}
                                <div className="flex items-start gap-3">
                                  <div className="flex-shrink-0 w-6"></div>
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 flex-wrap">
                                      <span className="text-sm font-semibold text-gray-900">
                                        Alt Text Status:
                                      </span>
                                      <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        {img.element?.match(
                                          /alt="([^"]*)"/
                                        )?.[1] !== undefined
                                          ? img.element.match(
                                              /alt="([^"]*)"/
                                            )[1] === ""
                                            ? "Empty alt attribute"
                                            : img.element.match(
                                                /alt="([^"]*)"/
                                              )[1]
                                          : "No alt attribute"}
                                      </span>
                                    </div>
                                  </div>
                                </div>

                                {/* HTML Element */}
                                <div className="flex items-start gap-3">
                                  <div className="flex-shrink-0 w-6"></div>
                                  <div className="flex-1">
                                    <div className="bg-gray-50 p-3 rounded-md border">
                                      <p className="text-xs font-semibold text-gray-900 mb-2">
                                        HTML Element:
                                      </p>
                                      <code className="text-xs text-gray-700 break-all leading-relaxed block">
                                        {img.element}
                                      </code>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        ))
                    : null}
                </div>
                {Array.isArray(
                  onPageSeoData.image_alt_attributes.missing_alt_images_sample
                ) &&
                  onPageSeoData.image_alt_attributes.missing_alt_images_sample
                    .length > 20 && (
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-center gap-3">
                        <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full"></div>
                        <p className="text-sm text-blue-700 font-medium">
                          And{" "}
                          {onPageSeoData.image_alt_attributes
                            .missing_alt_images_sample.length - 20}{" "}
                          more images missing alt text...
                        </p>
                      </div>
                    </div>
                  )}
              </div>
            )}

          {onPageSeoData.image_alt_attributes.recommendation && (
            <div className="mt-6 pt-4 border-t border-gray-200">
              <RecommendationCard
                recommendation={
                  onPageSeoData.image_alt_attributes.recommendation
                }
              />
            </div>
          )}
        </div>
      )}

      {/* Technical SEO Elements */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Canonical Tag Analysis */}
        {onPageSeoData.canonical_tag && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Canonical Tag
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Status"
                value={
                  onPageSeoData.canonical_tag.pass ? "✓ Present" : "✗ Missing"
                }
                important={!onPageSeoData.canonical_tag.pass}
              />
              {onPageSeoData.canonical_tag.canonical_url && (
                <DataRow
                  label="Canonical URL"
                  value={onPageSeoData.canonical_tag.canonical_url}
                />
              )}
            </div>
            {onPageSeoData.canonical_tag.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.canonical_tag.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* SSL Analysis */}
        {onPageSeoData.ssl_enabled && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                SSL Security
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="SSL Status"
                value={
                  onPageSeoData.ssl_enabled.pass ? "✓ Enabled" : "✗ Disabled"
                }
                important={!onPageSeoData.ssl_enabled.pass}
              />
              <DataRow
                label="Description"
                value={onPageSeoData.ssl_enabled.description || "SSL analysis"}
              />
            </div>
            {onPageSeoData.ssl_enabled.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.ssl_enabled.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Indexing and Redirects */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Noindex Tag Analysis */}
        {onPageSeoData.noindex_tag && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Noindex Tag Test
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Noindex Status"
                value={
                  !onPageSeoData.noindex_tag.pass
                    ? "✓ Not Present (Good)"
                    : "⚠ Present"
                }
                important={onPageSeoData.noindex_tag.pass}
              />
              <DataRow
                label="Description"
                value={
                  onPageSeoData.noindex_tag.description ||
                  "Noindex tag analysis"
                }
              />
            </div>
            {onPageSeoData.noindex_tag.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.noindex_tag.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Noindex Header Analysis */}
        {onPageSeoData.noindex_header && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Noindex Header Test
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Noindex Header Status"
                value={
                  !onPageSeoData.noindex_header.pass
                    ? "✓ Not Present (Good)"
                    : "⚠ Present"
                }
                important={onPageSeoData.noindex_header.pass}
              />
              <DataRow
                label="Description"
                value={
                  onPageSeoData.noindex_header.description ||
                  "Noindex header analysis"
                }
              />
            </div>
            {onPageSeoData.noindex_header.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.noindex_header.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* HTTPS Redirect Analysis */}
        {onPageSeoData.https_redirect && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                HTTPS Redirect
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Redirect Status"
                value={
                  onPageSeoData.https_redirect.pass ? "✓ Enabled" : "✗ Disabled"
                }
                important={!onPageSeoData.https_redirect.pass}
              />
              <DataRow
                label="Description"
                value={
                  onPageSeoData.https_redirect.description ||
                  "HTTPS redirect analysis"
                }
              />
            </div>
            {onPageSeoData.https_redirect.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.https_redirect.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Analytics and Technical Setup */}
      {(onPageSeoData.robots_txt ||
        onPageSeoData.analytics ||
        onPageSeoData.xml_sitemap) && (
        <div className="mt-8">
          <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
            Technical SEO Setup
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Robots.txt Analysis */}
            {onPageSeoData.robots_txt && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                  <h4 className="font-bold text-gray-800 text-lg">
                    Robots.txt
                  </h4>
                </div>
                <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                  <DataRow
                    label="Status"
                    value={
                      onPageSeoData.robots_txt.pass ? "✓ Present" : "✗ Missing"
                    }
                    important={!onPageSeoData.robots_txt.pass}
                  />
                </div>
                {onPageSeoData.robots_txt.recommendation && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <RecommendationCard
                      recommendation={onPageSeoData.robots_txt.recommendation}
                    />
                  </div>
                )}
              </div>
            )}

            {/* Analytics Analysis */}
            {onPageSeoData.analytics && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                  <h4 className="font-bold text-gray-800 text-lg">Analytics</h4>
                </div>
                <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                  <DataRow
                    label="Status"
                    value={
                      onPageSeoData.analytics.pass
                        ? "✓ Detected"
                        : "✗ Not Found"
                    }
                    important={!onPageSeoData.analytics.pass}
                  />
                  {onPageSeoData.analytics.detected_tools &&
                    onPageSeoData.analytics.detected_tools.length > 0 && (
                      <DataRow
                        label="Tools Detected"
                        value={onPageSeoData.analytics.detected_tools.join(
                          ", "
                        )}
                      />
                    )}
                </div>
                {onPageSeoData.analytics.recommendation && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <RecommendationCard
                      recommendation={onPageSeoData.analytics.recommendation}
                    />
                  </div>
                )}
              </div>
            )}

            {/* XML Sitemap Analysis */}
            {onPageSeoData.xml_sitemap && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                  <h4 className="font-bold text-gray-800 text-lg">
                    XML Sitemap
                  </h4>
                </div>
                <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                  <DataRow
                    label="Status"
                    value={
                      onPageSeoData.xml_sitemap.pass ? "✓ Found" : "✗ Not Found"
                    }
                    important={!onPageSeoData.xml_sitemap.pass}
                  />
                  {onPageSeoData.xml_sitemap.sitemap_urls_found &&
                    onPageSeoData.xml_sitemap.sitemap_urls_found.length > 0 && (
                      <DataRow
                        label="URLs Found"
                        value={`${onPageSeoData.xml_sitemap.sitemap_urls_found.length} URLs`}
                      />
                    )}
                </div>
                {onPageSeoData.xml_sitemap.recommendation && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <RecommendationCard
                      recommendation={onPageSeoData.xml_sitemap.recommendation}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Comprehensive On-Page SEO Recommendations */}
      <div className="mt-8 p-6 border border-gray-200/80 rounded-xl bg-white pdf-section-box print-section ">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
          <h3 className="text-xl font-bold text-gray-800 print:text-lg ">
            All On-Page SEO Recommendations
          </h3>
        </div>
        <div className="grid grid-cols-1 gap-4 recommendations-grid pdf-recommendation-grid">
          {/* Title Tag Recommendations */}
          {onPageSeoData.title_tag?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.title_tag.recommendation}
            />
          )}

          {/* Meta Description Recommendations */}
          {onPageSeoData.meta_description?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.meta_description.recommendation}
            />
          )}

          {/* SERP Preview Recommendations */}
          {onPageSeoData.serp_preview?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.serp_preview.recommendation}
            />
          )}

          {/* Content Amount Recommendations */}
          {onPageSeoData.content_amount?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.content_amount.recommendation}
            />
          )}

          {/* Headers Recommendations */}
          {onPageSeoData.headers?.hierarchy_recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.headers.hierarchy_recommendation}
            />
          )}

          {/* H1 Specific Recommendations */}
          {onPageSeoData.headers?.h1?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.headers.h1.recommendation}
            />
          )}

          {/* Keyword Consistency Recommendations */}
          {onPageSeoData.keyword_consistency?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.keyword_consistency.recommendation}
            />
          )}

          {/* Schema Markup Recommendations */}
          {onPageSeoData.schema_markup?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.schema_markup.recommendation}
            />
          )}

          {/* Language Recommendations */}
          {onPageSeoData.language?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.language.recommendation}
            />
          )}

          {/* Images Recommendations */}
          {onPageSeoData.image_alt_attributes?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.image_alt_attributes.recommendation}
            />
          )}

          {/* Language Recommendations */}
          {onPageSeoData.language?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.language.recommendation}
            />
          )}

          {/* Canonical Tag Recommendations */}
          {onPageSeoData.canonical_tag?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.canonical_tag.recommendation}
            />
          )}

          {/* SSL Recommendations */}
          {onPageSeoData.ssl_enabled?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.ssl_enabled.recommendation}
            />
          )}

          {/* Noindex Tag Recommendations */}
          {onPageSeoData.noindex_tag?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.noindex_tag.recommendation}
            />
          )}

          {/* Noindex Header Recommendations */}
          {onPageSeoData.noindex_header?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.noindex_header.recommendation}
            />
          )}

          {/* HTTPS Redirect Recommendations */}
          {onPageSeoData.https_redirect?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.https_redirect.recommendation}
            />
          )}

          {/* Robots.txt Recommendations */}
          {onPageSeoData.robots_txt?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.robots_txt.recommendation}
            />
          )}

          {/* Analytics Recommendations */}
          {onPageSeoData.analytics?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.analytics.recommendation}
            />
          )}

          {/* XML Sitemap Recommendations */}
          {onPageSeoData.xml_sitemap?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.xml_sitemap.recommendation}
            />
          )}

          {/* If no recommendations found */}
          {!onPageSeoData.title_tag?.recommendation &&
            !onPageSeoData.meta_description?.recommendation &&
            !onPageSeoData.serp_preview?.recommendation &&
            !onPageSeoData.content_amount?.recommendation &&
            !onPageSeoData.headers?.hierarchy_recommendation &&
            !onPageSeoData.headers?.h1?.recommendation &&
            !onPageSeoData.keyword_consistency?.recommendation &&
            !onPageSeoData.schema_markup?.recommendation &&
            !onPageSeoData.language?.recommendation &&
            !onPageSeoData.image_alt_attributes?.recommendation &&
            !onPageSeoData.canonical_tag?.recommendation &&
            !onPageSeoData.ssl_enabled?.recommendation &&
            !onPageSeoData.noindex_tag?.recommendation &&
            !onPageSeoData.noindex_header?.recommendation &&
            !onPageSeoData.https_redirect?.recommendation &&
            !onPageSeoData.robots_txt?.recommendation &&
            !onPageSeoData.analytics?.recommendation &&
            !onPageSeoData.xml_sitemap?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: "Your on-page SEO appears to be well optimized. Continue monitoring and updating content regularly to maintain good search engine visibility.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
