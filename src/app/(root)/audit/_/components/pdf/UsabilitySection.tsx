"use client";
import React, { useState } from "react";
import {
  UsabilityAnalysis,
  PageSpeedAnalysis,
  PageSpeedMobileAnalysis,
} from "@/types/seoAnalyzerTypes";
import {
  SectionHeader,
  SectionScoreBox,
  DataRow,
  RecommendationCard,
} from "./BaseComponents";
import ProgressChart from "@/ui/charts/ProgressChart";
import { SectionWatermark } from "./WatermarkComponents";

export interface UsabilitySectionProps {
  usabilityData: UsabilityAnalysis;
  pagespeedData?: PageSpeedAnalysis;
  pagespeedMobileData?: PageSpeedMobileAnalysis;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

// Mobile Screenshot Component for PDF
const MobileScreenshot: React.FC<{ src: string; alt: string }> = ({
  src,
  alt,
}) => {
  const [hasError, setHasError] = useState(false);

  return (
    <div className="w-full h-full flex items-center justify-center bg-white relative">
      {hasError || !src ? (
        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
          <div className="text-center p-4">
            <div className="w-8 h-8 mx-auto mb-2 bg-gray-300 rounded flex items-center justify-center">
              <svg
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <p className="text-xs text-gray-500">Image not available</p>
          </div>
        </div>
      ) : (
        <img
          src={src}
          alt={alt}
          className="w-full h-full object-contain"
          onError={() => setHasError(true)}
        />
      )}
    </div>
  );
};

// Tablet Screenshot Component for PDF
const TabletScreenshot: React.FC<{ src: string; alt: string }> = ({
  src,
  alt,
}) => {
  const [hasError, setHasError] = useState(false);

  if (hasError || !src) {
    return (
      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
        <div className="text-center p-4">
          <div className="w-12 h-12 mx-auto mb-2 bg-gray-300 rounded-lg flex items-center justify-center">
            <svg
              className="w-6 h-6 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
          <p className="text-xs text-gray-500">Image not available</p>
        </div>
      </div>
    );
  }

  return (
    <img
      src={src}
      alt={alt}
      className="w-full h-full object-cover"
      onError={() => setHasError(true)}
    />
  );
};

export const UsabilitySection: React.FC<UsabilitySectionProps> = ({
  usabilityData,
  pagespeedData,
  pagespeedMobileData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "SEO ANALYSER"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoUsability")}
        onLogoError={() => onImageError?.("sectionLogoUsability")}
        sectionId="usability-details"
      />

      <SectionHeader
        title="Usability Audit Details"
        brandName={brand_name}
        brandWebsite={brand_website}
      />

      {/* Section Score Box */}
      {usabilityData.total_score && (
        <SectionScoreBox
          scoreGrade={usabilityData.total_score}
          title="Usability Score"
          description={
            usabilityData.overall_description ||
            "This score evaluates how user-friendly your website is across different devices and browsers, including mobile responsiveness, page loading speed, and overall user experience."
          }
        />
      )}

      {/* Device Rendering - Unified Section */}
      {usabilityData.device_rendering && (
        <div className="mb-8 p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
            <h3 className="font-bold text-gray-800 text-lg">
              Device Rendering
            </h3>
          </div>

          {/* Screenshots Section */}
          {usabilityData.device_rendering.screenshot_urls && (
            <div className="w-full flex flex-col lg:flex-row items-center justify-center gap-8 mb-6 overflow-x-hidden">
              {/* Mobile Screenshot */}
              {usabilityData.device_rendering.screenshot_urls.mobile && (
                <div className="text-center">
                  <h4 className="font-medium text-gray-700 mb-4">
                    Mobile View
                  </h4>
                  <div className="relative max-w-[220px] w-full h-auto aspect-[220/453] rounded-[32px] bg-black p-[10px] shadow-lg mx-auto">
                    {/* Phone top notch */}
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[80px] h-[25px] bg-black rounded-b-[14px] z-10 flex items-center justify-center">
                      <div className="w-[8px] h-[8px] rounded-full bg-gray-600 mx-1"></div>
                    </div>
                    {/* Phone screen */}
                    <div className="w-full h-full rounded-[24px] overflow-hidden border-[2px] border-gray-700 relative">
                      <MobileScreenshot
                        src={
                          usabilityData.device_rendering.screenshot_urls.mobile
                        }
                        alt="Mobile screenshot"
                      />
                    </div>
                    {/* Phone home button */}
                    <div className="absolute bottom-[5px] left-1/2 transform -translate-x-1/2 w-[40px] h-[5px] bg-gray-600 rounded-full"></div>
                  </div>
                </div>
              )}

              {/* Tablet Screenshot */}
              {usabilityData.device_rendering.screenshot_urls.tablet && (
                <div className="text-center">
                  <h4 className="font-medium text-gray-700 mb-4">
                    Tablet View
                  </h4>
                  <div className="relative max-w-[380px] w-full h-auto aspect-[380/494] mx-auto">
                    {/* Pure CSS iPad-style Tablet Frame */}
                    <div className="w-full h-full bg-black rounded-[24px] p-[12px]">
                      {/* Screen area */}
                      <div className="w-full h-full bg-white rounded-[16px] overflow-hidden relative">
                        {/* Home button indicator */}
                        <div className="absolute bottom-[6px] left-1/2 transform -translate-x-1/2 w-[40px] h-[4px] bg-gray-800 rounded-full"></div>
                        {/* Screenshot content */}
                        <div className="w-full h-full">
                          <TabletScreenshot
                            src={
                              usabilityData.device_rendering.screenshot_urls
                                .tablet
                            }
                            alt="Tablet screenshot"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Device Rendering Data */}
          <div className="space-y-1 bg-gray-50/50 rounded-lg p-4 mb-4">
            <DataRow
              label="Available Devices"
              value={
                usabilityData.device_rendering.available_devices?.length || 0
              }
            />
            <DataRow
              label="Expected Devices"
              value={
                usabilityData.device_rendering.expected_devices?.length || 0
              }
            />
            <DataRow
              label="Status"
              value={
                usabilityData.device_rendering.pass
                  ? "✓ Good"
                  : "⚠ Issues Found"
              }
              important={!usabilityData.device_rendering.pass}
            />
          </div>

          {/* Description */}
          {usabilityData.device_rendering.description && (
            <div className="pt-4 border-t border-gray-100 mb-4">
              <p className="text-sm text-gray-600">
                {usabilityData.device_rendering.description}
              </p>
            </div>
          )}

          {/* Device Rendering Recommendation */}
          {usabilityData.device_rendering.recommendation && (
            <div className="mt-4">
              <RecommendationCard
                recommendation={usabilityData.device_rendering.recommendation}
              />
            </div>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Viewport Configuration */}
        {usabilityData.viewport_usage && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Viewport Configuration
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Viewport Meta Tag"
                value={
                  usabilityData.viewport_usage.has_viewport_meta
                    ? "✓ Present"
                    : "✗ Missing"
                }
              />
              <DataRow
                label="Responsive Design"
                value={
                  usabilityData.viewport_usage.is_responsive ? "✓ Yes" : "✗ No"
                }
                important={!usabilityData.viewport_usage.is_responsive}
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.viewport_usage.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
                important={!usabilityData.viewport_usage.pass}
              />
            </div>
            {usabilityData.viewport_usage.viewport_content && (
              <div className="mt-3">
                <h4 className="font-medium text-gray-700 mb-1">
                  Viewport Content:
                </h4>
                <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                  {usabilityData.viewport_usage.viewport_content}
                </p>
              </div>
            )}

            {/* Viewport Configuration Recommendation */}
            {usabilityData.viewport_usage.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={usabilityData.viewport_usage.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Font Legibility */}
        {usabilityData.font_legibility && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Font Legibility
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Small Font Issues"
                value={
                  usabilityData.font_legibility.small_font_issue_count || 0
                }
              />
              <DataRow
                label="Elements Analyzed"
                value={
                  usabilityData.font_legibility.elements_analyzed_inline || 0
                }
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.font_legibility.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
                important={!usabilityData.font_legibility.pass}
              />
            </div>
            {usabilityData.font_legibility.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.font_legibility.description}
                </p>
              </div>
            )}

            {/* Font Legibility Recommendation */}
            {usabilityData.font_legibility.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={usabilityData.font_legibility.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Tap Target Sizing */}
        {usabilityData.tap_target_sizing && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Tap Target Sizing
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Interactive Elements"
                value={
                  usabilityData.tap_target_sizing.total_interactive_elements ||
                  0
                }
              />
              <DataRow
                label="Problematic Elements"
                value={
                  usabilityData.tap_target_sizing.problematic_elements_count ||
                  0
                }
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.tap_target_sizing.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
                important={!usabilityData.tap_target_sizing.pass}
              />
            </div>
            {usabilityData.tap_target_sizing.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.tap_target_sizing.description}
                </p>
              </div>
            )}

            {/* Tap Target Sizing Recommendation */}
            {usabilityData.tap_target_sizing.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={
                    usabilityData.tap_target_sizing.recommendation
                  }
                />
              </div>
            )}
          </div>
        )}

        {/* Flash Usage */}
        {usabilityData.flash_usage && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Flash Usage</h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Flash Elements"
                value={usabilityData.flash_usage.count || 0}
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.flash_usage.pass ? "✓ Good" : "⚠ Flash Found"
                }
                important={!usabilityData.flash_usage.pass}
              />
            </div>
            {usabilityData.flash_usage.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.flash_usage.description}
                </p>
              </div>
            )}

            {/* Flash Usage Recommendation */}
            {usabilityData.flash_usage.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={usabilityData.flash_usage.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Iframe Usage */}
        {usabilityData.iframes_usage && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Iframe Usage</h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Iframe Count"
                value={usabilityData.iframes_usage.count || 0}
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.iframes_usage.pass ? "✓ Good" : "⚠ Issues Found"
                }
                important={!usabilityData.iframes_usage.pass}
              />
            </div>
            {usabilityData.iframes_usage.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.iframes_usage.description}
                </p>
              </div>
            )}

            {/* Iframe Usage Recommendation */}
            {usabilityData.iframes_usage.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={usabilityData.iframes_usage.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Iframe Protection */}
        {usabilityData.iframe_protection && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Iframe Protection
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="X-Frame-Options"
                value={
                  usabilityData.iframe_protection.has_x_frame_options
                    ? "✓ Present"
                    : "✗ Missing"
                }
                important={!usabilityData.iframe_protection.has_x_frame_options}
              />
              <DataRow
                label="CSP Frame Protection"
                value={
                  usabilityData.iframe_protection.has_csp_frame_protection
                    ? "✓ Present"
                    : "✗ Missing"
                }
                important={
                  !usabilityData.iframe_protection.has_csp_frame_protection
                }
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.iframe_protection.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
                important={!usabilityData.iframe_protection.pass}
              />
            </div>
            {usabilityData.iframe_protection.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.iframe_protection.description}
                </p>
              </div>
            )}

            {/* Iframe Protection Recommendation */}
            {usabilityData.iframe_protection.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={
                    usabilityData.iframe_protection.recommendation
                  }
                />
              </div>
            )}
          </div>
        )}

        {/* Favicon Presence */}
        {usabilityData.favicon_presence && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Favicon</h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Favicon Present"
                value={
                  usabilityData.favicon_presence.has_favicon ? "✓ Yes" : "✗ No"
                }
                important={!usabilityData.favicon_presence.has_favicon}
              />
              <DataRow
                label="Apple Touch Icon"
                value={
                  usabilityData.favicon_presence.has_apple_touch_icon
                    ? "✓ Yes"
                    : "✗ No"
                }
                important={!usabilityData.favicon_presence.has_apple_touch_icon}
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.favicon_presence.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
                important={!usabilityData.favicon_presence.pass}
              />
            </div>
            {usabilityData.favicon_presence.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.favicon_presence.description}
                </p>
              </div>
            )}

            {/* Favicon Presence Recommendation */}
            {usabilityData.favicon_presence.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={usabilityData.favicon_presence.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Email Privacy */}
        {usabilityData.email_privacy && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Email Privacy</h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Exposed Emails"
                value={usabilityData.email_privacy.exposed_email_count || 0}
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.email_privacy.pass ? "✓ Good" : "⚠ Issues Found"
                }
                important={!usabilityData.email_privacy.pass}
              />
            </div>
            {usabilityData.email_privacy.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.email_privacy.description}
                </p>
              </div>
            )}

            {/* Email Privacy Recommendation */}
            {usabilityData.email_privacy.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={usabilityData.email_privacy.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Google PageSpeed Section */}
      {(pagespeedData || pagespeedMobileData) && (
        <div className="mt-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
            <h3 className="font-bold text-gray-800 text-lg">
              Google PageSpeed Insights
            </h3>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Desktop PageSpeed */}
            {pagespeedData?.performance_desktop && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-2 h-6 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full"></div>
                  <h4 className="font-bold text-gray-800">
                    Desktop Performance
                  </h4>
                </div>

                {/* Desktop Score Chart */}
                <div className="flex justify-center mb-4">
                  <ProgressChart
                    score={pagespeedData.performance_desktop.performance_score}
                    title=""
                    size="md"
                    showGrade={false}
                    progressStates={[
                      {
                        label: "Score",
                        value:
                          pagespeedData.performance_desktop.performance_score,
                        isNoColor: false,
                      },
                    ]}
                  />
                </div>

                {/* Desktop Metrics */}
                <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                  <DataRow
                    label="First Contentful Paint"
                    value={`${
                      pagespeedData.performance_desktop[
                        "First Contentful Paint (FCP)"
                      ]?.toFixed(2) || "N/A"
                    }s`}
                  />
                  <DataRow
                    label="Largest Contentful Paint"
                    value={`${
                      pagespeedData.performance_desktop[
                        "Largest Contentful Paint (LCP)"
                      ]?.toFixed(2) || "N/A"
                    }s`}
                  />
                  <DataRow
                    label="Speed Index"
                    value={`${
                      pagespeedData.performance_desktop[
                        "Speed Index (SI)"
                      ]?.toFixed(2) || "N/A"
                    }s`}
                  />
                  <DataRow
                    label="Time to Interactive"
                    value={`${
                      pagespeedData.performance_desktop[
                        "Time to Interactive (TTI)"
                      ]?.toFixed(2) || "N/A"
                    }s`}
                  />
                </div>

                {pagespeedData.performance_desktop.description && (
                  <div className="mt-3 pt-2 border-t border-gray-100">
                    <p className="text-sm text-gray-600">
                      {pagespeedData.performance_desktop.description}
                    </p>
                  </div>
                )}

                {/* Desktop Performance Recommendation */}
                {pagespeedData.performance_desktop.recommendation && (
                  <div className="mt-4">
                    <RecommendationCard
                      recommendation={
                        pagespeedData.performance_desktop.recommendation
                      }
                    />
                  </div>
                )}
              </div>
            )}

            {/* Mobile PageSpeed */}
            {pagespeedMobileData?.performance_mobile && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-2 h-6 bg-gradient-to-b from-green-500 to-green-600 rounded-full"></div>
                  <h4 className="font-bold text-gray-800">
                    Mobile Performance
                  </h4>
                </div>

                {/* Mobile Score Chart */}
                <div className="flex justify-center mb-4">
                  <ProgressChart
                    score={
                      pagespeedMobileData.performance_mobile.performance_score
                    }
                    title=""
                    size="md"
                    showGrade={false}
                    progressStates={[
                      {
                        label: "Score",
                        value:
                          pagespeedMobileData.performance_mobile
                            .performance_score,
                        isNoColor: false,
                      },
                    ]}
                  />
                </div>

                {/* Mobile Metrics */}
                <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                  <DataRow
                    label="First Contentful Paint"
                    value={`${
                      pagespeedMobileData.performance_mobile[
                        "First Contentful Paint (FCP)"
                      ]?.toFixed(2) || "N/A"
                    }s`}
                  />
                  <DataRow
                    label="Largest Contentful Paint"
                    value={`${
                      pagespeedMobileData.performance_mobile[
                        "Largest Contentful Paint (LCP)"
                      ]?.toFixed(2) || "N/A"
                    }s`}
                  />
                  <DataRow
                    label="Speed Index"
                    value={`${
                      pagespeedMobileData.performance_mobile[
                        "Speed Index (SI)"
                      ]?.toFixed(2) || "N/A"
                    }s`}
                  />
                  <DataRow
                    label="Time to Interactive"
                    value={`${
                      pagespeedMobileData.performance_mobile[
                        "Time to Interactive (TTI)"
                      ]?.toFixed(2) || "N/A"
                    }s`}
                  />
                </div>

                {pagespeedMobileData.performance_mobile.description && (
                  <div className="mt-3 pt-2 border-t border-gray-100">
                    <p className="text-sm text-gray-600">
                      {pagespeedMobileData.performance_mobile.description}
                    </p>
                  </div>
                )}

                {/* Mobile Performance Recommendation */}
                {pagespeedMobileData.performance_mobile.recommendation && (
                  <div className="mt-4">
                    <RecommendationCard
                      recommendation={
                        pagespeedMobileData.performance_mobile.recommendation
                      }
                    />
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Core Web Vitals Section */}
          {(pagespeedData?.core_web_vitals_desktop ||
            pagespeedMobileData?.core_web_vitals_mobile) && (
            <div className="mt-6">
              <h4 className="font-bold text-gray-800 mb-4">
                Google's Core Web Vitals
              </h4>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Desktop Core Web Vitals */}
                {pagespeedData?.core_web_vitals_desktop && (
                  <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                      <h4 className="font-bold text-gray-800 text-lg">
                        Google's Core Web Vitals - Desktop
                      </h4>
                    </div>

                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-4">
                        {pagespeedData.core_web_vitals_desktop.description ||
                          "Core Web Vitals (LCP, INP, CLS) measure key aspects of desktop user experience: loading, interactivity, and visual stability."}
                      </p>
                    </div>

                    {/* Core Web Vitals Table */}
                    <div className="bg-gray-50/50 rounded-lg p-4">
                      <div className="grid grid-cols-2 gap-2 mb-2">
                        <div className="font-semibold text-gray-800">
                          Lab Data
                        </div>
                        <div className="font-semibold text-gray-800 text-right">
                          Value
                        </div>
                      </div>
                      <div className="space-y-1">
                        {pagespeedData.core_web_vitals_desktop[
                          "Cumulative Layout Shift (CLS)"
                        ] !== undefined && (
                          <div className="grid grid-cols-2 gap-2 py-1 border-b border-gray-200 last:border-b-0">
                            <div className="text-sm text-gray-700">
                              Cumulative Layout Shift (CLS)
                            </div>
                            <div className="text-sm text-gray-700 text-right">
                              {pagespeedData.core_web_vitals_desktop[
                                "Cumulative Layout Shift (CLS)"
                              ] !== null
                                ? pagespeedData.core_web_vitals_desktop[
                                    "Cumulative Layout Shift (CLS)"
                                  ].toFixed(3)
                                : "N/A"}
                            </div>
                          </div>
                        )}
                        {pagespeedData.core_web_vitals_desktop[
                          "Largest Contentful Paint (LCP)"
                        ] !== undefined && (
                          <div className="grid grid-cols-2 gap-2 py-1 border-b border-gray-200 last:border-b-0">
                            <div className="text-sm text-gray-700">
                              Largest Contentful Paint (LCP)
                            </div>
                            <div className="text-sm text-gray-700 text-right">
                              {pagespeedData.core_web_vitals_desktop[
                                "Largest Contentful Paint (LCP)"
                              ] !== null
                                ? `${pagespeedData.core_web_vitals_desktop[
                                    "Largest Contentful Paint (LCP)"
                                  ].toFixed(2)}s`
                                : "N/A"}
                            </div>
                          </div>
                        )}
                        {pagespeedData.core_web_vitals_desktop[
                          "Interaction to Next Paint (INP)"
                        ] !== undefined && (
                          <div className="grid grid-cols-2 gap-2 py-1 border-b border-gray-200 last:border-b-0">
                            <div className="text-sm text-gray-700">
                              Interaction to Next Paint (INP)
                            </div>
                            <div className="text-sm text-gray-700 text-right">
                              {pagespeedData.core_web_vitals_desktop[
                                "Interaction to Next Paint (INP)"
                              ] !== null
                                ? `${pagespeedData.core_web_vitals_desktop[
                                    "Interaction to Next Paint (INP)"
                                  ].toFixed(0)}ms`
                                : "N/A"}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Desktop Core Web Vitals Recommendation */}
                    {pagespeedData.core_web_vitals_desktop.recommendation && (
                      <div className="mt-4">
                        <RecommendationCard
                          recommendation={
                            pagespeedData.core_web_vitals_desktop.recommendation
                          }
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* Mobile Core Web Vitals */}
                {pagespeedMobileData?.core_web_vitals_mobile && (
                  <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                      <h4 className="font-bold text-gray-800 text-lg">
                        Google's Core Web Vitals - Mobile
                      </h4>
                    </div>

                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-4">
                        {pagespeedMobileData.core_web_vitals_mobile
                          .description ||
                          "Core Web Vitals (LCP, INP, CLS) measure key aspects of mobile user experience: loading, interactivity, and visual stability."}
                      </p>
                    </div>

                    {/* Core Web Vitals Table */}
                    <div className="bg-gray-50/50 rounded-lg p-4">
                      <div className="grid grid-cols-2 gap-2 mb-2">
                        <div className="font-semibold text-gray-800">
                          Lab Data
                        </div>
                        <div className="font-semibold text-gray-800 text-right">
                          Value
                        </div>
                      </div>
                      <div className="space-y-1">
                        {pagespeedMobileData.core_web_vitals_mobile[
                          "Cumulative Layout Shift (CLS)"
                        ] !== undefined && (
                          <div className="grid grid-cols-2 gap-2 py-1 border-b border-gray-200 last:border-b-0">
                            <div className="text-sm text-gray-700">
                              Cumulative Layout Shift (CLS)
                            </div>
                            <div className="text-sm text-gray-700 text-right">
                              {pagespeedMobileData.core_web_vitals_mobile[
                                "Cumulative Layout Shift (CLS)"
                              ] !== null
                                ? pagespeedMobileData.core_web_vitals_mobile[
                                    "Cumulative Layout Shift (CLS)"
                                  ].toFixed(3)
                                : "N/A"}
                            </div>
                          </div>
                        )}
                        {pagespeedMobileData.core_web_vitals_mobile[
                          "Largest Contentful Paint (LCP)"
                        ] !== undefined && (
                          <div className="grid grid-cols-2 gap-2 py-1 border-b border-gray-200 last:border-b-0">
                            <div className="text-sm text-gray-700">
                              Largest Contentful Paint (LCP)
                            </div>
                            <div className="text-sm text-gray-700 text-right">
                              {pagespeedMobileData.core_web_vitals_mobile[
                                "Largest Contentful Paint (LCP)"
                              ] !== null
                                ? `${pagespeedMobileData.core_web_vitals_mobile[
                                    "Largest Contentful Paint (LCP)"
                                  ].toFixed(2)}s`
                                : "N/A"}
                            </div>
                          </div>
                        )}
                        {pagespeedMobileData.core_web_vitals_mobile[
                          "Interaction to Next Paint (INP)"
                        ] !== undefined && (
                          <div className="grid grid-cols-2 gap-2 py-1 border-b border-gray-200 last:border-b-0">
                            <div className="text-sm text-gray-700">
                              Interaction to Next Paint (INP)
                            </div>
                            <div className="text-sm text-gray-700 text-right">
                              {pagespeedMobileData.core_web_vitals_mobile[
                                "Interaction to Next Paint (INP)"
                              ] !== null
                                ? `${pagespeedMobileData.core_web_vitals_mobile[
                                    "Interaction to Next Paint (INP)"
                                  ].toFixed(0)}ms`
                                : "N/A"}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Mobile Core Web Vitals Recommendation */}
                    {pagespeedMobileData.core_web_vitals_mobile
                      .recommendation && (
                      <div className="mt-4">
                        <RecommendationCard
                          recommendation={
                            pagespeedMobileData.core_web_vitals_mobile
                              .recommendation
                          }
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Additional Usability Metrics - Enhanced with all data */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Font Legibility Analysis */}
        {usabilityData.font_legibility && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Font Legibility
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Small Font Issues"
                value={`${
                  usabilityData.font_legibility.small_font_issue_count || 0
                }`}
                important={
                  (usabilityData.font_legibility.small_font_issue_count || 0) >
                  0
                }
              />
              <DataRow
                label="Elements Analyzed (Inline)"
                value={`${
                  usabilityData.font_legibility.elements_analyzed_inline || 0
                }`}
              />
              <DataRow
                label="CSS Declarations Analyzed"
                value={`${
                  usabilityData.font_legibility.declarations_analyzed_css || 0
                }`}
              />
              <DataRow
                label="External CSS"
                value={
                  usabilityData.font_legibility.has_external_css
                    ? "✓ Present"
                    : "✗ Not Present"
                }
              />
              <DataRow
                label="External CSS Links"
                value={`${
                  usabilityData.font_legibility.external_css_total_links || 0
                }`}
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.font_legibility.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
                important={!usabilityData.font_legibility.pass}
              />
            </div>

            {/* CSS Issues */}
            {usabilityData.font_legibility.css_issues &&
              usabilityData.font_legibility.css_issues.length > 0 && (
                <div className="mt-3">
                  <h4 className="font-medium text-gray-700 mb-2">
                    CSS Issues:
                  </h4>
                  <div className="bg-gray-50 p-3 rounded">
                    {usabilityData.font_legibility.css_issues.map(
                      (issue: any, index: number) => (
                        <div
                          key={index}
                          className="text-xs text-gray-600 mb-2 p-2 bg-white rounded"
                        >
                          <p>
                            <strong>Selector:</strong> {issue.selector}
                          </p>
                          <p>
                            <strong>Size:</strong> {issue.size}
                          </p>
                          <p>
                            <strong>Severity:</strong> {issue.severity}
                          </p>
                          <p>
                            <strong>Source:</strong> {issue.source}
                          </p>
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}

            {/* Analysis Timing */}
            {usabilityData.font_legibility.timing && (
              <div className="mt-3">
                <h4 className="font-medium text-gray-700 mb-2">
                  Analysis Timing:
                </h4>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <strong>Total:</strong>{" "}
                      {usabilityData.font_legibility.timing.total_analysis_time?.toFixed(
                        2
                      )}
                      s
                    </div>
                    <div>
                      <strong>Inline:</strong>{" "}
                      {usabilityData.font_legibility.timing.inline_styles_time?.toFixed(
                        2
                      )}
                      s
                    </div>
                    <div>
                      <strong>Internal CSS:</strong>{" "}
                      {usabilityData.font_legibility.timing.internal_css_time?.toFixed(
                        2
                      )}
                      s
                    </div>
                    <div>
                      <strong>External CSS:</strong>{" "}
                      {usabilityData.font_legibility.timing.external_css_time?.toFixed(
                        2
                      )}
                      s
                    </div>
                  </div>
                </div>
              </div>
            )}

            {usabilityData.font_legibility.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.font_legibility.description}
                </p>
              </div>
            )}

            {usabilityData.font_legibility.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={usabilityData.font_legibility.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Tap Target Sizing Analysis */}
        {usabilityData.tap_target_sizing && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Tap Target Sizing
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Total Interactive Elements"
                value={`${
                  usabilityData.tap_target_sizing.total_interactive_elements ||
                  0
                }`}
              />
              <DataRow
                label="Problematic Elements"
                value={`${
                  usabilityData.tap_target_sizing.problematic_elements_count ||
                  0
                }`}
                important={
                  (usabilityData.tap_target_sizing.problematic_elements_count ||
                    0) > 0
                }
              />
              <DataRow
                label="Touch-Specific CSS"
                value={
                  usabilityData.tap_target_sizing.has_touch_specific_css
                    ? "✓ Yes"
                    : "✗ No"
                }
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.tap_target_sizing.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
                important={!usabilityData.tap_target_sizing.pass}
              />
            </div>

            {/* Touch Media Queries */}
            {usabilityData.tap_target_sizing.touch_media_queries &&
              usabilityData.tap_target_sizing.touch_media_queries.length >
                0 && (
                <div className="mt-3">
                  <h4 className="font-medium text-gray-700 mb-2">
                    Touch Media Queries:
                  </h4>
                  <div className="bg-gray-50 p-3 rounded max-h-24 overflow-y-auto">
                    {usabilityData.tap_target_sizing.touch_media_queries
                      .slice(0, 3)
                      .map((query: string, index: number) => (
                        <p
                          key={index}
                          className="text-xs text-gray-600 mb-1 break-all"
                        >
                          {query}
                        </p>
                      ))}
                    {usabilityData.tap_target_sizing.touch_media_queries
                      .length > 3 && (
                      <p className="text-xs text-gray-500">
                        ... and{" "}
                        {usabilityData.tap_target_sizing.touch_media_queries
                          .length - 3}{" "}
                        more
                      </p>
                    )}
                  </div>
                </div>
              )}

            {/* Problematic Elements */}
            {usabilityData.tap_target_sizing.problematic_elements &&
              usabilityData.tap_target_sizing.problematic_elements.length >
                0 && (
                <div className="mt-3">
                  <h4 className="font-medium text-gray-700 mb-2">
                    Problematic Elements:
                  </h4>
                  <div className="bg-gray-50 p-3 rounded max-h-24 overflow-y-auto">
                    {usabilityData.tap_target_sizing.problematic_elements
                      .slice(0, 2)
                      .map((element: any, index: number) => (
                        <div
                          key={index}
                          className="text-xs text-gray-600 mb-2 p-2 bg-white rounded"
                        >
                          {JSON.stringify(element)}
                        </div>
                      ))}
                    {usabilityData.tap_target_sizing.problematic_elements
                      .length > 2 && (
                      <p className="text-xs text-gray-500">
                        ... and{" "}
                        {usabilityData.tap_target_sizing.problematic_elements
                          .length - 2}{" "}
                        more
                      </p>
                    )}
                  </div>
                </div>
              )}

            {usabilityData.tap_target_sizing.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.tap_target_sizing.description}
                </p>
              </div>
            )}

            {usabilityData.tap_target_sizing.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={
                    usabilityData.tap_target_sizing.recommendation
                  }
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Usability Recommendations */}
      <div className="mt-8">
        <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
          Usability Recommendations
        </h3>
        <div className="grid grid-cols-1 gap-4 recommendations-grid">
          {/* Device Rendering Recommendations */}
          {usabilityData.device_rendering?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.device_rendering.recommendation}
            />
          )}

          {/* Viewport Usage Recommendations */}
          {usabilityData.viewport_usage?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.viewport_usage.recommendation}
            />
          )}

          {/* Font Legibility Recommendations */}
          {usabilityData.font_legibility?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.font_legibility.recommendation}
            />
          )}

          {/* Tap Target Sizing Recommendations */}
          {usabilityData.tap_target_sizing?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.tap_target_sizing.recommendation}
            />
          )}

          {/* Flash Usage Recommendations */}
          {usabilityData.flash_usage?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.flash_usage.recommendation}
            />
          )}

          {/* iFrame Usage Recommendations */}
          {usabilityData.iframes_usage?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.iframes_usage.recommendation}
            />
          )}

          {/* Iframe Protection Recommendations */}
          {usabilityData.iframe_protection?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.iframe_protection.recommendation}
            />
          )}

          {/* Favicon Presence Recommendations */}
          {usabilityData.favicon_presence?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.favicon_presence.recommendation}
            />
          )}

          {/* Email Privacy Recommendations */}
          {usabilityData.email_privacy?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.email_privacy.recommendation}
            />
          )}

          {/* General Usability Recommendation if no specific ones */}
          {!usabilityData.device_rendering?.recommendation &&
            !usabilityData.viewport_usage?.recommendation &&
            !usabilityData.font_legibility?.recommendation &&
            !usabilityData.tap_target_sizing?.recommendation &&
            !usabilityData.flash_usage?.recommendation &&
            !usabilityData.iframes_usage?.recommendation &&
            !usabilityData.iframe_protection?.recommendation &&
            !usabilityData.favicon_presence?.recommendation &&
            !usabilityData.email_privacy?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: "Your website's usability appears to be well optimized. Continue monitoring mobile responsiveness and user experience metrics.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
