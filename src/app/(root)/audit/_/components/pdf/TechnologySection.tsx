"use client";
import React from "react";
import { TechSEOAnalysis } from "@/types/seoAnalyzerTypes";
import {
  SectionHeader,
  SectionScoreBox,
  DataRow,
  RecommendationCard,
} from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";
import { TechnologyIcon } from "./TechnologyIcon";

export interface TechnologySectionProps {
  technologyData: TechSEOAnalysis;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
  onTechnologyIconLoad?: (index: number, success: boolean) => void;
}

export const TechnologySection: React.FC<TechnologySectionProps> = ({
  technologyData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
  onTechnologyIconLoad,
}) => {
  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "SEO ANALYSER"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoTech")}
        onLogoError={() => onImageError?.("sectionLogoTech")}
        sectionId="technology-details"
      />

      <SectionHeader
        title="Technology Review Audit"
        brandName={brand_name}
        brandWebsite={brand_website}
      />

      {/* Section Score Box */}
      {technologyData.total_score && (
        <SectionScoreBox
          scoreGrade={technologyData.total_score}
          title="Technology Score"
          description={
            technologyData.overall_description ||
            "This score evaluates your website's technical implementation, including SSL security, server configuration, and technology stack optimization for better performance and SEO."
          }
        />
      )}

      {/* Technology Summary Metrics */}
      <div className="mb-8">
        <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
            <h3 className="font-bold text-gray-800 text-lg">
              Technology Overview
            </h3>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 bg-gradient-to-br from-primary/5 to-primary/10 rounded-lg border border-primary/20">
              <div className="text-2xl font-bold text-primary mb-1">
                {technologyData.technologies?.technology_count?.toString() ||
                  technologyData.technologies?.technologies?.length?.toString() ||
                  "0"}
              </div>
              <div className="text-sm text-gray-600 font-medium">
                Technologies Used
              </div>
            </div>

            <div className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {technologyData.dns_servers?.count?.toString() || "0"}
              </div>
              <div className="text-sm text-gray-600 font-medium">
                DNS Servers
              </div>
            </div>

            <div className="p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {technologyData.server_ip?.all_ips?.length?.toString() || "0"}
              </div>
              <div className="text-sm text-gray-600 font-medium">
                Server IPs
              </div>
            </div>

            <div className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200">
              <div className="text-2xl font-bold text-purple-600 mb-1">
                {technologyData.ssl_enabled ? "Yes" : "No"}
              </div>
              <div className="text-sm text-gray-600 font-medium">
                SSL Enabled
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Technologies Detected - moved to next page */}
      {technologyData.technologies &&
        technologyData.technologies.technologies && (
          <div className="mb-8 ">
            <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                <h3 className="font-bold text-gray-800 text-lg">
                  Technologies Detected
                </h3>
              </div>

              {/* Technology Summary */}
              <div className="mb-6 p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border border-primary/20">
                <div className="grid grid-cols-2 gap-4">
                  <DataRow
                    label="Total Technologies"
                    value={
                      technologyData.technologies.technology_count?.toString() ||
                      technologyData.technologies.technologies.length.toString()
                    }
                  />
                  <DataRow
                    label="Status"
                    value={
                      technologyData.technologies.pass
                        ? "✓ Good"
                        : "⚠ Issues Found"
                    }
                  />
                </div>
              </div>

              {/* Technology Icons Grid */}
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
                {technologyData.technologies.technologies
                  .slice(0, 12)
                  .map((tech, index) => (
                    <div
                      key={index}
                      className="flex flex-col items-center p-4 border border-gray-200/60 rounded-xl bg-gradient-to-b from-gray-50/50 to-white transition-all duration-200"
                    >
                      <div className="w-10 h-10 mb-3 flex items-center justify-center">
                        <TechnologyIcon
                          name={tech.name}
                          index={index}
                          onImageLoad={onTechnologyIconLoad}
                        />
                      </div>
                      <span
                        className="text-xs text-center text-gray-700 font-semibold truncate w-full leading-tight"
                        title={tech.name}
                      >
                        {tech.name}
                      </span>
                      {tech.category && (
                        <span className="text-xs text-gray-500 mt-1 truncate w-full text-center">
                          {tech.category}
                        </span>
                      )}
                    </div>
                  ))}
              </div>

              {technologyData.technologies.technologies.length > 12 && (
                <div className="mb-4 p-3 bg-primary/5 rounded-lg border border-primary/20">
                  <p className="text-sm text-primary font-medium text-center">
                    ... and{" "}
                    {technologyData.technologies.technologies.length - 12} more
                    technologies detected
                  </p>
                </div>
              )}

              {/* Detailed Technology Stack - matching main component */}
              {technologyData.technologies.technologies.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-semibold text-gray-800 mb-4">
                    Technology Stack Details:
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {technologyData.technologies.technologies
                      .slice(0, 8)
                      .map((tech, index) => (
                        <div
                          key={index}
                          className="bg-gray-50/50 p-3 rounded-lg border border-gray-200/60"
                        >
                          <div className="font-medium text-gray-800 mb-1">
                            {tech.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">Category: </span>
                            {tech.category || "Unknown"}
                          </div>
                          {tech.version && tech.version !== "Detected" && (
                            <div className="text-sm text-gray-600">
                              <span className="font-medium">Version: </span>
                              {tech.version}
                            </div>
                          )}
                        </div>
                      ))}
                  </div>
                  {technologyData.technologies.technologies.length > 8 && (
                    <p className="text-xs text-gray-500 mt-3 text-center">
                      Showing 8 of{" "}
                      {technologyData.technologies.technologies.length}{" "}
                      technologies
                    </p>
                  )}
                </div>
              )}

              {/* Technologies Detected Recommendation */}
              {technologyData.technologies.recommendations &&
                technologyData.technologies.recommendations.length > 0 && (
                  <div className="mt-6">
                    <RecommendationCard
                      recommendation={
                        technologyData.technologies.recommendations[0]
                      }
                    />
                  </div>
                )}
            </div>
          </div>
        )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Web Server Audit */}
        {technologyData.web_server && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Web Server Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Server"
                value={technologyData.web_server.server || "Unknown"}
              />
              <DataRow
                label="Status"
                value={
                  technologyData.web_server.pass ? "✓ Good" : "⚠ Issues Found"
                }
              />
            </div>
            {technologyData.web_server.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {technologyData.web_server.description}
                </p>
              </div>
            )}

            {/* Web Server Recommendation */}
            {technologyData.web_server.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={technologyData.web_server.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* DNS Audit */}
        {technologyData.dns_servers && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">DNS Audit</h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="DNS Status"
                value={
                  technologyData.dns_servers.pass ? "✓ Good" : "⚠ Issues Found"
                }
              />
            </div>
            {technologyData.dns_servers.nameservers &&
              technologyData.dns_servers.nameservers.length > 0 && (
                <div className="mt-3">
                  <h4 className="font-medium text-gray-700 mb-2">
                    DNS Servers:
                  </h4>
                  <div className="space-y-1">
                    {technologyData.dns_servers.nameservers
                      .slice(0, 3)
                      .map((server, index) => (
                        <p
                          key={index}
                          className="text-sm text-gray-600 bg-gray-50 p-2 rounded"
                        >
                          {server}
                        </p>
                      ))}
                  </div>
                </div>
              )}

            {/* DNS Servers Recommendation */}
            {technologyData.dns_servers.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={technologyData.dns_servers.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Robots Meta Audit */}
        {technologyData.robots_meta && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Robots Meta Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Robots Status"
                value={
                  !technologyData.robots_meta.noindex
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
              />
            </div>
            {technologyData.robots_meta.description && (
              <div className="mt-3">
                <h4 className="font-medium text-gray-700 mb-2">Description:</h4>
                <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                  {technologyData.robots_meta.description}
                </p>
              </div>
            )}

            {/* Robots Meta Recommendation */}
            {technologyData.robots_meta.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={technologyData.robots_meta.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* SSL Audit */}
        {technologyData.ssl_enabled !== undefined && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                SSL Certificate Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="SSL Status"
                value={
                  technologyData.ssl_enabled ? "✓ Enabled" : "⚠ Not Enabled"
                }
              />
              <DataRow
                label="Security"
                value={
                  technologyData.ssl_enabled
                    ? "✓ Secure Connection"
                    : "⚠ Insecure Connection"
                }
              />
            </div>

            {technologyData.ssl_enabled && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  SSL certificate is properly configured, providing secure HTTPS
                  connections and improving SEO rankings.
                </p>
              </div>
            )}

            {/* SSL Certificate Recommendation */}
            {!technologyData.ssl_enabled && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={{
                    text: "Enable SSL certificate to secure your website and improve SEO rankings. HTTPS is a ranking factor and builds user trust.",
                    priority: "High",
                  }}
                />
              </div>
            )}
          </div>
        )}

        {/* Charset Audit */}
        {technologyData.charset && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Character Set Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Charset"
                value={technologyData.charset.charset || "Unknown"}
              />
              <DataRow
                label="Source"
                value={technologyData.charset.source || "Unknown"}
              />
              <DataRow
                label="Standard"
                value={
                  technologyData.charset.is_standard
                    ? "✓ Yes"
                    : "⚠ Non-standard"
                }
              />
            </div>

            {/* Charset Recommendation */}
            {technologyData.charset.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={technologyData.charset.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Server IP Audit */}
        {technologyData.server_ip && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Server IP Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Primary IP"
                value={technologyData.server_ip.ip || "Unknown"}
              />
              <DataRow
                label="Status"
                value={
                  technologyData.server_ip.pass ? "✓ Good" : "⚠ Issues Found"
                }
              />
            </div>

            {/* All Server IPs - matching main audit component */}
            {technologyData.server_ip.all_ips &&
              technologyData.server_ip.all_ips.length > 0 && (
                <div className="mt-4 pt-3 border-t border-gray-100">
                  <h5 className="font-medium text-gray-700 mb-2">
                    All Server IPs:
                  </h5>
                  <div className="grid grid-cols-1 gap-1">
                    {technologyData.server_ip.all_ips.map((ip, index) => (
                      <div
                        key={index}
                        className="p-2 bg-gray-50 rounded text-sm font-mono text-gray-700"
                      >
                        {ip}
                      </div>
                    ))}
                  </div>
                  {technologyData.server_ip.all_ips.length > 5 && (
                    <p className="text-xs text-gray-500 mt-2">
                      Showing{" "}
                      {Math.min(5, technologyData.server_ip.all_ips.length)} of{" "}
                      {technologyData.server_ip.all_ips.length} IPs
                    </p>
                  )}
                </div>
              )}

            {/* Server IP Recommendation */}
            {technologyData.server_ip.recommendation && (
              <div className="mt-4">
                <RecommendationCard
                  recommendation={technologyData.server_ip.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Email Security Section - Missing from PDF */}
      {(technologyData.dmarc_record || technologyData.spf_record) && (
        <div className="mt-8 print:break-before-page">
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Email Security Information
              </h3>
            </div>
            <p className="text-sm text-gray-600 mb-6">
              Email security records help protect your domain from email
              spoofing and phishing attacks.
            </p>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* DMARC Record */}
              {technologyData.dmarc_record && (
                <div className="p-4 border border-gray-200/60 rounded-lg bg-gradient-to-b from-gray-50/30 to-white">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-1.5 h-6 bg-primary rounded-full"></div>
                    <h4 className="font-semibold text-gray-800">
                      DMARC Record
                    </h4>
                  </div>

                  <div className="space-y-3">
                    <DataRow
                      label="Status"
                      value={
                        technologyData.dmarc_record.pass
                          ? "✓ Configured"
                          : "⚠ Not Configured"
                      }
                    />
                    <DataRow
                      label="Policy"
                      value={technologyData.dmarc_record.policy || "none"}
                    />

                    {technologyData.dmarc_record.record && (
                      <div className="mt-3">
                        <h5 className="font-medium text-gray-700 mb-2 text-sm">
                          DMARC Record:
                        </h5>
                        <div className="bg-gray-100 p-3 rounded text-xs font-mono break-all text-gray-700 border">
                          {technologyData.dmarc_record.record}
                        </div>
                      </div>
                    )}
                  </div>

                  {technologyData.dmarc_record.recommendation && (
                    <div className="mt-4">
                      <RecommendationCard
                        recommendation={
                          technologyData.dmarc_record.recommendation
                        }
                      />
                    </div>
                  )}
                </div>
              )}

              {/* SPF Record */}
              {technologyData.spf_record && (
                <div className="p-4 border border-gray-200/60 rounded-lg bg-gradient-to-b from-gray-50/30 to-white">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-1.5 h-6 bg-primary rounded-full"></div>
                    <h4 className="font-semibold text-gray-800">SPF Record</h4>
                  </div>

                  <div className="space-y-3">
                    <DataRow
                      label="Status"
                      value={
                        technologyData.spf_record.pass
                          ? "✓ Configured"
                          : "⚠ Not Configured"
                      }
                    />
                    <DataRow
                      label="Policy Strength"
                      value={
                        technologyData.spf_record.policy_strength || "none"
                      }
                    />

                    {technologyData.spf_record.record && (
                      <div className="mt-3">
                        <h5 className="font-medium text-gray-700 mb-2 text-sm">
                          SPF Record:
                        </h5>
                        <div className="bg-gray-100 p-3 rounded text-xs font-mono break-all text-gray-700 border">
                          {technologyData.spf_record.record}
                        </div>
                      </div>
                    )}
                  </div>

                  {technologyData.spf_record.recommendation && (
                    <div className="mt-4">
                      <RecommendationCard
                        recommendation={
                          technologyData.spf_record.recommendation
                        }
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Technology Recommendations - Moved outside grid for full width */}
      <div className="mt-8">
        <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
          Technology Recommendations
        </h3>
        <div className="grid grid-cols-1 gap-4 recommendations-grid">
          {/* SSL Recommendations */}
          {technologyData.ssl_enabled !== undefined &&
            !technologyData.ssl_enabled && (
              <RecommendationCard
                recommendation={{
                  text: "Enable SSL certificate to secure your website and improve SEO rankings. HTTPS is a ranking factor and builds user trust.",
                  priority: "High",
                }}
              />
            )}

          {/* DNS Recommendations */}
          {technologyData.dns_servers?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.dns_servers.recommendation}
            />
          )}

          {/* Web Server Recommendations */}
          {technologyData.web_server?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.web_server.recommendation}
            />
          )}

          {/* Robots Meta Recommendations */}
          {technologyData.robots_meta?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.robots_meta.recommendation}
            />
          )}

          {/* Server IP Recommendations */}
          {technologyData.server_ip?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.server_ip.recommendation}
            />
          )}

          {/* Charset Recommendations */}
          {technologyData.charset?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.charset.recommendation}
            />
          )}

          {/* DMARC Recommendations */}
          {technologyData.dmarc_record?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.dmarc_record.recommendation}
            />
          )}

          {/* SPF Recommendations */}
          {technologyData.spf_record?.recommendation && (
            <RecommendationCard
              recommendation={technologyData.spf_record.recommendation}
            />
          )}

          {/* Technologies Recommendations */}
          {technologyData.technologies?.recommendations &&
            technologyData.technologies.recommendations.length > 0 &&
            technologyData.technologies.recommendations.map((rec, index) => (
              <RecommendationCard key={index} recommendation={rec} />
            ))}

          {/* General Technology Recommendation if no specific ones */}
          {!technologyData.ssl_enabled &&
            !technologyData.dns_servers?.recommendation &&
            !technologyData.web_server?.recommendation &&
            !technologyData.robots_meta?.recommendation &&
            !technologyData.server_ip?.recommendation &&
            !technologyData.charset?.recommendation &&
            !technologyData.dmarc_record?.recommendation &&
            !technologyData.spf_record?.recommendation &&
            (!technologyData.technologies?.recommendations ||
              technologyData.technologies.recommendations.length === 0) && (
              <RecommendationCard
                recommendation={{
                  text: "Your website's technology stack appears to be well configured. Continue monitoring for security updates and performance optimizations.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
