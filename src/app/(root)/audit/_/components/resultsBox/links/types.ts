// Shared types for Links components

export type LinksAnalysisData = {
  main?: {
    totalScore: {
      Grade: string;
      score: number;
    };
  };
  broken_links?: {
    metrics?: {
      broken_count_on_page?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      links_checked_on_page?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      broken_percentage_on_page?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
    broken_analysis_summary?: string;
    broken_links_detail_sample?: Array<{
      url: string;
      status: string;
      error_code: number;
    }>;
  };
  domain_insight?: {
    metrics?: {
      spam_score?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      page_authority?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      domain_authority?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
  };
  friendly_links?: {
    metrics?: {
      friendly_links_percentage?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      analyzed_text_links_on_page?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      unfriendly_links_percentage?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      unfriendly_links_count_sample?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
    unfriendly_links_samples?: Array<{
      url: string;
      anchor_text: string;
      readability_score: number;
    }>;
    readability_analysis_summary?: string;
  };
  backlinks_detail?: {
    metrics?: {
      total?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      dofollow_links?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      nofollow_links?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      unique_domain_count?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
    is_premium_user?: boolean;
    upgrade_message?: string;
    top_backlinks_sample?: Array<{
      anchor: string;
      dofollow: boolean;
      url_from: string;
      spam_score: number;
      domain_from: string;
      page_authority: number;
      domain_authority: number;
    }>;
  };
  overall_backlinks?: {
    metrics?: {
      total_backlinks?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      domain_authority?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      broken_links_count?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
      friendly_links_percentage?: {
        value: number;
        importance?: string;
        description?: string;
        recommendation?: string;
      };
    };
    overall_title?: string;
    overall_description?: string;
  };
};

export type ActiveSectionType = 
  | "overall_backlinks"
  | "backlinks_detail"
  | "domain_insight"
  | "friendly_links"
  | "broken_links";

export type TabContentProps = {
  data: LinksAnalysisData;
  activeSection: ActiveSectionType;
};

export type MetricInfo = {
  importance?: string;
  description?: string;
  recommendation?: string;
};
