import React from "react";

type Props = {
  icon: React.ReactNode;
  value: string;
  label: string;
};

export default function StateCard({ icon, label, value }: Props) {
  return (
    <div className="rounded-lg p-2 sm:p-3 lg:p-4 border border-light-gray">
      <div className="flex items-center justify-center gap-1 sm:gap-2 text-secondary mb-2 sm:mb-3 lg:mb-4">
        <div className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 flex items-center justify-center">
          {icon}
        </div>
        <span className="font-semibold text-sm sm:text-base">{value}</span>
      </div>
      <div className="text-center text-xs sm:text-sm text-secondary/60">
        {label}
      </div>
    </div>
  );
}
