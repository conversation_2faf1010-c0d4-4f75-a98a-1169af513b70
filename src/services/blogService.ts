// Blog service implemented with fetch to leverage Next.js caching
// All functions accept an optional revalidate parameter; when omitted, build-time SSG caching is used
const API_BASE = process.env.NEXT_PUBLIC_API_URL || "https://seoanalyser.com.au";

// Blog post types
export interface BlogPost {
  id: number;
  title: string;
  slug: string;
  author: {
    id: number;
    email: string;
    display_name: string;
  };
  body: string;
  publish_timestamp: number;
  status: string;
  snippet: string;
  cover_image: string | null;
  url: string;
  tags: string[];
  similar_posts: {
    id: number;
    title: string;
    slug: string;
    author: {
      id: number;
      email: string;
      display_name: string;
    };
    publish_timestamp: number;
    tags: string[];
    url?: string;
  }[];
}

export interface BlogPostResult {
  id: number;
  title: string;
  slug: string;
  author: {
    id: number;
    email: string;
    display_name: string;
  };
  body: string;
  publish_timestamp: number;
  status: string;
  snippet: string;
  cover_image: string | null;
  url: string;
  tags: string[];
  similar_posts: Array<{
    id: number;
    title: string;
    slug: string;
    author: {
      id: number;
      email: string;
      display_name: string;
    };
    publish_timestamp: number;
    tags: string[];
  }>;
}

export interface BlogApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BlogPostResult[];
  categories: Category[];
}

export interface Category {
  name: string;
  slug: string;
}

export interface SearchResponse {
  query: string;
  results: SearchResult[];
  count: number;
  categories: Category[];
}

export interface SearchResult {
  id: number;
  title: string;
  slug: string;
  category: {
    name: string;
    slug: string;
  };
  author: string;
  publish_timestamp: number;
  snippet: string;
  body_json?: {
    text: string;
    blocks: unknown[];
  };
  body?: string;
  cover_image?: string;
  tags: string[];
  url: string;
}

/**
 * Get blog post by slug
 * @param slug The blog post slug
 * @returns Promise with the blog post data
 */
export async function getBlogPostBySlug(
  slug: string,
  revalidateSeconds?: number
): Promise<BlogPost> {
  try {
    const options: RequestInit & { next?: { revalidate?: number } } = {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    };
    if (typeof revalidateSeconds === "number") {
      options.next = { revalidate: revalidateSeconds };
    }
    const res = await fetch(`${API_BASE}/api/blog/posts/${slug}/`, options);
    if (!res.ok) {
      throw new Error(`Failed to fetch blog post: ${res.status}`);
    }
    return (await res.json()) as BlogPost;
  } catch (error) {
    throw error instanceof Error ? error : new Error("Error fetching blog post");
  }
}

/**
 * Get blog posts with pagination and filters
 * @param params Query parameters for filtering and pagination
 * @returns Promise with the blog posts data
 */
export async function getBlogPosts(
  params?: { page?: number; category?: string; q?: string },
  revalidateSeconds?: number
): Promise<BlogApiResponse> {
  try {
    const usp = new URLSearchParams();
    if (params?.page) usp.set("page", String(params.page));
    if (params?.category) usp.set("category", params.category);
    if (params?.q) usp.set("q", params.q);
    const qs = usp.toString();
    const url = `${API_BASE}/api/blog/posts/${qs ? `?${qs}` : ""}`;
    const options: RequestInit & { next?: { revalidate?: number } } = {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    };
    if (typeof revalidateSeconds === "number") {
      options.next = { revalidate: revalidateSeconds };
    }
    const res = await fetch(url, options);
    if (!res.ok) {
      throw new Error(`Failed to fetch blog posts: ${res.status}`);
    }
    return (await res.json()) as BlogApiResponse;
  } catch (error) {
    throw error instanceof Error ? error : new Error("Error fetching blog posts");
  }
}

/**
 * Search blog posts
 * @param query Search query
 * @param page Page number for pagination
 * @returns Promise with the search results
 */
export async function searchBlogPosts(
  query: string,
  page?: number,
  revalidateSeconds?: number
): Promise<SearchResponse> {
  try {
    const usp = new URLSearchParams();
    usp.set("q", query);
    if (page) usp.set("page", String(page));
    const options: RequestInit & { next?: { revalidate?: number } } = {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    };
    if (typeof revalidateSeconds === "number") {
      options.next = { revalidate: revalidateSeconds };
    }
    const res = await fetch(`${API_BASE}/api/blog/search/?${usp.toString()}`,
      options
    );
    if (!res.ok) {
      throw new Error(`Failed to search blog posts: ${res.status}`);
    }
    return (await res.json()) as SearchResponse;
  } catch (error) {
    throw error instanceof Error ? error : new Error("Error searching blog posts");
  }
}

/**
 * Search blog posts by tag
 * @param tag Tag to search for
 * @param page Page number for pagination
 * @returns Promise with the search results
 */
export async function searchBlogPostsByTag(
  tag: string,
  page?: number,
  revalidateSeconds?: number
): Promise<SearchResponse> {
  try {
    const usp = new URLSearchParams();
    usp.set("q", tag);
    if (page) usp.set("page", String(page));
    const options: RequestInit & { next?: { revalidate?: number } } = {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    };
    if (typeof revalidateSeconds === "number") {
      options.next = { revalidate: revalidateSeconds };
    }
    const res = await fetch(`${API_BASE}/api/blog/search/?${usp.toString()}`,
      options
    );
    if (!res.ok) {
      throw new Error(`Failed to search blog posts by tag: ${res.status}`);
    }
    return (await res.json()) as SearchResponse;
  } catch (error) {
    throw error instanceof Error
      ? error
      : new Error("Error searching blog posts by tag");
  }
}

export async function getCategories(
  revalidateSeconds?: number
): Promise<Category[]> {
  const url = `${API_BASE}/api/blog/categories/`;
  const options: RequestInit & { next?: { revalidate?: number } } = {
    method: "GET",
    headers: { "Content-Type": "application/json" },
  };
  if (typeof revalidateSeconds === "number") {
    options.next = { revalidate: revalidateSeconds };
  }
  const res = await fetch(url, options);
  if (!res.ok) throw new Error(`Failed to fetch categories: ${res.status}`);
  const data = await res.json();
  return data.categories || [];
}

/**
 * Get posts for a specific category (preferred dedicated endpoint)
 */
export async function getCategoryPosts(
  slug: string,
  page?: number,
  revalidateSeconds?: number
): Promise<BlogApiResponse> {
  const usp = new URLSearchParams();
  if (page) usp.set("page", String(page));
  const query = usp.toString();
  const url = `${API_BASE}/api/blog/category/${encodeURIComponent(slug)}/${
    query ? `?${query}` : ""
  }`;
  const options: RequestInit & { next?: { revalidate?: number } } = {
    method: "GET",
    headers: { "Content-Type": "application/json" },
  };
  if (typeof revalidateSeconds === "number") {
    options.next = { revalidate: revalidateSeconds };
  }
  const res = await fetch(url, options);
  if (!res.ok) {
    throw new Error(`Failed to fetch category posts: ${res.status}`);
  }
  return (await res.json()) as BlogApiResponse;
}

const blogService = {
  getBlogPostBySlug,
  getBlogPosts,
  searchBlogPosts,
  searchBlogPostsByTag,
  getCategories,
  getCategoryPosts,
};

export default blogService;
