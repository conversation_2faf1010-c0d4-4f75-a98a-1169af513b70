"use client";
import { useState, useEffect, useCallback } from "react";
import Modal from "@/ui/Modal";
import Image from "next/image";
import LoginForm from "./LoginForm";
import RegisterForm from "./RegisterForm";
import OtpVerificationForm from "./OtpVerificationForm";
import StepIndicator from "./StepIndicator";
import StepProgressBar from "@/ui/StepProgressBar";
import { InfoIcon } from "@/ui/icons/general";
import { useAuthStore } from "@/store/authStore";
import profileService from "@/services/profileService";

type AuthModalsProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  actionType?: "pdf" | "share" | "whiteLabel" | "general";
  showSteps?: boolean;
};

export type AuthStep =
  | "login-register"
  | "login"
  | "register"
  | "otp"
  | "success";

export default function AuthModals({
  isOpen,
  onClose,
  onSuccess,
  actionType = "general",
  showSteps = true,
}: AuthModalsProps) {
  const [step, setStep] = useState<AuthStep>("login-register");
  const [email, setEmail] = useState("");
  const [flowType, setFlowType] = useState<"login" | "register">("register");

  // State to track authentication status
  const [isUserAuthenticated, setIsUserAuthenticated] = useState(false);
  // Add loading state to track when authentication check is in progress
  const [isAuthCheckLoading, setIsAuthCheckLoading] = useState(false);
  // State to control when the modal should actually be shown
  const [shouldShowModal, setShouldShowModal] = useState(false);

  // Form state management
  const [shouldPreventModalClose, setShouldPreventModalClose] = useState<(() => boolean) | null>(null);

  // Handle form state changes from child components
  const handleFormStateChange = useCallback((preventCloseFn: () => boolean) => {
    setShouldPreventModalClose(() => preventCloseFn);
  }, []);

  // Get auth state and actions from the store
  const { isAuthenticated, checkAuth } = useAuthStore();

  // Check if user is already logged in (async function)
  const checkLogin = useCallback(async (): Promise<boolean> => {
    // Use the cached auth state if available, otherwise check with server
    if (isAuthenticated) {
      setIsUserAuthenticated(true);
      return true;
    }

    // If not authenticated in cached state, check with the server
    const isAuth = await checkAuth();
    setIsUserAuthenticated(isAuth);
    return isAuth;
  }, [isAuthenticated, checkAuth, setIsUserAuthenticated]);

  // Effect to check login status when modal opens
  useEffect(() => {
    if (isOpen) {
      // Set initial loading state
      setIsAuthCheckLoading(true);
      setShouldShowModal(false);

      // If already authenticated in the store, skip the modal
      if (isAuthenticated) {
        onSuccess();
        setShouldShowModal(false);
        setIsAuthCheckLoading(false);
        return;
      }

      const checkAuthStatus = async () => {
        try {
          // Only check with the server if not already authenticated in the store
          const isAuth = await checkLogin();
          if (isAuth) {
            // User is already logged in, call onSuccess directly and don't show the modal
            onSuccess();
            setShouldShowModal(false);
            setIsAuthCheckLoading(false);
            return; // Exit early to prevent any modal from showing
          } else {
            // User is not logged in, show login/register options
            setStep("login-register");
            setShouldShowModal(true);
          }
        } catch (error) {
          // If there's an error, default to not authenticated
          setIsUserAuthenticated(false);
          setStep("login-register");
          setShouldShowModal(true);
        } finally {
          // Authentication check is complete
          setIsAuthCheckLoading(false);
        }
      };

      checkAuthStatus();
    } else {
      // Reset when modal is closed
      setShouldShowModal(false);
    }
  }, [isOpen, onSuccess, isAuthenticated, actionType, checkLogin]);

  // Define steps for each flow - with all registration/login processes inside Authentication step
  const registerSteps = [
    { number: 1, label: "Authentication" },
    {
      number: 2,
      label:
        actionType === "pdf"
          ? "Download"
          : actionType === "whiteLabel"
          ? "White Label Setting"
          : "Share",
    },
    ...(actionType === "whiteLabel"
      ? [
          { number: 3, label: "Billing" },
          { number: 4, label: "Payment" },
          { number: 5, label: "Download" },
        ]
      : []),
  ];

  const loginSteps = [
    { number: 1, label: "Authentication" },
    {
      number: 2,
      label:
        actionType === "pdf"
          ? "Download"
          : actionType === "whiteLabel"
          ? "White Label Setting"
          : "Share",
    },
    ...(actionType === "whiteLabel"
      ? [
          { number: 3, label: "Billing" },
          { number: 4, label: "Payment" },
          { number: 5, label: "Download" },
        ]
      : []),
  ];

  // Get current step number based on the auth step
  const getCurrentStepNumber = () => {
    // All authentication steps (login-register, register, login, otp) are part of step 1
    if (step === "success") {
      return 2; // Success means we're on the next step (Download/Share/White Label)
    } else {
      return 1; // All other steps are part of Authentication
    }
  };

  const handleSuccess = () => {
    // For registration flow, we don't want to call the success callback
    // since we're showing a different success screen
    if (flowType === "register" && step === "success") {
      return;
    }

    // Check if user has an active white label subscription
    const checkSubscription = async () => {
      try {
        // Use the store's authentication state
        if (isAuthenticated) {
          // If authenticated and action type is white label, check for active subscription
          if (actionType === "whiteLabel") {
            const hasSubscription =
              await profileService.hasActiveProPlanSubscription();

            // This will be used by the WhiteLabelModal component to skip billing and payment steps
            // The subscription check is done again in the WhiteLabelModal component
          }
        } else {
          // If not authenticated in the store, try to check auth once more
          const isAuth = await checkAuth(true); // Force refresh

          if (!isAuth) {
            setStep("login-register");
            setFlowType("register");
          }
        }
      } catch (error) {
        // Error handled silently
      }
        // If there's an error, default to not authenticated
        setIsUserAuthenticated(false);
        setStep("login-register");
        setFlowType("register");
      } finally {
        // Ensure modal is closed properly
        setShouldShowModal(false);
      }
    };

    // Call the success callback for non-registration flows
    // or for login flows that have completed
    if (flowType !== "register" || step !== "success") {
      onSuccess();
    }

    // Reset state after successful authentication
    setTimeout(() => {
      checkSubscription();
    }, 500);
  };

  const renderModalContent = () => {
    switch (step) {
      case "login-register":
        return {
          title: "Authentication Required",
          children: (
            <div className="flex flex-col h-full p-4 lg:p-6">
              <div className="flex-1 flex flex-col items-center justify-center">
                <div className="w-14 h-14 md:w-16 md:h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4 md:mb-6">
                  <InfoIcon className="text-purple-600 w-8 h-8 md:w-10 md:h-10" />
                </div>
                <h2 className="text-xl md:text-2xl font-bold text-secondary text-center mb-2 md:mb-3">
                  Authentication Required
                </h2>
                <p className="text-sm md:text-base text-secondary/70 text-center mb-6 md:mb-8 max-w-md px-1">
                  {actionType === "pdf"
                    ? "Please login or create an account to download the PDF report with detailed SEO analysis."
                    : actionType === "share"
                    ? "Please login or create an account to share this SEO analysis report with others."
                    : actionType === "whiteLabel"
                    ? "Please login or create an account to access White Label features."
                    : "Please login or create an account to continue."}
                </p>
                <div className="flex flex-col w-full max-w-sm gap-3 md:gap-4">
                  <button
                    className="btn btn--primary !w-full !py-3 md:!py-4 !text-base md:!text-lg flex items-center justify-center gap-2 transition-all hover:shadow-lg"
                    onClick={() => {
                      setFlowType("login");
                      setStep("login");
                    }}
                  >
                    <span>Login to Your Account</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-4 h-4 md:w-5 md:h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M13.8 12H3" />
                    </svg>
                  </button>
                  <button
                    className="btn btn--outline !w-full !py-3 md:!py-4 !text-base md:!text-lg flex items-center justify-center gap-2 transition-all hover:shadow-lg"
                    onClick={() => {
                      setFlowType("register");
                      setStep("register");
                    }}
                  >
                    <span>Create New Account</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-4 h-4 md:w-5 md:h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="8.5" cy="7" r="4"></circle>
                      <line x1="20" y1="8" x2="20" y2="14"></line>
                      <line x1="23" y1="11" x2="17" y2="11"></line>
                    </svg>
                  </button>
                </div>
              </div>
              <div className="mt-auto pt-4 md:pt-6 text-center text-[10px] md:text-xs text-secondary/50">
                Your information is secure and will only be used to enhance your
                experience.
              </div>
            </div>
          ),
        };

      case "login":
        return {
          title: "Login to Your Account",
          children: (
            <LoginForm
              onSuccess={handleSuccess}
              onRegisterClick={() => {
                setFlowType("register");
                setStep("register");
              }}
              onFormStateChange={handleFormStateChange}
            />
          ),
        };

      case "register":
        return {
          title: "Create New Account",
          children: (
            <RegisterForm
              onSuccess={(userEmail: string) => {
                setEmail(userEmail);
                setStep("otp");
              }}
              onLoginClick={() => {
                setFlowType("login");
                setStep("login");
              }}
              onFormStateChange={handleFormStateChange}
            />
          ),
        };

      case "otp":
        return {
          title: "Verify Your Account",
          children: (
            <OtpVerificationForm
              email={email}
              onSuccess={() => setStep("success")}
              onResendCode={() => {
                // This is just a callback for any additional handling needed after resend
                // The actual resend API call is handled in the OtpVerificationForm component
              }}
            />
          ),
        };

      case "success":
        return {
          title: "Success",
          children: (
            <div className="p-4 lg:p-6 flex-1 flex flex-col h-full">
              <div className="flex-1 flex flex-col items-center justify-center">
                <div className="w-14 h-14 md:w-16 md:h-16 bg-primary-green/10 rounded-full flex items-center justify-center mb-4 md:mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-7 h-7 md:w-8 md:h-8 text-primary-green"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                </div>
                <h2 className="text-xl md:text-2xl font-bold text-secondary text-center mb-2 md:mb-3">
                  Verification Successful
                </h2>
                <p className="text-sm md:text-base text-secondary/70 text-center mb-6 md:mb-8 max-w-md px-1">
                  {flowType === "register"
                    ? "Your account has been successfully created and verified. You can now access all features of the platform."
                    : actionType === "pdf"
                    ? "Your account has been successfully verified. You can now download the PDF report with detailed SEO analysis."
                    : actionType === "share"
                    ? "Your account has been successfully verified. You can now share this report with detailed SEO analysis."
                    : actionType === "whiteLabel"
                    ? "Your account has been successfully verified. You can now access White Label features."
                    : "Your account has been successfully verified. You can now continue."}
                </p>

                {/* Only show the continue button if not coming from registration flow */}
                {flowType !== "register" && (
                  <div className="w-full max-w-sm">
                    <button
                      onClick={handleSuccess}
                      className="btn btn--primary !w-full !py-3 md:!py-4 !text-base md:!text-lg flex items-center justify-center gap-2 transition-all hover:shadow-lg"
                    >
                      {actionType === "pdf" ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-4 h-4 md:w-5 md:h-5"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="7 10 12 15 17 10"></polyline>
                          <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                      ) : actionType === "share" ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-4 h-4 md:w-5 md:h-5"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <circle cx="18" cy="5" r="3"></circle>
                          <circle cx="6" cy="12" r="3"></circle>
                          <circle cx="18" cy="19" r="3"></circle>
                          <line
                            x1="8.59"
                            y1="13.51"
                            x2="15.42"
                            y2="17.49"
                          ></line>
                          <line
                            x1="15.41"
                            y1="6.51"
                            x2="8.59"
                            y2="10.49"
                          ></line>
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-4 h-4 md:w-5 md:h-5"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                          <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                      )}
                      {actionType === "pdf"
                        ? "Continue to PDF"
                        : actionType === "share"
                        ? "Share Report"
                        : actionType === "whiteLabel"
                        ? "Continue to White Label"
                        : "Continue"}
                    </button>
                  </div>
                )}

                {/* Show a different button for registration flow */}
                {flowType === "register" && (
                  <div className="w-full max-w-sm">
                    <button
                      onClick={() => onClose()}
                      className="btn btn--primary !w-full !py-3 md:!py-4 !text-base md:!text-lg flex items-center justify-center gap-2 transition-all hover:shadow-lg"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-4 h-4 md:w-5 md:h-5"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M5 12h14"></path>
                        <path d="M12 5l7 7-7 7"></path>
                      </svg>
                      Get Started
                    </button>
                  </div>
                )}
              </div>
            </div>
          ),
        };

      default:
        return {
          title: "Error",
          children: <div>Something went wrong</div>,
        };
    }
  };

  const handleClose = () => {
    // If we're still loading, we should cancel the loading state
    if (isAuthCheckLoading) {
      setIsAuthCheckLoading(false);
    }

    onClose();

    // Reset state after modal is closed
    setTimeout(() => {
      // Don't reset to login-register if user is logged in
      if (!isUserAuthenticated) {
        setStep("login-register");
        setFlowType("register");
      }
      // Reset the shouldShowModal state
      setShouldShowModal(false);
    }, 500);
  };

  // Loading indicator for when authentication check is in progress
  if (isOpen && isAuthCheckLoading) {
    return (
      <Modal
        open={true}
        onClose={handleClose}
        title="Loading"
        size="xl"
        shouldPreventClose={shouldPreventModalClose || undefined}
      >
        <div className="flex flex-col items-center justify-center p-8">
          <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-secondary text-center">
            Checking authentication status...
          </p>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      open={isOpen && shouldShowModal}
      onClose={handleClose}
      title={renderModalContent().title}
      size="xl"
      shouldPreventClose={shouldPreventModalClose || undefined}
    >
      <div className=" flex flex-col">
        {/* Only show step indicator when showSteps is true and not on the initial choice screen */}
        {showSteps && step !== "login-register" && (
          <div className="px-3 pt-3 lg:px-6 lg:pt-6">
            {/* Use the new StepProgressBar for a more modern look */}
            <StepProgressBar
              steps={
                isUserAuthenticated
                  ? [
                      {
                        id: "success",
                        label:
                          actionType === "pdf"
                            ? "Download"
                            : actionType === "share"
                            ? "Share"
                            : actionType === "whiteLabel"
                            ? "White Label"
                            : "Success",
                      },
                    ]
                  : flowType === "register"
                  ? registerSteps.map((step) => ({
                      id: step.number.toString(),
                      label: step.label,
                    }))
                  : loginSteps.map((step) => ({
                      id: step.number.toString(),
                      label: step.label,
                    }))
              }
              currentStepId={
                isUserAuthenticated
                  ? "success"
                  : getCurrentStepNumber().toString()
              }
              className="mb-4"
            />
          </div>
        )}
        {renderModalContent().children}
      </div>
    </Modal>
  );
}
