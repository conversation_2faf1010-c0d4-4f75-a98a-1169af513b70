import { <PERSON>ada<PERSON> } from "next";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { permanentRedirect } from "next/navigation";

import blogService from "@/services/blogService";
import { ArrowRigthIcon } from "@/ui/icons/navigation";

import BlogPic from "../../blog.jpg";
import DateHolder from "../../components/DateHolder";
import MetaTags from "./MetaTags";
import "../../blogStyle.css";

// Helper to sanitize a URL segment (category/slug)
function sanitizeSegment(segment: string): string {
  if (!segment) return "";
  // Drop anything after the first comma (common corruption pattern)
  let s: string = (segment.split(",")[0] ?? "");
  // Take the last path segment if any slashes slipped in
  const last = s.split("/").filter(Boolean).pop();
  s = (last ?? s ?? "");
  // Trim whitespace
  s = (s ?? "").trim();
  // Strip common trailing punctuation and query/hash if present
  s = (s.split(/[?#]/)[0] ?? "");
  return s;
}

// Revalidate static HTML for blog post pages every 10 minutes
export const revalidate = 600;

// Prebuild all blog post pages at build time
export async function generateStaticParams(): Promise<
  Array<{ category: string; slug: string }>
> {
  const params: Array<{ category: string; slug: string }> = [];
  try {
    const categories = await blogService.getCategories();
    for (const cat of categories) {
      let page = 1;
      let next: string | null = null;
      do {
        const data = await blogService.getBlogPosts({ page, category: cat.slug }, 600);
        (data.results || []).forEach((post) => {
          params.push({ category: cat.slug, slug: post.slug });
        });
        next = data.next;
        page += 1;
      } while (next);
    }
  } catch (_) {
    // If prebuild fetching fails, return empty so Next can fallback
  }
  return params;
}

// Define the props for the page component
interface BlogPostPageProps {
  params: Promise<{
    category: string;
    slug: string;
  }>;
}

// Generate metadata for the page
export async function generateMetadata({
  params,
}: BlogPostPageProps): Promise<Metadata> {
  try {
    const { category, slug } = await params;
    const blogPost = await blogService.getBlogPostBySlug(slug, 600);

    // Derive the actual category slug from the API response URL, if available
    let actualCategory = category;
    let canonicalSlug = slug;
    if (blogPost.url) {
      const parts = blogPost.url.split("/").filter(Boolean);
      const blogIdx = parts.findIndex((p) => p.toLowerCase() === "blog");
      if (blogIdx >= 0) {
        if (parts.length > blogIdx + 1) {
          actualCategory = (parts[blogIdx + 1] || category).trim();
        }
        if (parts.length > blogIdx + 2) {
          canonicalSlug = (parts[blogIdx + 2] || canonicalSlug).trim();
        }
      }
    }

    // Final sanitation of derived values
    actualCategory = sanitizeSegment(actualCategory);
    canonicalSlug = sanitizeSegment(canonicalSlug);

    // If the URL category does not match the post's actual category, return safe fallback metadata
    if (decodeURIComponent(category).toLowerCase() !== decodeURIComponent(actualCategory).toLowerCase()) {
      return {
        title: "Blog Post | SEO Analyser",
        description:
          "Read our latest blog post on SEO tips and strategies to improve your website's search engine optimization and rankings.",
        alternates: {
          canonical: "https://seoanalyser.com.au/blog",
        },
      };
    }

    // Enhanced meta description generation logic
    let description = "";
    
    if (blogPost.snippet) {
      // Use snippet if available, ensure it's properly formatted
      description = blogPost.snippet.trim();
      if (description.length > 160) {
        description = description.substring(0, 157) + "...";
      }
    } else if (blogPost.body) {
      // Extract meaningful content from body
      const cleanText = blogPost.body
        .replace(/<[^>]*>?/gm, "") // Remove HTML tags
        .replace(/\s+/g, " ") // Normalize whitespace
        .trim();
      
      // Try to end at a sentence boundary within 160 characters
      if (cleanText.length <= 160) {
        description = cleanText;
      } else {
        const truncated = cleanText.substring(0, 157);
        const lastSentence = truncated.lastIndexOf(".");
        const lastSpace = truncated.lastIndexOf(" ");
        
        if (lastSentence > 100) {
          description = cleanText.substring(0, lastSentence + 1);
        } else if (lastSpace > 100) {
          description = cleanText.substring(0, lastSpace) + "...";
        } else {
          description = truncated + "...";
        }
      }
    }

    // Ensure we always have a description
    if (!description) {
      description = `Read our latest blog post about ${blogPost.title.toLowerCase()} on SEO Analyser. Get expert insights and tips to improve your website's search engine optimization.`;
    }

    // Generate canonical URL
    const canonicalUrl = `https://seoanalyser.com.au/blog/${encodeURIComponent(category)}/${encodeURIComponent(slug)}`;
    
    // Prepare Open Graph/Twitter image
    // - Ensure absolute URL
    // - Avoid SVG for Twitter cards (use a JPG fallback)
    const siteUrl = "https://seoanalyser.com.au";
    let ogImage = blogPost.cover_image || "/blog-default.jpg"; // public/blog-default.jpg
    if (ogImage.toLowerCase().endsWith(".svg")) {
      ogImage = "/blog-default.jpg";
    }
    if (!ogImage.startsWith("http")) {
      ogImage = `${siteUrl}${ogImage.startsWith("/") ? "" : "/"}${ogImage}`;
    }
    
    // Generate keywords from tags and title
    const keywords = [
      ...blogPost.tags,
      "SEO",
      "SEO Analyser",
      "blog",
      ...blogPost.title.toLowerCase().split(" ").filter(word => word.length > 3)
    ].slice(0, 10); // Limit to 10 keywords

    // Format dates
    const publishDate = new Date(blogPost.publish_timestamp * 1000);
    const publishedTime = publishDate.toISOString();
    
    return {
      title: `${blogPost.title} | SEO Analyser Blog`,
      description: description,
      keywords: keywords,
      authors: [{ 
        name: blogPost.author.display_name,
        url: `https://seoanalyser.com.au/author/${blogPost.author.id}`
      }],
      creator: blogPost.author.display_name,
      publisher: "SEO Analyser",
      alternates: {
        canonical: canonicalUrl,
        languages: {
          "en-AU": canonicalUrl,
        },
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          "max-video-preview": -1,
          "max-image-preview": "large",
          "max-snippet": -1,
        },
      },
      openGraph: {
        title: blogPost.title,
        description: description,
        type: "article",
        url: canonicalUrl,
        siteName: "SEO Analyser",
        locale: "en_AU",
        publishedTime: publishedTime,
        modifiedTime: publishedTime,
        authors: [blogPost.author.display_name],
        tags: blogPost.tags,
        section: decodeURIComponent(category),
        images: [
          {
            url: ogImage,
            width: 1200,
            height: 630,
            alt: `${blogPost.title} - SEO Analyser Blog`,
            // type intentionally omitted as source may be PNG/JPEG
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        site: "@SEO_ANALYSER",
        creator: "@SEO_ANALYSER",
        title: blogPost.title,
        description: description,
        images: [ogImage],
      },
      other: {
        "og:locale": "en_AU",
        "og:published_time": publishedTime,
        "og:updated_time": publishedTime,
        "article:author": blogPost.author.display_name,
        "article:published_time": publishedTime,
        "article:modified_time": publishedTime,
        "article:section": decodeURIComponent(category),
        "article:tag": blogPost.tags.join(", "),
      },
    };
  } catch (_error) {
    return {
      title: "Blog Post | SEO Analyser",
      description: "Read our latest blog post on SEO tips and strategies to improve your website's search engine optimization and rankings.",
      alternates: {
        canonical: "https://seoanalyser.com.au/blog",
      },
    };
  }
}

// The main page component
export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { category, slug } = await params;
  const blogPost = await blogService.getBlogPostBySlug(slug, 600);

    // Derive the actual category and canonical slug from the API response URL, if available
    let actualCategory = category;
    let canonicalSlug = blogPost.slug?.trim() || slug;
    if (blogPost.url) {
      const parts = blogPost.url.split("/").filter(Boolean);
      const blogIdx = parts.findIndex((p) => p.toLowerCase() === "blog");
      if (blogIdx >= 0) {
        if (parts.length > blogIdx + 1) {
          actualCategory = (parts[blogIdx + 1] || category).trim();
        }
        if (parts.length > blogIdx + 2) {
          canonicalSlug = (parts[blogIdx + 2] || canonicalSlug).trim();
        }
      }
    }

    // If mismatch between URL category and post's actual category, 301 redirect to canonical URL
    if (decodeURIComponent(category).toLowerCase() !== decodeURIComponent(actualCategory).toLowerCase()) {
      // Build target from canonical components to avoid duplicated/garbled paths
      const target = `/blog/${encodeURIComponent(actualCategory)}/${encodeURIComponent(canonicalSlug)}`;
      permanentRedirect(target);
    }

    // Generate meta description for client-side injection
    let description = "";
    
    if (blogPost.snippet) {
      description = blogPost.snippet.trim();
      if (description.length > 160) {
        description = description.substring(0, 157) + "...";
      }
    } else if (blogPost.body) {
      const cleanText = blogPost.body
        .replace(/<[^>]*>?/gm, "")
        .replace(/\s+/g, " ")
        .trim();
      
      if (cleanText.length <= 160) {
        description = cleanText;
      } else {
        const truncated = cleanText.substring(0, 157);
        const lastSentence = truncated.lastIndexOf(".");
        const lastSpace = truncated.lastIndexOf(" ");
        
        if (lastSentence > 100) {
          description = cleanText.substring(0, lastSentence + 1);
        } else if (lastSpace > 100) {
          description = cleanText.substring(0, lastSpace) + "...";
        } else {
          description = truncated + "...";
        }
      }
    }

    if (!description) {
      description = `Read our latest blog post about ${blogPost.title.toLowerCase()} on SEO Analyser. Get expert insights and tips to improve your website's search engine optimization.`;
    }

    const canonicalUrl = `https://seoanalyser.com.au/blog/${encodeURIComponent(actualCategory)}/${encodeURIComponent(canonicalSlug)}`;
    const title = `${blogPost.title} | SEO Analyser Blog`;

    // Prepare absolute Open Graph image URL for client-side structured data
    const siteUrl = "https://seoanalyser.com.au";
    let ogImageClient = blogPost.cover_image || "/blog-default.jpg";
    if (ogImageClient.toLowerCase().endsWith(".svg")) {
      ogImageClient = "/blog-default.jpg";
    }
    if (!ogImageClient.startsWith("http")) {
      ogImageClient = `${siteUrl}${ogImageClient.startsWith("/") ? "" : "/"}${ogImageClient}`;
    }

    // Generate structured data for the article
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "BlogPosting",
      headline: blogPost.title,
      description: description,
      image: ogImageClient,
      author: {
        "@type": "Person",
        name: blogPost.author.display_name,
        url: `https://seoanalyser.com.au/author/${blogPost.author.id}`,
      },
      publisher: {
        "@type": "Organization",
        name: "SEO Analyser",
        logo: {
          "@type": "ImageObject",
          url: "https://seoanalyser.com.au/images/appLogo.svg",
          width: 600,
          height: 60,
        },
      },
      datePublished: new Date(blogPost.publish_timestamp * 1000).toISOString(),
      dateModified: new Date(blogPost.publish_timestamp * 1000).toISOString(),
      mainEntityOfPage: {
        "@type": "WebPage",
        "@id": canonicalUrl,
      },
      articleSection: decodeURIComponent(category),
      keywords: blogPost.tags.join(", "),
      wordCount: blogPost.body?.replace(/<[^>]*>?/gm, "").trim().split(/\s+/).length || 0,
      inLanguage: "en-AU",
      url: canonicalUrl,
    };

    // Generate Breadcrumb structured data
    const breadcrumbStructuredData = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Blog",
          item: "https://seoanalyser.com.au/blog",
        },
        {
          "@type": "ListItem",
          position: 2,
          name: decodeURIComponent(category),
          item: `https://seoanalyser.com.au/blog/${encodeURIComponent(category)}`,
        },
        {
          "@type": "ListItem",
          position: 3,
          name: blogPost.title,
          item: canonicalUrl,
        },
      ],
    };

    // Generate author structured data
    const authorStructuredData = {
      "@context": "https://schema.org",
      "@type": "Person",
      name: blogPost.author.display_name,
      url: `https://seoanalyser.com.au/author/${blogPost.author.id}`,
      worksFor: {
        "@type": "Organization",
        name: "SEO Analyser",
      },
    };

    return (
      <>
        {/* Client-side meta tag injection */}
        <MetaTags description={description} title={title} canonicalUrl={canonicalUrl} />
        
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(authorStructuredData),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(breadcrumbStructuredData),
          }}
        />
        
        <div className="w-full lg:mt-[84px] bg-white py-6 pt-8 rounded-2xl lg:px-2 mt-8 mb-16 container flex justify-center blog-single-page">
          <div className="flex flex-col w-full gap-6 max-w-5xl">
            {/* Back to Blog Button - Positioned at the top for better UX */}
            <div className="flex justify-start">
              <Link
                href="/blog"
                className="bg-primary text-white hover:bg-primary/85 px-4 py-2 rounded-lg flex flex-row hover:shadow-md justify-center items-center"
              >
                <ArrowRigthIcon className="rotate-180 w-6 h-6 transition-transform duration-300 group-hover:-translate-x-3" />
                Back to Blog
              </Link>
            </div>

            {/* Main content */}
            <div className="lg:col-span-3 font-semibold">
              {/* Breadcrumb Navigation */}
              <div className="mb-6">
                <div className="flex flex-wrap gap-2 text-xs lg:text-base mb-4">
                  <Link
                    href="/blog"
                    className="text-primary hover:underline inline-block transition-colors duration-200"
                  >
                    Blog
                  </Link>
                  <span className="text-gray-400">/</span>
                  <Link
                    href={`/blog/${category}`}
                    className="text-primary hover:underline inline-block capitalize transition-colors duration-200"
                  >
                    {decodeURIComponent(category)}
                  </Link>
                  <span className="text-gray-400">/</span>
                  <span className="text-gray-600 truncate">{blogPost.title}</span>
                </div>
                <div className="mt-6 ">
                  <h1 className="blogTitle   ">{blogPost.title}</h1>
                </div>
              </div>

              {/* Blog thumbnail */}
              <div className="overflow-hidden rounded-2xl relative w-full aspect-[1138/490] mb-6">
                <Image
                  src={blogPost.cover_image || BlogPic}
                  alt={blogPost.title}
                  width={1138}
                  height={490}
                  className="w-full h-full object-contain object-center rounded-2xl"
                  unoptimized={blogPost.cover_image ? true : false}
                />
                <div className="p-6 absolute top-0 left-0 w-full h-full bg-gradient-to-b from-[#00000000] to-[#00000080] rounded-2xl">
                  <div className="flex items-start gap-2 sm:gap-3 md:gap-4 justify-between">
                    <DateHolder date={blogPost.publish_timestamp} />
                    <div className="bg-white flex flex-col gap-2 rounded-lg px-4 py-2 text-[10px] lg:text-[12px]">
                      <div>
                        Written by <b>{blogPost.author.display_name}</b>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Blog content */}
              <article
                className="ck-content prose prose-lg max-w-none text-secondary prose-headings:text-secondary prose-headings:font-bold prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-img:rounded-lg leading-relaxed pb-4"
                dangerouslySetInnerHTML={{ __html: blogPost.body }}
              ></article>

              {/* Similar posts */}
              {blogPost.similar_posts && blogPost.similar_posts.length > 0 && (
                <div className="mt-12">
                  <h2 className="text-secondary font-black text-xl mb-6">
                    Similar Posts
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {blogPost.similar_posts.map((similarPost) => {
                      // Extract category from the URL if available
                      let postCategory = category;

                      if (similarPost.url) {
                        const urlParts = similarPost.url
                          .split("/")
                          .filter(Boolean);
                        if (urlParts.length > 1) {
                          postCategory = urlParts[1] || postCategory;
                        }
                      }

                      return (
                        <Link
                          key={similarPost.id}
                          href={`/blog/${postCategory}/${similarPost.slug}`}
                          className="p-4 border border-gray-200 rounded-lg hover:border-primary transition-colors"
                        >
                          <h3 className="text-secondary font-bold mb-2">
                            {similarPost.title}
                          </h3>
                          <div className="flex items-center justify-between text-sm text-gray-500">
                            <span>By {similarPost.author.display_name}</span>
                            <span>
                              {new Date(
                                similarPost.publish_timestamp * 1000
                              ).toLocaleDateString()}
                            </span>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </>
    );
  
}