"use client";
import { useState, useCallback } from "react";
import { CheckIcon } from "@/ui/icons/general";
import { PricingPlan } from "@/services/pricingService";

// Define types for billing period
type BillingPeriod = "monthly" | "annually";

// Define types for the White Label plan
interface WhiteLabelPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  description: string;
  features: string[];
  apiPlanId?: number;
}

interface PlanSelectionStepProps {
  onNext: (planId: string, billingPeriod: BillingPeriod) => void;
  onBack: () => void;
  apiPricingData: PricingPlan[];
  premiumPlanData: PricingPlan[];
  initialSelectedPlan: string | null;
  initialBillingPeriod: BillingPeriod;
}

export default function PlanSelectionStep({
  onNext,
  onBack,
  apiPricingData,
  premiumPlanData,
  initialSelectedPlan,
  initialBillingPeriod,
}: PlanSelectionStepProps) {
  // State for the selected plan
  const [selectedPlan, setSelectedPlan] = useState<string | null>(initialSelectedPlan);
  
  // State for billing period (monthly or annually)
  const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>(initialBillingPeriod);
  
  // State for loading
  const [isLoading, setIsLoading] = useState(false);
  
  // State for errors
  const [error, setError] = useState<string | null>(null);

  // Get plans based on API data
  const plans = useCallback(() => {
    // If API data is available, use it
    if (apiPricingData.length > 0 && premiumPlanData.length > 0) {
      const proPlanPlans = apiPricingData.filter(
        (plan) => plan.period === (billingPeriod === "monthly" ? "monthly" : "yearly")
      );

      const premiumPlans = premiumPlanData.filter(
        (plan) => plan.period === (billingPeriod === "monthly" ? "monthly" : "yearly")
      );

      if (proPlanPlans.length > 0 && premiumPlans.length > 0) {
        const proPlan = proPlanPlans[0];
        const premiumPlan = premiumPlans[0];

        return [
          {
            id: "basic",
            name: "Pro Plan",
            price: parseFloat(proPlan.price.replace("$", "")),
            period: proPlan.period,
            description: proPlan.description,
            features: [
              "All DIY features included",
              "Generate beautiful, branded PDF audit reports",
              "Add  your logo, and contact info to impress clients",
              "Local SEO audits tailored for service-area businesses",
            ],
            apiPlanId: proPlan.id,
          },
          {
            id: "premium",
            name: "Pro Plan & Embedding",
            price: parseFloat(premiumPlan.price.replace("$", "")),
            period: premiumPlan.period,
            description: premiumPlan.description,
            features: [
              "All Pro Plan features included",
              "Full API access & embeddable SEO audit widgets",
              "AI-powered SEO analysis for smarter recommendations",
              "Priority support and dedicated account manager",
            ],
            apiPlanId: premiumPlan.id,
          },
        ];
      }
    }

    // Fallback to default plans
    return [
      {
        id: "basic",
        name: "Pro Plan",
        price: billingPeriod === "monthly" ? 28 : 288,
        period: billingPeriod === "monthly" ? "monthly" : "yearly",
        description:
          "Offer top-tier SEO reports under your own branding. Whether you're a freelancer or growing agency, Pro Plan helps you scale client services without building a team.",
        features: [
          "All DIY features included",
          "Generate beautiful, branded PDF audit reports",
          "Add  your logo, and contact info to impress clients",
          "Local SEO audits tailored for service-area businesses",
        ],
      },
      {
        id: "premium",
        name: "Pro Plan & Embedding",
        price: billingPeriod === "monthly" ? 79 : 758,
        period: billingPeriod === "monthly" ? "monthly" : "yearly",
        description:
          "Built for agencies, SaaS products, and platforms ready to offer seamless, scalable SEO services under their own brand. This plan gives you full control over integration, customization, and delivery—powered by automation and AI.",
        features: [
          "All Pro Plan features included",
          "Full API access & embeddable SEO audit widgets",
          "AI-powered SEO analysis for smarter recommendations",
          "Priority support and dedicated account manager",
        ],
      },
    ];
  }, [apiPricingData, premiumPlanData, billingPeriod]);

  // Handle plan selection
  const handlePlanSelect = useCallback((planId: string) => {
    setSelectedPlan(planId);
  }, []);

  // Handle billing period change
  const handleBillingPeriodChange = useCallback((period: BillingPeriod) => {
    setBillingPeriod(period);
  }, []);

  // Handle next button click
  const handleNextClick = useCallback(() => {
    if (!selectedPlan) {
      setError("Please select a plan");
      return;
    }

    setError(null);
    onNext(selectedPlan, billingPeriod);
  }, [selectedPlan, billingPeriod, onNext]);

  return (
    <div className="p-4 overflow-y-auto">
      <h2 className="text-xl font-bold text-secondary mb-4">Select a Plan</h2>
      <p className="text-secondary mb-6">
        Choose the pro plan that best fits your needs.
      </p>

      {/* Billing period toggle */}
      <div className="flex justify-center mb-8">
        <div className="inline-flex bg-gray-100 p-1 rounded-full">
          <button
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              billingPeriod === "monthly"
                ? "bg-primary text-white"
                : "text-gray-600 hover:bg-gray-200"
            }`}
            onClick={() => handleBillingPeriodChange("monthly")}
          >
            Monthly
          </button>
          <button
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              billingPeriod === "annually"
                ? "bg-primary text-white"
                : "text-gray-600 hover:bg-gray-200"
            }`}
            onClick={() => handleBillingPeriodChange("annually")}
          >
            Annually
          </button>
        </div>
      </div>

      {/* Vertical plan cards */}
      <div className="space-y-6">
        {plans().map((plan) => (
          <div
            key={plan.id}
            className={`border rounded-lg p-6 cursor-pointer transition-all ${
              selectedPlan === plan.id
                ? "border-primary bg-primary/5"
                : "border-gray-200 hover:border-primary/50"
            }`}
            onClick={() => handlePlanSelect(plan.id)}
          >
            <div className="flex flex-col md:flex-row md:items-start gap-4">
              {/* Plan info */}
              <div className="flex-1">
                <h3 className="text-xl font-bold text-secondary mb-2">
                  {plan.name}
                </h3>

                <p className="text-secondary mb-4">{plan.description}</p>

                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckIcon className="w-5 h-5 text-primary-green mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-secondary">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Pricing */}
              <div className="md:w-64 flex-shrink-0 bg-gray-50 p-4 rounded-lg">
                <div className="text-center">
                  <div className="flex items-center justify-center">
                    <span className="text-3xl font-bold text-primary">
                      $
                      {billingPeriod === "annually"
                        ? (plan.price / 12).toFixed(2)
                        : plan.price.toFixed(2)}
                    </span>
                    <span className="text-gray-500 ml-1">/mo</span>
                  </div>

                  <div className="mt-2 text-sm text-gray-500">
                    {billingPeriod === "annually"
                      ? `Billed annually as $${plan.price.toFixed(2)}`
                      : "Billed monthly"}
                  </div>

                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePlanSelect(plan.id);
                      handleNextClick();
                    }}
                    className="mt-4 w-full btn btn--primary py-2"
                  >
                    Select Plan
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-lg">
          {error}
        </div>
      )}

      <div className="mt-6 flex justify-between">
        <button
          onClick={onBack}
          className="btn btn--outline px-6 py-2"
          disabled={isLoading}
        >
          Back
        </button>
        <button
          onClick={handleNextClick}
          className="btn btn--primary px-6 py-2"
          disabled={isLoading || !selectedPlan}
        >
          {isLoading ? (
            <div className="flex items-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Processing...
            </div>
          ) : (
            "Next Step"
          )}
        </button>
      </div>
    </div>
  );
}
