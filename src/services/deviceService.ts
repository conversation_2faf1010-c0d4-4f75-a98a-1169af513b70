/**
 * Device ID Service
 *
 * This service handles the generation and storage of a unique device ID
 * that persists across sessions for the same user/browser.
 */

// Constants
const DEVICE_ID_KEY = 'seo_device_id';

/**
 * Check if we're in a browser environment
 */
const isBrowser = typeof window !== 'undefined';

/**
 * Generate a random UUID v4
 * @returns A random UUID string
 */
const generateUUID = (): string => {
  // Implementation based on RFC4122 version 4
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

// Cache the device ID in memory to avoid repeated localStorage access
let cachedDeviceId: string | null = null;

/**
 * Get the device ID from localStorage, or generate a new one if it doesn't exist
 * Uses memory caching to improve performance
 * @returns The device ID string
 */
const getDeviceId = (): string => {
  if (!isBrowser) return '';

  // Return cached ID if available to avoid localStorage access
  if (cachedDeviceId) {
    return cachedDeviceId;
  }

  try {
    // Try to get existing device ID from localStorage
    let deviceId = localStorage.getItem(DEVICE_ID_KEY);

    // If no device ID exists, generate a new one and store it
    if (!deviceId) {
      deviceId = generateUUID();
      localStorage.setItem(DEVICE_ID_KEY, deviceId);
    }

    // Cache the device ID in memory
    cachedDeviceId = deviceId;
    return deviceId;
  } catch (error) {
    // Error accessing device ID from localStorage

    // In case of error (e.g., localStorage disabled), generate a temporary ID
    // This won't persist across page refreshes but at least provides some tracking
    const tempId = generateUUID();
    cachedDeviceId = tempId;
    return tempId;
  }
};

const deviceService = {
  getDeviceId,
};

export default deviceService;
