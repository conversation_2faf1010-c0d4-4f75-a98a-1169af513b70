// SEO Analyzer API Response Types

// Common types used across multiple sections
export type RecommendationType = {
  text: string;
  priority: "Low" | "Medium" | "High";
};

export type ScoreGrade = {
  score: number;
  grade: string;
};

// Main API Response
export interface SEOAnalyzerResponse {
  status: string;
  result: SEOAnalyzerResult;
}

export interface SEOAnalyzerResult {
  desktop_screenshot_url: string;
  onpage_analysis: OnPageAnalysis;
  usability_analysis: UsabilityAnalysis;
  localseo_analysis: LocalSEOAnalysis;
  technology_review_analysis: TechSEOAnalysis;
  social_analysis: SocialAnalysis;
  performance_analysis: PerformanceAnalysis;
  links_analysis: LinksAnalysis;
  pagespeed_analysis: PageSpeedAnalysis;
  pagespeed_mobile_analysis: PageSpeedMobileAnalysis;
  child_pages: string[];
  child_pages_count: number;
}

// On-Page Analysis Types
export interface OnPageAnalysis {
  title_tag: TitleTagAnalysis;
  meta_description: MetaDescriptionAnalysis;
  serp_preview: SerpPreviewAnalysis;
  language: LanguageAnalysis;
  headers: HeadersAnalysis;
  keyword_consistency: KeywordConsistencyAnalysis;
  content_amount: ContentAmountAnalysis;
  image_alt_attributes: ImageAltAttributesAnalysis;
  canonical_tag: CanonicalTagAnalysis;
  noindex_tag: NoIndexTagAnalysis;
  ssl_enabled: SSLEnabledAnalysis;
  analytics: AnalyticsAnalysis;
  schema_markup: SchemaMarkupAnalysis;
  https_redirect: HttpsRedirectAnalysis;
  robots_txt: RobotsTxtAnalysis;
  xml_sitemap: XMLSitemapAnalysis;
  noindex_header: NoIndexHeaderAnalysis;
  total_score: ScoreGrade;
  overall_title: string;
  overall_description: string;
}

export interface TitleTagAnalysis {
  title: string;
  length: number;
  is_optimal_length: boolean;
  pass: boolean;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface MetaDescriptionAnalysis {
  content: string;
  length: number;
  is_optimal_length: boolean;
  pass: boolean;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface SerpPreviewAnalysis {
  pass: boolean;
  url: string;
  title: string;
  caption: string;
  favicon: string | null;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface LanguageAnalysis {
  pass: boolean;
  declared: string;
  hreflang: {
    exists: boolean;
    count: number;
    tags: Array<{
      lang: string;
      href: string;
    }>;
    errors: string[];
  };
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface HeadersAnalysis {
  pass: boolean;
  h1: {
    count: number;
    content: string[];
    pass: boolean;
    description: string;
    recommendation: RecommendationType;
    importance: string;
  };
  other_headers: {
    h2: {
      count: number;
      content: string[];
    };
    h3: {
      count: number;
      content: string[];
    };
    h4: {
      count: number;
      content: string[];
    };
    h5: {
      count: number;
      content: string[];
    };
    h6: {
      count: number;
      content: string[];
    };
  };
  hierarchy_recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface KeywordConsistencyAnalysis {
  pass: boolean;
  keywords: Array<{
    keyword: string;
    frequency: number;
    in_title: boolean;
    in_meta_description: boolean;
    in_headings: boolean;
    in_footer: boolean;
  }>;
  phrases: Array<{
    phrase: string;
    frequency: number;
    in_title: boolean;
    in_meta_description: boolean;
    in_headings: boolean;
    in_footer: boolean;
  }>;
  recommendation: RecommendationType;
  description: string;
  importance: string;
  score: number;
}

export interface ContentAmountAnalysis {
  pass: boolean;
  word_count: number;
  text_html_ratio_percent: number;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface ImageAltAttributesAnalysis {
  pass: boolean;
  total_images: number;
  images_with_alt: number;
  images_without_alt: number;
  percent_missing: number;
  missing_alt_images_sample: string[];
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface CanonicalTagAnalysis {
  pass: boolean;
  canonical_url: string;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface NoIndexTagAnalysis {
  pass: boolean;
  content: string;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface SSLEnabledAnalysis {
  pass: boolean;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface AnalyticsAnalysis {
  pass: boolean;
  detected_tools: string[];
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface SchemaMarkupAnalysis {
  pass: boolean;
  formats_found: string[];
  detected_types_count: Record<string, number>;
  common_types: string[];
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface HttpsRedirectAnalysis {
  pass: boolean;
  uses_permanent_redirect: boolean;
  status_code: number;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface RobotsTxtAnalysis {
  pass: boolean;
  url: string;
  content: string;
  error: string | null;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface XMLSitemapAnalysis {
  pass: boolean;
  sitemap_urls_found: string[];
  sitemap_urls_in_robots: string[];
  error: string | null;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface NoIndexHeaderAnalysis {
  pass: boolean;
  content: string;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

// Usability Analysis Types
export interface UsabilityAnalysis {
  device_rendering: DeviceRenderingAnalysis;
  viewport_usage: ViewportUsageAnalysis;
  flash_usage: FlashUsageAnalysis;
  iframes_usage: IframesUsageAnalysis;
  iframe_protection: IframeProtectionAnalysis;
  favicon_presence: FaviconPresenceAnalysis;
  email_privacy: EmailPrivacyAnalysis;
  font_legibility: FontLegibilityAnalysis;
  tap_target_sizing: TapTargetSizingAnalysis;
  total_score: ScoreGrade;
  overall_title: string;
  overall_description: string;
}

export interface DeviceRenderingAnalysis {
  pass: boolean;
  screenshot_urls: {
    desktop: string;
    tablet: string;
    mobile: string;
  };
  available_devices: string[];
  expected_devices: string[];
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface ViewportUsageAnalysis {
  pass: boolean;
  has_viewport_meta: boolean;
  viewport_content: string;
  is_responsive: boolean;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface FlashUsageAnalysis {
  pass: boolean;
  count: number;
  elements: any[];
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface IframesUsageAnalysis {
  pass: boolean;
  count: number;
  iframe_sources: string[];
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface IframeProtectionAnalysis {
  pass: boolean;
  has_x_frame_options: boolean;
  x_frame_options_value: string;
  has_csp_frame_protection: boolean;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface FaviconPresenceAnalysis {
  pass: boolean;
  has_favicon: boolean;
  has_apple_touch_icon: boolean;
  favicon_paths: string[];
  apple_touch_icon_paths: string[];
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface EmailPrivacyAnalysis {
  pass: boolean;
  exposed_email_count: number;
  exposed_emails_sample: string[];
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface FontLegibilityAnalysis {
  pass: boolean;
  small_font_issue_count: number;
  elements_analyzed_inline: number;
  declarations_analyzed_css: number;
  problematic_elements: any[];
  css_issues: Array<{
    selector: string;
    size: string;
    context: string;
    severity: string;
    source: string;
  }>;
  has_external_css: boolean;
  external_css_total_links: number;
  external_css_attempted_fetch: number;
  external_css_analyzed_count: number;
  external_css_fetch_errors: number;
  timing: {
    total_analysis_time: number;
    inline_styles_time: number;
    internal_css_time: number;
    external_css_time: number;
  };
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface TapTargetSizingElement {
  element: string;
  tag_name: string;
  text: string;
  selector: string;
  full_selector: string;
  element_location: {
    parent_tag: string;
    parent_class: string[];
    parent_id: string;
    sibling_position: number;
    total_siblings: number;
  };
  attributes: Record<string, string>;
  computed_width: number;
  computed_height: number;
  is_problematic: boolean;
  issues: string[];
  detection_method: string;
  width_source: string;
  height_source: string;
}

export interface TapTargetSizingAnalysis {
  pass: boolean;
  total_interactive_elements: number;
  problematic_elements_count: number;
  problematic_elements: TapTargetSizingElement[];
  has_touch_specific_css?: boolean;
  touch_media_queries?: string[];
  description?: string;
  importance?: string;
  recommendation?: RecommendationType;
  blog?: string;
  score?: number;
}

// Local SEO Analysis Types
export interface LocalSEOAnalysis {
  contact_info: ContactInfoAnalysis;
  business_schema: BusinessSchemaAnalysis;
  google_business: GoogleBusinessAnalysis;
  pass: boolean;
  total_score: ScoreGrade;
  overall_title: string;
  overall_description: string;
}

export interface ContactInfoAnalysis {
  pass: boolean;
  has_address: boolean;
  has_phone: boolean;
  addresses: string[];
  phones: string[];
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface BusinessSchemaAnalysis {
  pass: boolean;
  has_local_business_schema: boolean;
  schema_types: string[];
  local_business_schemas: any[];
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface GoogleBusinessAnalysis {
  pass: boolean;
  has_gmb_references: boolean;
  has_gmb_links: boolean;
  has_gmb_embeds: boolean;
  has_gbp_mentions: boolean;
  gmb_urls: string[];
  gmb_embeds: string[];
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

// Technology Review Analysis Types
export interface TechSEOAnalysis {
  pass: boolean;
  ssl_enabled: SSLEnabledTechAnalysis;
  robots_meta: RobotsMetaAnalysis;
  dns_servers: DNSServersAnalysis;
  web_server: WebServerAnalysis;
  charset: CharsetAnalysis;
  dmarc_record: DMARCRecordAnalysis;
  spf_record: SPFRecordAnalysis;
  server_ip: ServerIPAnalysis;
  technologies: TechnologiesAnalysis;
  summary: string;
  issues: string[];
  total_score: ScoreGrade;
  overall_title: string;
  overall_description: string;
}

export interface SSLEnabledTechAnalysis {
  pass: boolean;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
}

export interface RobotsMetaAnalysis {
  noindex: boolean;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface DNSServersAnalysis {
  pass: boolean;
  nameservers: string[];
  count: number;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface WebServerAnalysis {
  pass: boolean;
  server: string;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface CharsetAnalysis {
  pass: boolean;
  charset: string;
  source: string;
  is_standard: boolean;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface DMARCRecordAnalysis {
  pass: boolean;
  record: string;
  policy: string;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface SPFRecordAnalysis {
  pass: boolean;
  record: string;
  policy_strength: string;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface ServerIPAnalysis {
  pass: boolean;
  ip: string;
  all_ips: string[];
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface TechnologiesAnalysis {
  pass: boolean;
  technologies: Array<{
    name: string;
    version: string;
    category: string;
  }>;
  technology_count: number;
  description: string;
  recommendations: RecommendationType[];
  importance: string;
  blog: string;
  score: number;
}

// Social Analysis Types
export interface SocialAnalysis {
  facebook: SocialPlatformAnalysis;
  twitter: SocialPlatformAnalysis;
  instagram: SocialPlatformAnalysis;
  linkedin: SocialPlatformAnalysis;
  youtube: YouTubePlatformAnalysis;
  telegram: SocialPlatformAnalysis;
  social_meta_tags: SocialMetaTagsAnalysis;
  share_buttons: ShareButtonsAnalysis;
  addthis_detected: SocialPlatformAnalysis;
  sharethis_detected: SocialPlatformAnalysis;
  total_score: ScoreGrade;
  overall_title: string;
  overall_description: string;
}

export interface SocialPlatformAnalysis {
  pass: boolean;
  profile_url?: string;
  page_url?: string;
  pixel_id?: string | null;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  score: number;
}

export interface YouTubePlatformAnalysis extends SocialPlatformAnalysis {
  channel_url: string;
  channel_id: string | null;
  channel_name: string | null;
  blog: string;
  statistics: {
    subscriberCount: number;
    videoCount: number;
    viewCount: number;
  };
}

export interface SocialMetaTagsAnalysis {
  pass: boolean;
  og_tags_present: boolean;
  og_tags_count: number;
  og_tags: Record<string, string>;
  missing_og_tags: string[];
  twitter_tags_present: boolean;
  twitter_tags_count: number;
  twitter_tags: Record<string, string>;
  missing_twitter_tags: string[];
  description: string;
  recommendation: RecommendationType;
  importance: string;
  score: number;
}

export interface ShareButtonsAnalysis {
  pass: boolean;
  button_count: number;
  addthis_detected: boolean;
  sharethis_detected: boolean;
  description: string;
  recommendation: RecommendationType;
  importance: string;
  score: number;
}

// Performance Analysis Types
export interface PerformanceAnalysis {
  javascript_errors: JavaScriptErrorsAnalysis;
  deprecated_html: DeprecatedHTMLAnalysis;
  compression: CompressionAnalysis;
  resource_count: ResourceCountAnalysis;
  amp: AMPAnalysis;
  page_size: PageSizeAnalysis;
  inline_styles: InlineStylesAnalysis;
  performance_timing: PerformanceTimingAnalysis;
  http_protocol: HTTPProtocolAnalysis;
  image_optimisation: ImageOptimizationAnalysis;
  minification: MinificationAnalysis;
  total_score: ScoreGrade;
  overall_title: string;
  overall_description: string;
  overall_recommendation: string;
}

export interface JavaScriptErrorsAnalysis {
  pass: boolean;
  error_count: number;
  errors: string[];
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

export interface DeprecatedHTMLAnalysis {
  pass: boolean;
  count: number;
  elements_by_tag: Record<string, number>;
  elements: any[];
  description: string;
  recommendation: RecommendationType;
  importance: string;
  blog: string;
  score: number;
}

export interface CompressionDetails {
  html: {
    original_kb: number;
    compressed_kb: number;
    ratio: number;
    type: string | null;
  };
  css: {
    original_kb: number;
    compressed_kb: number;
    ratio: number;
    type: string | null;
  };
  js: {
    original_kb: number;
    compressed_kb: number;
    ratio: number;
    type: string | null;
  };
  images: {
    original_kb: number;
    compressed_kb: number;
    ratio: number;
    type: string | null;
  };
  other: {
    original_kb: number;
    compressed_kb: number;
    ratio: number;
    type: string | null;
  };
}

export interface CompressionAnalysis {
  pass: boolean;
  compression_type: string;
  size_mb: number;
  compressed_size_mb: number;
  compression_ratio: number;
  compression_details?: CompressionDetails;
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

export interface ResourceCountAnalysis {
  html: number;
  js: number;
  css: number;
  images: number;
  fonts: number;
  iframes: number;
  other: number;
  total: number;
  pass: boolean;
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

export interface AMPAnalysis {
  is_amp_page: boolean;
  has_amp_link: boolean;
  amp_url: string | null;
  pass: boolean;
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

export interface PageSizeAnalysis {
  pass: boolean;
  total_estimated_size_mb: number;
  size_category: string;
  breakdown_estimated_mb: {
    html_mb: number;
    est_js_mb: number;
    est_css_mb: number;
    est_images_mb: number;
  };
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

export interface InlineStylesAnalysis {
  pass: boolean;
  elements_with_style: number;
  total_elements: number;
  inline_style_percentage: number;
  total_style_size_kb: number;
  most_common_properties: Record<string, number>;
  style_examples: Array<{
    tag: string;
    style: string;
  }>;
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

export interface PerformanceTimingAnalysis {
  pass: boolean;
  time_to_first_byte_s: number;
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

export interface HTTPProtocolAnalysis {
  pass: boolean;
  protocol_used: string;
  alt_svc_header: string;
  supports_http2: boolean;
  supports_http3: boolean;
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

export interface ImageOptimizationAnalysis {
  pass: boolean;
  total_images: number;
  next_gen_images_count: number;
  missing_alt_count: number;
  missing_dimensions_count: number;
  problematic_images_sample: any[];
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

export interface MinificationAnalysis {
  pass: boolean;
  total_js: number;
  unminified_js_count: number;
  unminified_js_samples: string[];
  total_css: number;
  unminified_css_count: number;
  unminified_css_samples: string[];
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
  score: number;
}

// Links Analysis Types
export interface LinksAnalysis {
  backlinks: BacklinksAnalysis;
  backlinks_detail?: BacklinksDetailAnalysis;
  competitors: CompetitorsAnalysis;
  mentions: MentionsAnalysis;
  on_page_links: OnPageLinksAnalysis;
  friendly_links: FriendlyLinksAnalysis;
  broken_links: BrokenLinksAnalysis;
  domain_insight?: DomainInsightAnalysis;
  main?: {
    totalScore: {
      Grade: string;
      score: number;
    };
  };
  total_score: ScoreGrade;
  overall_title: string;
  overall_description: string;
}

export interface BacklinksAnalysis {
  pass: boolean;
  total_backlinks: number;
  unique_domains: number;
  sample_backlinks: Array<{
    link: string;
    title: string;
    description: string;
  }>;
  domains: Array<{
    domain: string;
    count: number;
  }>;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface BacklinksDetailAnalysis {
  overall_title: string;
  overall_description: string;
  nofollow_links: number;
  dofollow_links: number;
  unique_domains: number;
  top_backlinks_root_domain: Array<{
    domain: string;
    count: number;
  }>;
  top_backlinks_sub_domain: Array<{
    domain: string;
    count: number;
  }>;
}

export interface CompetitorsAnalysis {
  pass: boolean;
  total_competitors: number;
  unique_domains: number;
  sample_competitors: Array<{
    link: string;
    title: string;
    description: string;
  }>;
  domains: Array<{
    domain: string;
    count: number;
  }>;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface MentionsAnalysis {
  pass: boolean;
  total_mentions: number;
  brand_name: string;
  sample_mentions: Array<{
    link: string;
    title: string;
    description: string;
  }>;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface OnPageLinksAnalysis {
  pass: boolean;
  total_links: number;
  external_links: number;
  nofollow_links: number;
  external_percentage: number;
  nofollow_percentage: number;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface FriendlyLinksAnalysis {
  pass: boolean;
  friendly_percentage: number;
  unfriendly_links_sample: Array<{
    url: string;
    text: string;
    score: number;
  }>;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface BrokenLinksAnalysis {
  pass: boolean;
  total_checked: number;
  broken_count: number;
  broken_percentage: number;
  sample_broken_links: Array<{
    url: string;
    status: string;
    details: number;
  }>;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

export interface DomainInsightAnalysis {
  pass: boolean;
  overall_title: string;
  overall_description: string;
  domain_authority: number;
  page_authority: number;
  spam_score: number;
  trust_flow?: number;
  citation_flow?: number;
  referring_domains?: number;
  total_backlinks?: number;
  description: string;
  importance: string;
  recommendation: RecommendationType;
  blog: string;
  score: number;
}

// PageSpeed Analysis Types
export interface PageSpeedAnalysis {
  core_web_vitals_desktop: CoreWebVitalsAnalysis;
  performance_desktop: PerformanceDesktopAnalysis;
}

export interface PageSpeedMobileAnalysis {
  core_web_vitals_mobile: CoreWebVitalsAnalysis;
  performance_mobile: PerformanceMobileAnalysis;
}

export interface CoreWebVitalsAnalysis {
  pass: boolean;
  "Largest Contentful Paint (LCP)"?: number;
  "Interaction to Next Paint (INP)"?: number | null;
  "Cumulative Layout Shift (CLS)"?: number;
  description: string;
  importance: string;
  recommendation: RecommendationType;
}

export interface PerformanceDesktopAnalysis {
  pass: boolean;
  performance_score: number;
  "First Contentful Paint (FCP)": number;
  "Speed Index (SI)": number;
  "Largest Contentful Paint (LCP)": number;
  "Time to Interactive (TTI)": number;
  "Total Blocking Time (TBT)": number;
  "Cumulative Layout Shift (CLS)": number;
  "Interaction To Next Paint (INP)": number | null;
  top_opportunities_ms_savings: Record<string, number>;
  recommendation: RecommendationType;
  description: string;
  importance: string;
  blog: string;
}

export interface PerformanceMobileAnalysis extends PerformanceDesktopAnalysis {
  // Mobile-specific properties can be added here if needed in the future
  mobile_specific?: boolean;
}
