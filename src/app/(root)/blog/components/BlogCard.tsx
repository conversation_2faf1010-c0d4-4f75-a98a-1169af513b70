"use client";

import Link from "next/link";
import Image from "next/image";
import DateHolder from "./DateHolder";
import BlogPic from "../blog.jpg";
export interface BlogCardProps {
  id: number;
  title: string;
  slug: string;
  author?:
    | {
        id: number;
        email: string;
        display_name: string;
      }
    | string;
  body?: string;
  snippet?: string;
  publish_timestamp: number;
  url?: string;
  tags?: string[];
  cover_image?: string | null;
  category?: {
    name: string;
    slug: string;
  };
}

const BlogCard: React.FC<BlogCardProps> = ({
  title,
  slug,
  author,
  body,
  snippet,
  publish_timestamp,
  url,
  category,
  cover_image,
}) => {
  // Get author display name based on author type
  const getAuthorName = () => {
    if (!author) return "Unknown Author";

    if (typeof author === "string") {
      // If author is a string (email), use it or extract name part
      return author.split("@")[0] || author;
    } else {
      // If author is an object, use display_name
      return author.display_name || "Unknown Author";
    }
  };

  // Get description from snippet or body
  const getDescription = () => {
    // Use snippet if available
    if (snippet) {
      return snippet;
    }

    // Otherwise extract from body
    if (!body) return "";

    if (typeof window === "undefined") {
      // Server-side: use regex to strip HTML tags
      const text = body.replace(/<[^>]*>?/gm, "").trim();
      return text.substring(0, 150) + (text.length > 150 ? "..." : "");
    } else {
      // Client-side: use DOM to parse HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = body;
      // Get text content and limit to 150 characters
      const text = tempDiv.textContent || tempDiv.innerText || "";
      return text.substring(0, 150) + (text.length > 150 ? "..." : "");
    }
  };

  // Determine the correct URL to use
  const getPostUrl = () => {
    // If a URL is provided from the API, use it
    if (url) {
      return url;
    }

    // If a category is provided, use the category/slug format
    if (category && category.slug) {
      return `/blog/${category.slug}/${slug}`;
    }

    // Default to SEO category if no category is provided
    return `/blog/SEO/${slug}`;
  };

  return (
    <Link
      href={getPostUrl()}
      className="flex flex-col h-full min-h-[400px] sm:min-h-[420px] md:min-h-[450px] transition-all duration-100 rounded-lg p-2 sm:p-3 hover:bg-gray-50"
    >
      <div>
        <div className="mb-2 overflow-hidden rounded-lg relative w-full h-[200px] sm:h-[220px] md:h-[240px] lg:h-[260px]">
          <Image
            className="object-cover object-center transition-transform duration-500 hover:scale-105 rounded-lg w-full h-full"
            src={cover_image || BlogPic}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
            priority={false}
            alt={title}
          />
          <div className="p-3 sm:p-4 md:p-6 absolute top-0 left-0 w-full h-[200px] sm:h-[220px] md:h-[240px] lg:h-[260px] bg-gradient-to-b from-[#00000000] to-[#00000040] rounded-lg">
            <DateHolder date={publish_timestamp} />
          </div>
        </div>
        <span className="text-[10px] sm:text-[11px] md:text-[12px] flex flex-wrap items-center">
          <span>
            Written by <b>{getAuthorName()}</b>
          </span>
          {category && (
            <span className="ml-1 sm:ml-2 text-gray-500">
              in <span className="font-medium">{category.name}</span>
            </span>
          )}
        </span>
      </div>
      <div className="flex flex-col flex-1 justify-between">
        <div className="flex-1">
          <h3 className="text-secondary font-black text-base sm:text-lg md:text-xl mb-2 line-clamp-2">
            {title}
          </h3>
          <p className="text-secondary text-xs sm:text-sm md:text-base line-clamp-2 md:line-clamp-3">
            {getDescription()}
          </p>
        </div>
      </div>
    </Link>
  );
};

export default BlogCard;
