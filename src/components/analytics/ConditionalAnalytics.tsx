"use client";

import { useEffect } from "react";
import Script from "next/script";
import analyticsService from "@/services/analyticsService";

const GTM_ID = "AW-17379662777";

// Helper function to check if user has explicitly denied consent
const hasUserDeniedConsent = (): boolean => {
  if (typeof window === "undefined") return false;
  try {
    const consent = localStorage.getItem("seo_analyser_cookie_consent");
    return consent === "declined";
  } catch {
    return false;
  }
};

export default function ConditionalAnalytics() {
  useEffect(() => {
    // Initialize analytics if user hasn't denied consent
    if (!hasUserDeniedConsent()) {
      analyticsService.initializeAnalytics();
    }
  }, []);

  // Only render Google Tag Manager scripts if user hasn't explicitly denied consent
  if (hasUserDeniedConsent()) {
    return null;
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${GTM_ID}`}
        strategy="afterInteractive"
      />
      <Script id="google-tag-manager" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${GTM_ID}');
        `}
      </Script>
    </>
  );
}
