import React from "react";

interface OverallSectionProps {
  title?: string;
  description?: string;
  className?: string;
}

const OverallSection: React.FC<OverallSectionProps> = ({
  title,
  description,
  className = "",
}) => {
  // Don't render anything if both title and description are missing
  if (!title && !description) {
    return null;
  }

  return (
    <div
      className={`flex-1 py-6 px-5 lg:p-6 rounded-lg bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 ${className}`}
    >
      {title && (
        <div className="font-semibold text-primary text-base lg:text-lg">
          {title}
        </div>
      )}
      {description && (
        <p className="!text-xs lg:text-sm text-primary/70 mt-2 leading-relaxed">
          {description}
        </p>
      )}
    </div>
  );
};

export default OverallSection;
