import Table from "@/ui/Table";
import ProgressPercent from "../../ProgressPercent";

// Define the structure based on the actual API response
type DomainItem = {
  domain?: string;
  count?: number;
};

type TopCompetitorDomainsProps = {
  domains?: DomainItem[];
};

export default function TopCompetitorDomains({
  domains = [],
}: TopCompetitorDomainsProps) {
  // Check if we have domain data
  const hasDomains = domains && domains.length > 0;

  // Calculate percentages based on count
  const processedDomains = hasDomains
    ? domains.map((domain, index, arr) => {
        // Calculate percentage based on count
        const maxCount = Math.max(
          ...arr.map((d) => (typeof d.count === "number" ? d.count : 0))
        );

        const domainCount = typeof domain.count === "number" ? domain.count : 0;

        const calculatedPercentage =
          maxCount > 0 ? Math.round((domainCount / maxCount) * 100) : 0;

        return {
          ...domain,
          percentage: calculatedPercentage,
        };
      })
    : [];

  return (
    <div className="mt-6">
      <h4 className="font-semibold text-secondary pb-2">
        Top Competitor Domains
      </h4>

      {hasDomains ? (
        <Table>
          <Table.Header>
            <th className="pb-4">Domain</th>
            <th className="pb-4 px-2 sm:px-4 lg:px-0">Count</th>
            <th className="w-[100px] sm:w-[150px] lg:w-[180px]"></th>
          </Table.Header>
          <Table.Body>
            {processedDomains.map((item, index) => (
              <Table.Row key={index}>
                <td className="text-xs sm:text-sm">{item.domain || "N/A"}</td>
                <td className="text-center text-xs sm:text-sm">
                  {item.count || "0"}
                </td>
                <td>
                  <div className="w-full flex justify-end">
                    <div className="w-[100px] sm:w-[150px] lg:w-[180px]">
                      <ProgressPercent percentage={item.percentage || 0} />
                    </div>
                  </div>
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="p-4 bg-light-gray-6 rounded-lg text-center text-secondary/60">
          No competitor domain data available
        </div>
      )}
    </div>
  );
}
