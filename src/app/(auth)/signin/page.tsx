// app/signin/page.tsx
"use client";

import { signIn } from "next-auth/react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

export default function SignInPage() {
  const [error, setError] = useState("");
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = new FormData(e.currentTarget);

    const res = await signIn("credentials", {
      email: form.get("email"),
      password: form.get("password"),
      redirect: false,
    });

    if (res?.error) {
      setError(res.error);
    } else {
      router.push("/dashboard");
    }
  };

  return (
    <div className="bg-white rounded-2xl px-10">
      <div>
        <Image
          src={`/images/appLogo.svg`}
          width={93}
          height={47}
          alt="app logo"
        />
      </div>
      <form
        onSubmit={handleSubmit}
        className="flex flex-col gap-6"
        autoComplete="off"
        method="post"
        action="javascript:void(0);"
        data-form-type="signin"
      >
        <label>
          E-Mail
          <input
            type="email"
            name="email"
            required
            autoComplete="username email"
          />
        </label>
        <label>
          Password
          <input
            type="password"
            name="password"
            required
            autoComplete="current-password"
          />
        </label>
        <button type="submit">ورود</button>
        {error && <p style={{ color: "red" }}>{error}</p>}
      </form>
    </div>
  );
}
