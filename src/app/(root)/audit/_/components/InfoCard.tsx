"use client";
import { InfoIcon } from "@/ui/icons/general";
import { ReactNode, useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

type Props = {
  title: string;
  description: string | ReactNode;
  className?: string;
};

export default function InfoCard({ description, title, className }: Props) {
  const [displayTitle, setDisplayTitle] = useState(title);
  const [displayDescription, setDisplayDescription] = useState(description);

  useEffect(() => {
    if (title !== displayTitle || description !== displayDescription) {
      // Delay the content update to allow exit animation
      const timer = setTimeout(() => {
        setDisplayTitle(title);
        setDisplayDescription(description);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [title, description, displayTitle, displayDescription]);

  return (
    <div className="w-full flex flex-col p-4 rounded-lg border border-light-gray">
      <div className={`flex items-center gap-2.5 ${className}`}>
        <div>
          <InfoIcon className="w-12 h-12 text-primary" />
        </div>
        <div className="flex-1 flex flex-col gap-2 overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.div
              key={`${displayTitle}-${
                typeof displayDescription === "string"
                  ? displayDescription
                  : "complex"
              }`}
              initial={{ opacity: 0, y: 8 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -8 }}
              transition={{
                duration: 0.15,
                ease: "easeInOut",
                opacity: { duration: 0.1 },
              }}
              className="flex flex-col gap-2"
            >
              <motion.h5
                className="text-secondary font-semibold"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.03, duration: 0.1 }}
              >
                {displayTitle}
              </motion.h5>
              <motion.p
                className="text-sm text-secondary/60"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.06, duration: 0.1 }}
              >
                {displayDescription}
              </motion.p>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
