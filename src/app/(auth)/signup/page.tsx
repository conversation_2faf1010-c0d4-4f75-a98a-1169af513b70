"use client";

import { signIn } from "next-auth/react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import NavLogo from "@/components/navbar/NavLogo";
import EmailInput from "../components/input/EmailInput";
import PassInput from "../components/input/PassInput";
import KeepLoggedCheck from "../components/input/KeepLoggedCheck";
import Image from "next/image";
import Link from "next/link";

export default function SignUpPage() {
  const [error, setError] = useState("");
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [keepLoggedIn, setKeepLoggedIn] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = new FormData(e.currentTarget);

    const res = await signIn("credentials", {
      email: form.get(email),
      password: form.get(password),
      confirm_password: form.get(confirmPassword),
      redirect: false,
    });
    
    if (res?.error) {
      setError(res.error);
    } else {
      router.push("/dashboard");
    }
  };

  return (
    <div className="container grid lg:grid-cols-11 gap-6">
      <form
        onSubmit={handleSubmit}
        className="lg:col-span-5 bg-white rounded-2xl mt-[-8rem] flex flex-col gap-5"
        autoComplete="off"
        method="post"
        action="javascript:void(0);"
        data-form-type="signup"
      >
        <div className="px-9 py-5 border-b border-light-gray gap-2">
          <NavLogo />
        </div>
        <div className="p-9 flex flex-col gap-5">
          <EmailInput onChange={setEmail} placeholder="Type Your Email here." />
          <PassInput
            onChange={setPassword}
            placeholder="Type Your Password here."
          />
          <PassInput
            confirmPassword
            onChange={setConfirmPassword}
            placeholder="Confirm Your Password here."
          />
          <KeepLoggedCheck
            keepLoggedIn={keepLoggedIn}
            onChange={setKeepLoggedIn}
          />
          <button type="submit" className="btn btn--primary w-full">
            Sign Up
          </button>
          <div className="w-full flex items-center gap-4">
            <span className="w-full h-[1px] bg-light-gray"></span>
            <span className="text-gray-500 text-sm">or</span>
            <span className="w-full h-[1px] bg-light-gray"></span>
          </div>
          <button className="btn btn--outline border-light-gray text-secondary font-normal">
            <Image
              src={`/images/googleIcon.png`}
              width={24}
              height={24}
              alt="google"
            />
            Google
          </button>
          <div>
            Have an account?{" "}
            <Link href={`/signin`} className="text-blue-500">
              Log In
            </Link>
          </div>
        </div>
      </form>
      <div className="lg:col-span-6 mt-[5rem]">
        <Image
          src="/images/overview-top.svg"
          alt="Overview Top"
          width={680}
          height={580}
          className="w-full"
        />
      </div>
    </div>
  );
}
