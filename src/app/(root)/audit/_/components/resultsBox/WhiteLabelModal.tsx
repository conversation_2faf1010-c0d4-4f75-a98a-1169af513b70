"use client";
import { useState, useCallback, useEffect, useMemo } from "react";
import Modal from "@/ui/Modal";
import StepProgressBar from "@/ui/StepProgressBar";
import pricingService, { PricingPlan } from "@/services/pricingService";
import whiteLabelService from "@/services/whiteLabelService";
import profileService from "@/services/profileService";
import { PaymentStatusResponse } from "@/services/paymentService";
import { useAuthStore } from "@/store/authStore";
import {
  WhiteLabelInfoStep,
  BillingAndPaymentStep,
  DownloadStep,
} from "./WhiteLabelSteps";
import { countries } from "./WhiteLabelSteps/CountrySelector";

// Define types for the White Label steps
type WhiteLabelStep = "white-label" | "select-plan" | "payment" | "download";

// Define types for the White Label form data
interface WhiteLabelFormData {
  brandName: string;
  logoImage: File | null;
  phoneNumber: string;
  website: string;
  countryCode?: string;
}

// Define types for billing period
type BillingPeriod = "monthly" | "annually";

// Define types for the White Label payment method
type PaymentMethod = "paypal" | "credit-card" | "crypto" | "stripe";

// Define props for the White Label Modal component
interface WhiteLabelModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  taskId?: string;
  paymentStatus?: PaymentStatusResponse | null;
  isLoadingPayment?: boolean;
  initialStep?: WhiteLabelStep;
  urlPaymentId?: string;
}

export default function WhiteLabelModal({
  isOpen,
  onClose,
  onSuccess,
  taskId,
  paymentStatus,
  isLoadingPayment,
  initialStep,
  urlPaymentId,
}: WhiteLabelModalProps) {
  // Use the auth store for authentication state
  const { isAuthenticated } = useAuthStore();

  // Unified loading states
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSubscriptionCheckLoading, setIsSubscriptionCheckLoading] =
    useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isDataFetchComplete, setIsDataFetchComplete] =
    useState<boolean>(false);

  // State for the current step with improved initialization
  const [currentStep, setCurrentStep] = useState<WhiteLabelStep>(() => {
    if (paymentStatus && initialStep === "download") {
      return "download";
    }
    return initialStep || "white-label";
  });

  // Subscription states
  const [hasActiveSubscription, setHasActiveSubscription] =
    useState<boolean>(false);
  const [hasActiveProPlan, setHasActiveProPlan] = useState<boolean>(false);
  const [subscriptionDetails, setSubscriptionDetails] = useState<{
    planName: string;
    renewalDate?: string;
  } | null>(null);

  // Profile data state to store the full profile API response
  const [profileData, setProfileData] = useState<any>(null);

  // State to track if we should ignore payment status for navigation (when retrying)
  const [ignorePaymentStatus, setIgnorePaymentStatus] = useState<boolean>(false);

  // Parse phone number helper function
  const parsePhoneNumber = useCallback((fullNumber: string | undefined) => {
    if (!fullNumber) return { countryCode: "+1", phoneNumber: "" };

    const matchedCountry = countries.find((country) =>
      fullNumber.startsWith(country.dialCode)
    );

    if (matchedCountry) {
      return {
        countryCode: matchedCountry.dialCode,
        phoneNumber: fullNumber.substring(matchedCountry.dialCode.length),
      };
    }

    return { countryCode: "+1", phoneNumber: fullNumber };
  }, []);

  // Initialize form data from payment status or defaults
  const [formData, setFormData] = useState<WhiteLabelFormData>(() => {
    if (paymentStatus?.whitelabel_setting) {
      const settings = paymentStatus.whitelabel_setting;
      const { countryCode, phoneNumber } = parsePhoneNumber(
        settings.phone_number
      );

      return {
        brandName: settings.brand_name || "",
        logoImage: null, // Can't set File object directly
        phoneNumber,
        website: settings.website || "",
        countryCode,
      };
    }

    return {
      brandName: "",
      logoImage: null,
      phoneNumber: "",
      website: "",
      countryCode: "+1", // Default to US
    };
  });

  // Initialize other states from payment status
  const [selectedPlan, setSelectedPlan] = useState<string | null>(
    paymentStatus?.plan
      ? paymentStatus.plan.toLowerCase().includes("embedding")
        ? "premium"
        : "basic"
      : null
  );

  const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>(
    paymentStatus?.plan_period?.toLowerCase().includes("month")
      ? "monthly"
      : "annually"
  );

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(
    paymentStatus ? "stripe" : null
  );

  // State for API pricing data
  const [apiPricingData, setApiPricingData] = useState<PricingPlan[]>([]);

  // State for logo preview URL
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(
    paymentStatus?.whitelabel_setting?.logo || null
  );

  // State for tracking if we have existing settings
  const [hasExistingSettings, setHasExistingSettings] = useState<boolean>(
    !!paymentStatus?.whitelabel_setting
  );

  // Effect to handle step navigation when modal opens or payment status changes
  useEffect(() => {
    if (isOpen && !paymentStatus) {
      setCurrentStep("white-label");
    }
  }, [isOpen, paymentStatus]);

  // Effect to check subscription status and fetch data when modal opens
  useEffect(() => {
    if (!isOpen) return;

    const fetchData = async () => {
      // Skip if payment status is available - we already have the data
      if (paymentStatus) {
        setIsDataFetchComplete(true);
        return;
      }

      // Only proceed if user is authenticated
      if (!isAuthenticated) {
        setIsDataFetchComplete(true);
        return;
      }

      setIsSubscriptionCheckLoading(true);
      setIsLoading(true);

      try {
        // 1. Check subscription status
        const profileResponse = await profileService.getUserProfile();

        if (profileResponse.success && profileResponse.data) {
          // Store the full profile data
          setProfileData(profileResponse.data);

          const now = new Date();
          // Check for Pro Plan subscription (includes legacy White label plans)
          const proPlanSubscription =
            profileResponse.data.subscriptions.find((sub) => {
              const periodEnd = new Date(sub.current_period_end);
              return (sub.plan_name === "Pro Plan" || sub.plan_name === "Pro Plan & Embedding" || sub.plan_name === "White label") && periodEnd > now;
            });

          const hasSubscription = !!proPlanSubscription;
          const hasProPlan = !!proPlanSubscription;
          setHasActiveSubscription(hasSubscription);
          setHasActiveProPlan(hasProPlan);

          if (hasSubscription && proPlanSubscription) {
            setSubscriptionDetails({
              planName: proPlanSubscription.plan_name,
              renewalDate: new Date(
                proPlanSubscription.current_period_end
              ).toLocaleDateString(),
            });

            // Skip to download step if on billing or payment step
            if (currentStep === "select-plan" || currentStep === "payment") {
              setCurrentStep("download");
            }
          } else {
            setSubscriptionDetails(null);
          }
        } else {
          // Clear profile data if request failed
          setProfileData(null);
        }
      } catch (error) {
        // console.error("Error checking subscription status:", error);
        setHasActiveSubscription(false);
        setHasActiveProPlan(false);
        setSubscriptionDetails(null);
        setProfileData(null);
      } finally {
        setIsSubscriptionCheckLoading(false);
      }

      // 2. Fetch white label settings
      setIsFetching(true);

      try {
        const response = await whiteLabelService.getWhiteLabelSettings();

        if (response.success && response.data) {
          const settings = response.data;
          const { countryCode, phoneNumber } = parsePhoneNumber(
            settings.phone_number
          );

          setFormData({
            brandName: settings.brand_name || "",
            logoImage: null,
            phoneNumber,
            website: settings.website || "",
            countryCode: countryCode || "+1",
          });

          if (settings.logo) {
            setLogoPreviewUrl(settings.logo);
          } else {
            setLogoPreviewUrl(null);
          }

          setHasExistingSettings(true);
        } else {
          // Reset to defaults on error
          setFormData({
            brandName: "",
            logoImage: null,
            phoneNumber: "",
            website: "",
            countryCode: "+1",
          });
          setLogoPreviewUrl(null);
          setHasExistingSettings(false);
        }
      } catch (error) {
        // console.error("Error fetching white label settings:", error);
        // Reset form on error
        setFormData({
          brandName: "",
          logoImage: null,
          phoneNumber: "",
          website: "",
          countryCode: "+1",
        });
        setLogoPreviewUrl(null);
        setHasExistingSettings(false);
      } finally {
        setIsFetching(false);
        setIsLoading(false);
        setIsDataFetchComplete(true);
      }
    };

    // 3. Fetch pricing data
    const fetchPricingData = async () => {
      try {
        const allPricingData = await pricingService.getPricingData();
        const proPlanData = allPricingData["Pro Plan"] || [];
        setApiPricingData(proPlanData);
      } catch (error) {
        // console.error("Error fetching pricing data:", error);
        setApiPricingData([]);
      }
    };

    // Execute all data fetching
    fetchData();
    fetchPricingData();
  }, [isOpen, paymentStatus, isAuthenticated, currentStep, parsePhoneNumber]);

  // Effect to create preview URL for logo if it exists
  useEffect(() => {
    if (formData.logoImage) {
      const objectUrl = URL.createObjectURL(formData.logoImage);
      setLogoPreviewUrl(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    }
  }, [formData.logoImage]);

  // Effect to clean up the URL when component unmounts
  useEffect(() => {
    return () => {
      if (logoPreviewUrl && logoPreviewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(logoPreviewUrl);
      }
    };
  }, [logoPreviewUrl]);

  // Handle white label info step completion
  const handleWhiteLabelInfoComplete = useCallback(
    (updatedFormData: WhiteLabelFormData) => {
      setFormData(updatedFormData);
      setHasExistingSettings(true);

      // Determine next step based on subscription status
      if (paymentStatus || hasActiveSubscription) {
        setCurrentStep("download");
      } else {
        setCurrentStep("select-plan");
      }
    },
    [paymentStatus, hasActiveSubscription]
  );

  // Handle billing and payment step completion
  const handleBillingPaymentComplete = useCallback(
    (planId: string, period: BillingPeriod) => {
      setSelectedPlan(planId);
      setBillingPeriod(period);
      setPaymentMethod("stripe");

      // Show payment step briefly before proceeding to download
      setCurrentStep("payment");
      setTimeout(() => {
        if (!paymentStatus) {
          setCurrentStep("download");
        }
      }, 300);
    },
    [paymentStatus]
  );

  // Handle download step completion
  const handleDownloadComplete = useCallback(() => {
    onSuccess();
    setCurrentStep("white-label");
    onClose();

    // Navigate to white-label page
    if (typeof window !== "undefined") {
      window.location.href = "/white-label";
    }
  }, [onSuccess, onClose]);

  // Handle back button click
  const handleBackClick = useCallback(() => {
    if (currentStep === "select-plan" || currentStep === "payment") {
      setCurrentStep("white-label");
    }
  }, [currentStep]);

  // Handle back from download step (when payment fails)
  const handleDownloadBackClick = useCallback(() => {
    // Reset payment status and go back to billing step
    setCurrentStep("select-plan");
  }, []);

  // Handle retry from download step (when payment fails)
  const handleDownloadRetryClick = useCallback(() => {
    // console.log("handleDownloadRetryClick called"); // Debug log
    // Reset payment status and go back to billing step
    setCurrentStep("select-plan");
   
  }, []);

  // Handle skip to download button click
  const handleSkipToDownload = useCallback(() => {
    setCurrentStep("payment");
    setTimeout(() => setCurrentStep("download"), 100);
  }, []);

  // Steps array for the progress bar
  const steps = useMemo(() => {
    const baseSteps = [
      { id: "authentication", label: "Auth" },
      { id: "white-label", label: "White Label Setting" },
    ];

    if (hasActiveSubscription) {
      return [...baseSteps, { id: "download", label: "Download" }];
    } else {
      return [
        ...baseSteps,
        { id: "select-plan", label: "Billing" },
        { id: "payment", label: "Payment" },
        { id: "download", label: "Download" },
      ];
    }
  }, [hasActiveSubscription]);

  // Handle step click
  const handleStepClick = useCallback(
    (stepId: string) => {
      // Allow navigation only to specific steps based on conditions
      // Removed ability to navigate to select-plan (billing) step
      if (stepId === "download" && hasActiveSubscription) {
        setCurrentStep("download");
      }
    },
    [hasActiveSubscription]
  );

  // Determine the current step ID for the progress bar
  const getCurrentStepId = useCallback(() => {
    if (paymentStatus) {
      return "download";
    }

    if (
      hasActiveSubscription &&
      (currentStep === "select-plan" || currentStep === "payment")
    ) {
      return "download";
    }

    return currentStep;
  }, [currentStep, paymentStatus, hasActiveSubscription]);

  // Render step indicator
  const renderStepIndicator = useCallback(
    () => (
      <div className="px-2 sm:px-4 lg:px-6 pt-4 sm:pt-5 lg:pt-6 pb-2 sm:pb-3 lg:pb-4">
        <StepProgressBar
          steps={steps}
          currentStepId={getCurrentStepId()}
          className="mb-2 sm:mb-3 lg:mb-4"
          onStepClick={handleStepClick}
          clickableSteps={[]} // No steps are clickable to prevent hover effect on billing step
        />
      </div>
    ),
    [steps, getCurrentStepId, handleStepClick]
  );

  // Loading spinner component
  const LoadingSpinner = useCallback(
    ({ message }: { message: string }) => (
      <div className="p-4 flex flex-col items-center justify-center min-h-[300px]">
        <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
        <p className="text-secondary">{message}</p>
      </div>
    ),
    []
  );

  // Render the current step content
  const renderStepContent = useCallback(() => {
    // Common props for DownloadStep
    const downloadStepProps = {
      onComplete: handleDownloadComplete,
      onBack: handleDownloadBackClick,
      onRetry: handleDownloadRetryClick,
      onClose: onClose, // Pass the modal close function
      formData,
      selectedPlan: hasActiveSubscription
        ? "white-label"
        : selectedPlan || "basic",
      billingPeriod,
      paymentMethod: paymentMethod || "credit-card",
      apiPricingData,
      logoPreviewUrl,
      paymentStatus,
      isLoadingPayment,
      taskId,
      paymentId: urlPaymentId || "",
      hasActiveSubscription,
      hasActiveProPlan, // Pass Pro Plan status to DownloadStep
      profileData, // Pass the profile data to DownloadStep
    };

    // If payment status is available (and successful) or we're on download step
    if (
      (paymentStatus && paymentStatus.payment_status?.toLowerCase() === "completed") ||
      currentStep === "download" ||
      (hasActiveSubscription && currentStep !== "white-label")
    ) {
      return <DownloadStep {...downloadStepProps} />;
    }

    // If payment failed and we're trying to retry, show the appropriate step
    if (paymentStatus && currentStep === "select-plan") {
      // Don't show DownloadStep, let it fall through to the switch statement
    } else if (paymentStatus) {
      // For other payment statuses (failed, cancelled, etc.), show DownloadStep
      return <DownloadStep {...downloadStepProps} />;
    }

    // Show loading indicators
    if (isSubscriptionCheckLoading) {
      return <LoadingSpinner message="Checking subscription status..." />;
    }

    // Render step content based on current step
    switch (currentStep) {
      case "white-label":
        if (isFetching || !isDataFetchComplete) {
          return <LoadingSpinner message="Loading white label settings..." />;
        }
        return (
          <WhiteLabelInfoStep
            onNext={handleWhiteLabelInfoComplete}
            initialFormData={formData}
            hasExistingSettings={hasExistingSettings}
            logoPreviewUrl={logoPreviewUrl}
            isLoading={isLoading}
          />
        );
      case "select-plan":
      case "payment":
        return (
          <BillingAndPaymentStep
            onNext={handleBillingPaymentComplete}
            onBack={handleBackClick}
            apiPricingData={apiPricingData}
            initialSelectedPlan={selectedPlan}
            initialBillingPeriod={billingPeriod}
            taskId={taskId}
            hasActiveSubscription={hasActiveSubscription}
            subscriptionDetails={subscriptionDetails || undefined}
            skipToDownload={handleSkipToDownload}
          />
        );
      default:
        return null;
    }
  }, [
    currentStep,
    formData,
    selectedPlan,
    billingPeriod,
    paymentMethod,
    apiPricingData,
    logoPreviewUrl,
    hasExistingSettings,
    isLoading,
    isFetching,
    isDataFetchComplete,
    isSubscriptionCheckLoading,
    hasActiveSubscription,
    hasActiveProPlan,
    subscriptionDetails,
    handleWhiteLabelInfoComplete,
    handleBillingPaymentComplete,
    handleDownloadComplete,
    handleBackClick,
    handleSkipToDownload,
    handleDownloadBackClick,
    handleDownloadRetryClick,
    onClose, // Add onClose to dependencies
    paymentStatus,
    isLoadingPayment,
    taskId,
    urlPaymentId,
    profileData,
    LoadingSpinner,
  ]);

  // Get the modal title based on the current step
  const getModalTitle = useCallback(() => {
    if (paymentStatus) {
      return "White Label PDF Download";
    }

    const titles = {
      "white-label": "White Label Settings",
      "select-plan": "Select White Label Plan",
      payment: "Payment Processing",
      download: "White Label PDF Download",
    };

    // For users with active subscriptions on non-settings steps, show download title
    if (hasActiveSubscription && currentStep !== "white-label") {
      return titles["download"];
    }

    return titles[currentStep] || "White Label";
  }, [currentStep, paymentStatus, hasActiveSubscription]);

  // Custom scrollbar styles
  const scrollbarStyles = `
    .overflow-y-auto {
      scrollbar-width: thin;
      scrollbar-color: rgba(156, 163, 175, 0.5) rgba(229, 231, 235, 0.5);
    }
    .overflow-y-auto::-webkit-scrollbar {
      width: 8px;
    }
    .overflow-y-auto::-webkit-scrollbar-track {
      background: rgba(229, 231, 235, 0.5);
      border-radius: 4px;
    }
    .overflow-y-auto::-webkit-scrollbar-thumb {
      background-color: rgba(156, 163, 175, 0.5);
      border-radius: 4px;
      border: 2px solid rgba(229, 231, 235, 0.5);
    }
    .overflow-y-auto::-webkit-scrollbar-thumb:hover {
      background-color: rgba(156, 163, 175, 0.7);
    }
  `;

  return (
    <Modal open={isOpen} onClose={onClose} title={getModalTitle()} size="xl">
      <style jsx global>
        {scrollbarStyles}
      </style>
      {renderStepIndicator()}
      {renderStepContent()}
    </Modal>
  );
}
