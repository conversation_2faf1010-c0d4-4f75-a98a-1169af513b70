@import "tailwindcss";

@theme {
  --font-sans: var(--font-nunito-sans), ui-sans-serif, -apple-system, system-ui,
    "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif,
    "Segoe UI Emoji", "Segoe UI Symbol";
  --font-heading: var(--font-space-grotesk), "Space Grotesk", "Inter var",
    ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica, Arial,
    sans-serif;
  --color-primary: var(--color-primary);
  --color-secondary: var(--color-secondary);
  --color-background: var(--background);
  --color-light-gray: var(--color-light-gray);
  --color-light-gray-2: var(--color-light-gray-2);
  --color-light-gray-3: var(--color-light-gray-3);
  --color-light-gray-4: var(--color-light-gray-4);
  --color-light-gray-5: var(--color-light-gray-5);
  --color-light-gray-6: var(--color-light-gray-6);
  --color-primary-gray: var(--color-primary-gray);
  --color-light-blue: var(--color-light-blue);
  --color-primary-yellow: var(--color-primary-yellow);
  --color-primary-red: var(--color-primary-red);
  --color-primary-red-2: var(--color-primary-red-2);
  --color-primary-green: var(--color-primary-green);
  --color-primary-green-2: var(--color-primary-green-2);
  --color-primary-orange: var(--color-primary-orange);
  --color-primary-pink: var(--color-primary-pink);
}

/* max-width: 1240px; */
@utility container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1240px;

  @media (max-width: 64rem) {
    padding-inline: 16px;
  }

  @media (min-width: 64rem) and (max-width: 1300px) {
    padding-inline: 40px;
  }

  @media (min-width: 1300px) {
    padding-inline: 0;
  }
}

:root {
  --background: rgba(244, 244, 244, 1);
  --color-primary: rgba(145, 74, 196, 1);
  --color-secondary: rgba(52, 64, 84, 1);
  --color-light-gray: rgba(224, 224, 224, 1);
  --color-light-gray-2: rgba(234, 234, 234, 1);
  --color-light-gray-3: rgba(108, 117, 125, 1);
  --color-light-gray-4: rgba(175, 175, 175, 1);
  --color-light-gray-5: rgba(205, 205, 205, 1);
  --color-light-gray-6: rgba(244, 244, 244, 1);
  --color-primary-gray: rgba(95, 102, 108, 1);
  --color-light-blue: rgba(212, 242, 255, 1);
  --color-primary-yellow: rgba(255, 204, 0, 1);
  --color-primary-red: rgba(255, 59, 48, 1);
  --color-primary-red-2: rgba(246, 110, 112, 1);
  --color-primary-green: rgba(52, 199, 89, 1);
  --color-primary-green-2: rgba(67, 176, 119, 1);
  --color-primary-orange: rgba(255, 168, 24, 1);
  --color-primary-pink: rgba(255, 165, 218, 1);
}

@layer components {
  .max-w-full__customeLG {
    @apply lg:!max-w-full min-[1300px]:!px-10;
  }

  /* Font family utilities */
  .font-heading {
    font-family: var(--font-space-grotesk), "Space Grotesk", "Inter var",
      ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica, Arial,
      sans-serif !important;
  }

  .font-body {
    font-family: var(--font-nunito-sans), -apple-system, system-ui,
      BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
      sans-serif;
  }

  .font-audit {
    font-family: var(--font-nunito-sans), ui-sans-serif, -apple-system,
      system-ui, "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif,
      "Segoe UI Emoji", "Segoe UI Symbol";
  }

  .font-mono {
    font-family: "JetBrains Mono", "SFMono-Regular", "Consolas",
      "Liberation Mono", "Menlo", "Courier", monospace;
  }

  /* Global heading styles - Space Grotesk font */

  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-space-grotesk), "Space Grotesk", "Inter var",
      ui-sans-serif, -apple-system, system-ui, "Segoe UI", Helvetica, Arial,
      sans-serif !important;
  }

  .btn {
    @apply rounded-lg py-3 px-6 border flex items-center justify-center gap-[9px] cursor-pointer relative overflow-hidden;
  }

  .btn--sm {
    @apply !px-4 !py-2;
  }

  .btn--primary {
    @apply bg-primary border-primary text-white font-bold !leading-[21px] text-base hover:opacity-80 duration-200;
  }

  .btn--outline-light {
    @apply !py-2 !text-sm !border-light-gray !text-secondary/70;
  }

  .btn--outline {
    @apply border-secondary text-secondary font-bold !leading-[21px] text-base;
  }

  .btn--primary__outline {
    @apply bg-primary/10 border-primary text-primary font-bold !leading-[21px] text-base;
    transition-duration: 700ms;
  }

  .btn--primary__outline::before {
    content: "";
    @apply absolute inset-0 w-0 h-full bg-gradient-to-r from-primary to-primary opacity-10;
    transition-duration: 700ms;
  }

  .btn--primary__outline * {
    @apply z-10;
  }

  .btn--primary__outline:hover {
    @apply text-white;
  }

  .btn--primary__outline:hover::before {
    @apply w-full opacity-100;
  }

  .btn--secondary {
    @apply text-secondary bg-secondary/10 border-transparent text-sm !leading-[19px] font-black;
  }

  .btn--disabled {
    @apply bg-gray-400 border-gray-400 text-gray-600 cursor-not-allowed;
  }

  .btn--disabled:hover {
    @apply opacity-100;
  }

  .badge {
    @apply p-2 rounded-lg flex items-center justify-center border border-light-gray text-secondary/80 text-xs font-medium;
  }

  .badge--primary {
    @apply border-primary text-primary bg-primary/10;
  }

  .badge--danger {
    @apply border-primary-red text-primary-red bg-primary-red/15 font-semibold;
  }

  .badge--warning {
    @apply border-primary-yellow text-[#c7a00e] bg-primary-yellow/20 font-semibold;
  }

  .badge--success {
    @apply border-primary-green text-primary-green bg-primary-green/15 font-semibold;
  }

  /* Recommendation badges */
  .recommendation-badge {
    @apply min-w-[70px] sm:min-w-[90px] md:min-w-[100px] text-center py-1 px-2 whitespace-nowrap text-xs font-semibold transition-all duration-100 ease-in-out;
  }

  .recommendation-category {
    @apply bg-gray-100 text-gray-700;
  }

  .recommendation-category.active {
    @apply bg-primary/15 text-primary;
  }

  /* Improved recommendation section styling */
  .recommendations-container {
    @apply shadow-md rounded-lg overflow-hidden;
  }

  .recommendation-item {
    @apply transition-all duration-300 hover:shadow-md;
  }

  table thead tr th {
    @apply text-xs font-semibold text-secondary text-left break-words;
  }

  table thead tr th:first-child {
    @apply pl-2 sm:pl-4;
  }

  table tbody tr td {
    @apply text-sm text-secondary text-left first:pl-2 sm:first:pl-4 overflow-hidden last:pr-2 sm:last:pr-4 first:rounded-l-lg last:rounded-r-lg first:border-l last:border-r border-y border-light-gray group-odd:!border-0 break-words;
  }

  .textField__input {
    @apply appearance-auto border outline-0 border-light-gray p-4 rounded-lg text-secondary placeholder-light-gray;
    transition: box-shadow 0.2s ease-in-out, border-color 0.2s ease-in-out;
  }

  .textField__input:focus {
    @apply border-light-gray;
    box-shadow: 0 0 0 2px rgba(169, 48, 255, 0.658);
    position: relative;
    z-index: 1;
  }
}
html {
  scroll-behavior: smooth;
}
html,
body {
  padding: 0;
  margin: 0;
  min-height: 100vh;
  user-select: none;
  background-color: var(--color-background);
  font-size: 18px; /* Changed from default 16px to 18px */
}

body {
  overflow-x: hidden;
}

* {
  user-select: text;
}

button {
  cursor: pointer;
  outline: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.custome-scrollbar::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.custome-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border: 1px solid var(--color-light-gray);
  border-radius: 4px;
}

.custome-scrollbar::-webkit-scrollbar-thumb {
  background: var(--color-secondary);
  border-radius: 4px;
}

/* **** animations **** */
@keyframes floating {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(25px);
  }
  100% {
    transform: translateY(0);
  }
}

/* Login button pulse animation */
@keyframes login-button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(145, 74, 196, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(145, 74, 196, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(145, 74, 196, 0);
  }
}

.login-btn.animate-pulse {
  animation: login-button-pulse 2s infinite;
}

/* Enhanced login button styles for better UX */
.login-btn {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
  will-change: transform, background-color, color, box-shadow;
}

.login-btn * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}

.login-btn:hover {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.floating-animate {
  animation: floating 7s ease-in-out infinite;
}

@keyframes floating-reverse {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-25px);
  }
  100% {
    transform: translateY(0);
  }
}

.floating-animate-reverse {
  animation: floating-reverse 7s ease-in-out infinite;
}

@keyframes our-customer-animate-top {
  100% {
    transform: translateX(calc(-322px * 12));
  }
}

@keyframes our-customer-animate-bottom {
  100% {
    transform: translateX(calc(322px * 12));
  }
}

/* **** charts **** */
.apexcharts-radar-series line {
  stroke: var(--color-light-gray-2) !important;
  stroke-width: 0.7px !important;
}

.apexcharts-xaxis-label {
  fill: var(--color-light-gray-3) !important;
  color: var(--color-light-gray-3) !important;

  font-weight: 800 !important;
  font-family: var(--font-sans) !important;
}

/* Clickable radar chart labels */
.radar-chart-clickable-label {
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

/* Enhanced clickable labels for radar chart - multiple selectors for better coverage */
.apexcharts-radar text[style*="cursor: pointer"],
.apexcharts-radar text[data-clickable="true"],
.apexcharts-xaxis-label[style*="cursor: pointer"],
text[data-clickable="true"] {
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

.apexcharts-radar text[style*="cursor: pointer"]:hover,
.apexcharts-radar text[data-clickable="true"]:hover,
.apexcharts-xaxis-label[style*="cursor: pointer"]:hover,
text[data-clickable="true"]:hover {
  fill: var(--color-primary) !important;
  opacity: 1 !important;
}

/* Ensure non-clickable elements don't have hover effects */
.apexcharts-radar
  text:not([style*="cursor: pointer"]):not([data-clickable="true"]) {
  cursor: default !important;
}

/* Enhanced hover effects for all clickable text elements in charts */
.apexcharts-xaxis-label tspan {
  transition: all 0.2s ease !important;
}

/* Force pointer cursor for clickable elements */
[data-clickable="true"] {
  cursor: pointer !important;
  pointer-events: auto !important;
}

.recharts-layer .recharts-line-dots *:not(:last-child) {
  display: none;
}

.recharts-cartesian-grid-vertical line:first-child,
.recharts-cartesian-grid-vertical line:last-child {
  stroke: var(--color-light-gray-5) !important;
}

/* **** loading **** */
.loader {
  display: block;
  --height-of-loader: 6px;
  --loader-color: #0071e2;
  width: 150px;
  height: var(--height-of-loader);
  border-radius: 30px;
  background-color: rgba(0, 0, 0, 0.2);
  position: relative;
}

/* Custom animation for analyzing text */
@keyframes analyzing-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Skeleton animation for analyzing text - optimized for performance */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.analyzing-text {
  /* Base color is a dark gray */
  color: transparent; /* Must be transparent for background-clip to work */

  /* Enhanced gradient overlay for the skeleton effect - darker colors */
  background: linear-gradient(
    90deg,
    rgba(80, 80, 85, 0.9) 0%,
    rgba(60, 60, 65, 0.95) 35%,
    rgb(120, 120, 125) 50%,
    rgba(70, 70, 75, 0.95) 65%,
    rgba(50, 50, 55, 0.9) 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;

  /* Reduced animation speed for better performance */
  animation: skeleton-loading 3s infinite linear;

  /* Typography improvements */
  font-weight: 900;
  letter-spacing: 0.4em; /* Increased letter spacing to match JSX */

  /* Use transform: translateZ(0) to enable GPU acceleration */
  transform: translateZ(0);
  will-change: background-position;
}

/* Animated dots for the analyzing text - ultra-simple approach that always works */
.analyzing-text .dots::after {
  content: "";
  animation: dots-simple 1.2s infinite;
  display: inline-block;
  width: 1em;
  text-align: left;
  margin-left: 0.2em;
  color: rgba(80, 80, 85, 0.95);
  font-weight: 900;
  letter-spacing: 0.1em;
}

/* Shimmer animation for skeleton loading */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes dots-simple {
  0% {
    content: ".  ";
    opacity: 1; /* First dot - highest opacity */
  }
  33% {
    content: ".. ";
    opacity: 0.7; /* Two dots - medium opacity */
  }
  66% {
    content: "...";
    opacity: 0.4; /* Three dots - lowest opacity */
  }
  100% {
    content: ".  ";
    opacity: 1; /* Back to first dot */
  }
}

/* Fallback for browsers that don't support content animation */
.analyzing-text .dots {
  display: inline-block;
  width: 1em;
  margin-left: 0.2em;
  color: rgba(80, 80, 85, 0.95);
  font-weight: 900;
  animation: dots-opacity 1.2s infinite;
}

@keyframes dots-opacity {
  0%,
  33% {
    opacity: 1; /* First dot - highest opacity */
  }
  34%,
  66% {
    opacity: 0.7; /* Second dot - medium opacity */
  }
  67%,
  100% {
    opacity: 0.4; /* Third dot - lowest opacity */
  }
}

.loader::before {
  content: "";
  position: absolute;
  background: var(--loader-color);
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  border-radius: 30px;
  animation: moving 1s ease-in-out infinite;
}

@keyframes moving {
  50% {
    width: 100%;
  }

  100% {
    width: 0;
    right: 0;
    left: unset;
  }
}

/* Hide scrollbar but keep functionality */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Modal scroll lock styles */
.modal-scroll-locked {
  position: fixed !important;
  overflow: hidden !important;
  width: 100% !important;
}

/* Prevent scroll restoration issues */
.modal-scroll-locked html {
  overflow: hidden !important;
}

/* Ensure modal content remains scrollable */
.modal-content-scrollable {
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* Smooth transition for scroll lock */
body {
  transition: none !important; /* Disable transitions during scroll lock to prevent visual glitches */
}

/* for having no default style on auto complete */
input:-webkit-autofill {
  box-shadow: 0 0 0px 1000px rgba(255, 255, 255, 0) inset !important;
  -webkit-text-fill-color: #000;
}

/* Badge styles for recommendations */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  text-transform: capitalize;
}

.badge--success {
  background-color: rgba(76, 175, 80, 0.15);
  color: #2e7d32;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.badge--warning {
  background-color: rgba(255, 193, 7, 0.15);
  color: #f57c00;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.badge--danger {
  background-color: rgba(244, 67, 54, 0.15);
  color: #d32f2f;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* A3 page setup */
@page {
  size: A3 portrait;
  margin: 0.5cm;
}

/* PDF container styles */
.pdf-container {
  margin: 0 auto;
  padding: 0;
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 1300px;
  height: auto;
  overflow: visible;
  transform-origin: top center;
}

.pdf-container > div {
  margin-top: 0;
  width: 100%;
}

/* PDF modal content styles */
.pdf-modal-content {
  padding-top: 0 !important;
  height: calc(100% - 60px) !important; /* Adjust for header height */
  display: flex;
  flex-direction: column;
}

/* Mobile keyboard handling for modals */
@media (max-width: 768px) {
  /* Improve scrolling behavior when keyboard is visible */
  .pdf-modal-content {
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    overscroll-behavior: contain; /* Prevent scroll chaining */
  }

  /* Ensure inputs are visible when focused */
  input:focus,
  textarea:focus {
    position: relative;
    z-index: 2;
  }

  /* Additional styles for when keyboard is visible */
  .pdf-modal-content.keyboard-visible {
    padding-bottom: 20px;
    margin-bottom: 20px;
  }

  /* Adjust form elements for better visibility with keyboard */
  .keyboard-visible input,
  .keyboard-visible textarea {
    font-size: 16px; /* Prevent iOS zoom on focus */
    margin-bottom: 16px; /* Add more space between inputs */
  }

  /* Ensure buttons remain accessible when keyboard is visible */
  .keyboard-visible button[type="submit"],
  .keyboard-visible .btn {
    margin-top: 8px;
    margin-bottom: 20px;
  }
}

/* Print-specific styles for PDF */
@media print {
  /* Hide everything except the PDF when printing */
  body * {
    visibility: hidden;
  }

  .print-pdf,
  .print-pdf * {
    visibility: visible;
  }

  .print-pdf {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  .pdf-container .grid {
    display: grid !important;
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .pdf-container .grid-cols-1 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .pdf-container .overall-scores-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }

  .pdf-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .pdf-container * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    text-transform: none !important;
    font-variant: normal !important;
  }

  /* Ensure consistent font sizes */
  .pdf-container h1 {
    font-size: 24pt !important;
  }
  .pdf-container h2 {
    font-size: 20pt !important;
  }
  .pdf-container h3 {
    font-size: 16pt !important;
  }
  .pdf-container h4 {
    font-size: 14pt !important;
  }
  .pdf-container h5 {
    font-size: 12pt !important;
  }
  .pdf-container p,
  .pdf-container li,
  .pdf-container td {
    font-size: 10pt !important;
  }

  /* Ensure all backgrounds and colors print */
  .pdf-container .bg-green-500,
  .pdf-container .bg-yellow-500,
  .pdf-container .bg-red-500,
  .pdf-container .bg-primary,
  .pdf-container .bg-green-100,
  .pdf-container .bg-yellow-100,
  .pdf-container .bg-red-100 {
    print-color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
  }

  /* PDF section styling - no page breaks */
  .print-section {
    margin-bottom: 2rem !important;
    padding: 1.5rem !important;
    /* border: 1px solid #e5e7eb !important; */
    border-radius: 0.75rem !important;
    background: white !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  /* Combined first page section optimization */
  .print-section .first-page-combined {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    min-height: auto !important;
  }

  .print-section .first-page-combined > div {
    flex-shrink: 0 !important;
  }

  /* Ensure header section is compact in combined layout */
  .first-page-combined .header-section {
    margin-bottom: 0.5rem !important;
  }

  /* Fix logo centering in print/PDF header */
  .pdf-container .header-section .flex.items-center {
    align-items: center !important;
  }

  .pdf-container .pdf-header-logo {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 128px !important;
    height: 128px !important;
  }

  .pdf-container .pdf-header-logo > div {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    height: 100% !important;
  }

  .pdf-container .pdf-header-logo img {
    object-fit: contain !important;
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
    display: block !important;
    margin: auto !important;
  }

  /* Combined screenshot and scores section optimization */
  .print-section .screenshot-scores-combined {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    min-height: auto !important;
  }

  .print-section .screenshot-scores-combined > div {
    flex-shrink: 0 !important;
  }

  .pdf-section-box {
    margin-bottom: 1.5rem !important;
  }

  /* Recommendations sections should use full width in print */
  .recommendations-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* Ensure consistent spacing */
  .pdf-container .gap-4 {
    gap: 1rem !important;
  }

  .pdf-container .gap-6 {
    gap: 1.5rem !important;
  }

  /* Fix for images */
  .pdf-container img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    object-fit: contain !important;
    visibility: visible !important;
  }

  /* Force image loading */
  .pdf-container img[loading="lazy"] {
    /* Use attribute selector instead of loading property */
    display: block !important;
    visibility: visible !important;
  }

  /* Ensure Next.js images are visible */
  .pdf-container span[style*="box-sizing: border-box"] {
    display: block !important;
    visibility: visible !important;
  }

  .pdf-container span[style*="box-sizing: border-box"] img {
    object-fit: contain !important;
    visibility: visible !important;
  }

  /* Fix for SVG elements */
  .pdf-container svg {
    display: block !important;
    visibility: visible !important;
  }

  /* Reset scaling for print */
  .pdf-container {
    transform: scale(1) !important;
    max-width: 100% !important;
  }

  /* Ensure modal content is properly displayed */
  .pdf-modal-content {
    height: auto !important;
    overflow: visible !important;
  }

  /* Watermark and logo visibility in print */
  .pdf-container .watermark-container,
  .pdf-container .section-watermark,
  .pdf-container .logo-watermark {
    visibility: visible !important;
    display: flex !important;
    opacity: 1 !important;
  }

  .pdf-container .print-watermark-image {
    visibility: visible !important;
    display: block !important;
    opacity: 0.8 !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Ensure brand logos are clearly visible */
  .pdf-container .section-logo-watermark {
    visibility: visible !important;
    display: flex !important;
    opacity: 1 !important;
  }

  /* Text watermarks should be visible but subtle */
  .pdf-container .section-watermark > div {
    opacity: 0.25 !important;
    color: #9ca3af !important;
    text-transform: none !important;
  }

  /* Ensure watermark text is never transformed - comprehensive rules */
  .pdf-container .watermark-container > div,
  .pdf-container .section-watermark > div,
  .pdf-container .print-watermark > div,
  .pdf-container .watermark-container,
  .pdf-container .section-watermark,
  .pdf-container .print-watermark {
    text-transform: none !important;
    font-variant: normal !important;
    text-rendering: optimizeLegibility !important;
  }

  /* Force specific watermark text styling */
  .pdf-container [data-watermark],
  .pdf-container [data-watermark] *,
  .watermark-container *,
  .section-watermark *,
  .print-watermark * {
    text-transform: none !important;
    font-variant: normal !important;
  }
}
.mobile-zoom {
  zoom: 0.9;
}

@media (max-width: 768px) {
  .mobile-zoom {
    zoom: 0.5;
  }
}

@media print {
  .mobile-zoom {
    zoom: 0.85 !important;
  }
}

/* Enhanced text size improvements for better readability */
/* Override Tailwind's small text classes to be bigger */
.text-xs {
  font-size: 0.875rem !important; /* 14px instead of 12px */
  line-height: 1.25rem !important;
}

.text-sm {
  font-size: 1rem !important; /* 16px instead of 14px */
  line-height: 1.5rem !important;
}

/* Make badge text more readable */
.badge {
  font-size: 0.875rem !important; /* 14px instead of 12px */
}

/* Make table header text bigger */
table thead tr th {
  font-size: 0.875rem !important; /* 14px instead of 12px */
}

/* Make table body text bigger */
table tbody tr td {
  font-size: 1rem !important; /* 16px instead of 14px */
}

/* Make button text more readable */
.btn {
  font-size: 1rem !important; /* 16px base size */
}

.btn--sm {
  font-size: 0.875rem !important; /* 14px for small buttons */
}

/* Make form input text bigger */
.textField__input {
  font-size: 1rem !important; /* 16px */
}

/* Make recommendation badge text bigger */
.recommendation-badge {
  font-size: 0.875rem !important; /* 14px instead of 12px */
}

.apexcharts-xaxis-label {
  font-size: 10px !important;
}

/* Ensure overview containers maintain proper sizing with larger text */
.overview-container {
  box-sizing: border-box;
  overflow: hidden;
}

/* Ensure buttons stay within their containers */
.btn {
  box-sizing: border-box;
  max-width: 100%;
  word-wrap: break-word;
}

/* Ensure overview details containers handle larger text properly */
.bg-light-blue {
  box-sizing: border-box;
  overflow: hidden;
}

/* Prevent text selection on Swiper navigation buttons and banner area */
.swiper,
.swiper-slide,
.swiper-wrapper,
.history-arrow-left,
.history-arrow-right,
.banner-item,
.banner-item *,
.banner-image,
.banner-overlay {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* AGGRESSIVE: Prevent image dragging and selection in banner */
.banner-item img,
.banner-image,
.swiper-slide img,
.swiper img,
img[src*="blog"],
img[alt*="SEO"],
img[alt*="Content"] {
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
  pointer-events: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  outline: none !important;
  border: none !important;
}

/* Disable all mouse events on banner images */
.banner-item img,
.swiper-slide img {
  pointer-events: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Disable selection on image containers but keep them functional */
.banner-item div:has(img),
.swiper-slide div:has(img) {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Additional protection for Next.js Image components */
.banner-item img[data-nimg],
.banner-image[data-nimg],
.swiper-slide img[data-nimg] {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  pointer-events: none !important;
  -webkit-user-drag: none !important;
  -moz-user-drag: none !important;
  user-drag: none !important;
}

/* Ensure navigation buttons work properly */
.history-arrow-left,
.history-arrow-right {
  pointer-events: auto !important;
  touch-action: manipulation !important;
}

/* Aggressive prevention of selection in banner area */
.swiper-container,
.swiper-wrapper,
.swiper-slide,
.banner-item,
.banner-item *:not(.history-arrow-left):not(.history-arrow-right) {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* Prevent selection on mousedown for banner elements */
.banner-item,
.banner-item * {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Override any global text selection rules for banner */
.banner-item h1,
.banner-item h2,
.banner-item h3,
.banner-item h4,
.banner-item h5,
.banner-item h6,
.banner-item p,
.banner-item span,
.banner-item div {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Disable selection highlighting completely for banner area */
.banner-item::selection,
.banner-item *::selection,
.swiper-slide::selection,
.swiper-slide *::selection,
.banner-item img::selection {
  background: transparent !important;
  color: inherit !important;
}

.banner-item::-moz-selection,
.banner-item *::-moz-selection,
.swiper-slide::-moz-selection,
.swiper-slide *::-moz-selection,
.banner-item img::-moz-selection {
  background: transparent !important;
  color: inherit !important;
}

/* Balanced approach: prevent selection but keep images functional */
.swiper img,
.banner-item img {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-user-drag: none !important;
  -moz-user-drag: none !important;
  user-drag: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* Mobile-specific table styling for PageSpeed tables */
@media (max-width: 768px) {
  /* Make PageSpeed tables more compact on mobile */
  .pagespeed-table table thead tr th {
    font-size: 0.75rem !important; /* 12px - smaller header text */
    padding: 0.5rem 0.25rem !important; /* Reduced padding */
  }

  .pagespeed-table table tbody tr td {
    font-size: 0.875rem !important; /* 14px - smaller body text */
    padding: 0.5rem 0.25rem !important; /* Reduced padding */
  }

  .pagespeed-table table tbody tr {
    height: 40px !important; /* Reduced row height */
  }

  /* Reduce spacing between table sections */
  .pagespeed-table > div {
    gap: 1rem !important; /* Reduced gap between tables */
  }

  /* Make table container more compact */
  .pagespeed-table .w-full {
    margin-bottom: 0.5rem !important;
  }
}

/* Responsive text adjustments for overview sections */
@media (max-width: 1024px) {
  .bg-light-blue {
    padding: 1.5rem !important; /* Reduce padding on smaller screens */
  }

  .bg-light-blue h4 {
    font-size: 1.125rem !important; /* 18px instead of larger sizes */
    line-height: 1.4 !important;
  }

  .bg-light-blue .btn {
    font-size: 0.875rem !important; /* 14px for mobile buttons */
    padding: 0.75rem 1rem !important;
  }
}
.text-xs {
  font-size: 0.75rem !important;
}
.text-sm {
  font-size: 0.875rem !important; /* 0.875rem (14px) */
}
