"use client";

const CategoryHeaderSkeleton: React.FC = () => {
  return (
    <div className="mb-6">
      <style jsx>{`
        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
        .shimmer {
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: shimmer 2s infinite linear;
        }
      `}</style>
      
      {/* Breadcrumb skeleton */}
      <div className="flex flex-wrap gap-2 mb-4">
        <div className="h-4 shimmer rounded w-8"></div>
        <div className="h-4 w-1 bg-gray-300"></div>
        <div className="h-4 shimmer rounded w-20"></div>
      </div>
      
      {/* Category title skeleton */}
      <div className="mb-2">
        <div className="h-8 lg:h-12 shimmer rounded w-48 lg:w-64"></div>
      </div>
      
      {/* Category description skeleton */}
      <div className="mt-2">
        <div className="h-4 shimmer rounded w-full max-w-md"></div>
      </div>
    </div>
  );
};

export default CategoryHeaderSkeleton;
