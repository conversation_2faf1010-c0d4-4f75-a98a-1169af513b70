import React from "react";

type HeaderContentProps = {
  content: string[];
};

export default function HeaderContent({ content }: HeaderContentProps) {
  if (!content || content.length === 0) {
    return <p className="text-sm text-secondary/60">No content available</p>;
  }

  return (
    <div className="mt-2 space-y-2">

      <ul className="list-disc pl-5 space-y-1">
        {content.map((item, index) => (
          <li key={index} className="text-sm text-secondary/80">
            {item}
          </li>
        ))}
      </ul>
    </div>
  );
}
