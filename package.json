{"name": "seo-analyser-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:analyze": "cross-env ANALYZE=true next build", "build:production": "cross-env NODE_ENV=production next build", "start": "next start -p 3000", "start:production": "cross-env NODE_ENV=production next start -p 3001", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "server": "sudo node server.js", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out node_modules/.cache"}, "dependencies": {"@babel/runtime": "^7.28.3", "@hookform/resolvers": "^5.2.1", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.85.5", "apexcharts": "^5.3.4", "axios": "^1.11.0", "framer-motion": "^12.23.12", "fs": "^0.0.1-security", "https": "^1.0.0", "next": "^15.5.2", "next-auth": "^5.0.0-beta.27", "react": "^19.1.1", "react-apexcharts": "^1.7.0", "react-chrome-dino": "^0.1.3", "react-circular-progressbar": "^2.2.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-masonry-css": "^1.0.16", "react-to-print": "^3.1.1", "react-toastify": "^11.0.5", "react-tooltip": "^5.29.1", "react-world-flags": "^1.6.0", "recharts": "^3.1.2", "sharp": "^0.34.3", "swiper": "^11.2.10", "yup": "^1.7.0", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-world-flags": "^1.6.0", "cross-env": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}