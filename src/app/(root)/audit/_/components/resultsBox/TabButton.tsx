"use client";

type Props = {
  value: string;
  label: string;
  isActive: boolean;
  onClick: (value: string) => void;
  className?: string;
};

export default function TabButton({
  isActive,
  label,
  onClick,
  value,
  className = "",
}: Props) {
  return (
    <button
      onClick={() => onClick(value)}
      className={`
        font-semibold
        px-3 sm:px-4 md:px-5 lg:px-6
        py-2 sm:py-2.5 
        rounded-lg
        text-xs sm:text-sm
        transition-all duration-200 ease-in-out
        border 
        ${className}
        ${
          isActive
            ? "bg-primary/8 text-primary border-1 border-transparent"
            : "bg-[#F4F4F4] border-[#E0E0E0] text-[#344054CC] hover:bg-gray-200 hover:text-secondary/90"
        }
      `}
    >
      {label}
    </button>
  );
}
