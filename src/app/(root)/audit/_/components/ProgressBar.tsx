"use client";
import { useEffect, useState, useRef, useCallback } from "react";

type ProgressBarProps = {
  progress: number;
  isComplete: boolean;
};

export default function ProgressBar({
  progress,
  isComplete,
}: ProgressBarProps) {
  const [isSticky, setIsSticky] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [showCompletionMessage, setShowCompletionMessage] = useState(false);

  // Enhanced progress state management
  const [displayedProgress, setDisplayedProgress] = useState(0);
  const [lastActualProgress, setLastActualProgress] = useState(0);
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());

  // Refs for managing intervals and preventing memory leaks
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const estimationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced progress calculation with interpolation
  const calculateSmoothedProgress = useCallback(
    (actualProgress: number, timeSinceLastUpdate: number): number => {
      // If we're complete, always return 100%
      if (isComplete) return 100;

      // If actual progress is 0, return 0
      if (actualProgress === 0) return 0;

      // Calculate estimated progress increment based on time elapsed
      // Assume roughly 60-90 seconds for full analysis, so ~1-1.5% per second base rate
      const baseIncrementPerSecond = 1.2;
      const timeBasedIncrement =
        (timeSinceLastUpdate / 1000) * baseIncrementPerSecond;

      // Apply diminishing returns - slower progress as we get higher
      const diminishingFactor = Math.max(0.1, 1 - (actualProgress / 100) * 0.7);
      const adjustedIncrement = timeBasedIncrement * diminishingFactor;

      // Calculate estimated progress
      const estimatedProgress = actualProgress + adjustedIncrement;

      // Never exceed 95% unless we're actually complete
      // This ensures we never show 100% prematurely
      const maxAllowedProgress = isComplete ? 100 : 95;

      return Math.min(estimatedProgress, maxAllowedProgress);
    },
    [isComplete]
  );

  // Handle scroll events to make the progress bar sticky
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 40) {
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Enhanced progress management effect
  useEffect(() => {
    // Clear any existing intervals
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    if (estimationIntervalRef.current) {
      clearInterval(estimationIntervalRef.current);
      estimationIntervalRef.current = null;
    }

    // If we're complete, immediately set to 100%
    if (isComplete) {
      setDisplayedProgress(100);
      return;
    }

    // If actual progress changed, update our tracking
    if (progress !== lastActualProgress) {
      setLastActualProgress(progress);
      setLastUpdateTime(Date.now());

      // Smoothly animate to the new actual progress
      const startProgress = displayedProgress;
      const targetProgress = progress;
      const progressDiff = targetProgress - startProgress;

      if (progressDiff !== 0) {
        let step = 0;
        const steps = 20; // Number of animation steps
        const stepSize = progressDiff / steps;

        progressIntervalRef.current = setInterval(() => {
          step++;
          const newProgress = startProgress + stepSize * step;

          if (step >= steps) {
            setDisplayedProgress(targetProgress);
            if (progressIntervalRef.current) {
              clearInterval(progressIntervalRef.current);
              progressIntervalRef.current = null;
            }
          } else {
            setDisplayedProgress(newProgress);
          }
        }, 50); // 50ms intervals for smooth animation
      } else {
        setDisplayedProgress(progress);
      }
    }

    // Start estimation interval for continuous progress between API updates
    if (progress > 0 && progress < 95 && !isComplete) {
      estimationIntervalRef.current = setInterval(() => {
        const currentTime = Date.now();
        const timeSinceLastUpdate = currentTime - lastUpdateTime;

        // Only apply estimation if enough time has passed since last real update
        if (timeSinceLastUpdate > 2000) {
          // 2 seconds
          const smoothedProgress = calculateSmoothedProgress(
            lastActualProgress,
            timeSinceLastUpdate
          );
          setDisplayedProgress(smoothedProgress);
        }
      }, 500); // Update every 500ms for smooth visual progress
    }

    // Cleanup function
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      if (estimationIntervalRef.current) {
        clearInterval(estimationIntervalRef.current);
        estimationIntervalRef.current = null;
      }
    };
  }, [
    progress,
    isComplete,
    lastActualProgress,
    lastUpdateTime,
    displayedProgress,
    calculateSmoothedProgress,
  ]);

  // Handle completion animation
  useEffect(() => {
    if (isComplete) {
      // Show completion message
      setShowCompletionMessage(true);

      // After showing completion message for 4 seconds, start fade out
      const fadeOutTimer = setTimeout(() => {
        // Start the fade-out animation
        document
          .getElementById("progress-bar-container")
          ?.classList.add("animate-fade-out");

        // After animation completes, hide the component
        const hideTimer = setTimeout(() => {
          setIsVisible(false);
        }, 800); // Animation duration + small buffer

        return () => {
          clearTimeout(hideTimer);
        };
      }, 2000);

      return () => clearTimeout(fadeOutTimer);
    } else {
      // Reset states when not complete
      setShowCompletionMessage(false);
      setIsVisible(true);
    }
  }, [isComplete]);

  // If not visible, don't render anything
  if (!isVisible) {
    return null;
  }

  return (
    <div
      id="progress-bar-container"
      className={`w-full transition-all duration-700 ease-in-out ${
        isSticky ? "fixed top-0 left-0 z-50 shadow-md" : "relative"
      }`}
    >
      <div className="bg-gray-50/90  h-12 w-full flex items-center justify-center border-b border-gray-200">
        <div className="container mx-auto px-4 w-full relative">
          {/* Progress text - centered absolutely over the progress bar */}
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div
              className={`text-sm font-semibold tracking-wide flex items-center bg-white/80 px-3 py-0.5 rounded-full shadow-sm backdrop-blur-sm ${
                showCompletionMessage ? "animate-pulse" : ""
              }`}
            >
              {isComplete ? (
                <>
                  <svg
                    className="w-4 h-4 text-green-500 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                  <span className="text-green-700">Analysis Complete</span>
                </>
              ) : (
                <>
                  <svg
                    className="w-4 h-4 text-primary mr-2 animate-spin"
                    fill="none"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  <span className="text-primary font-medium">
                    {displayedProgress < 30 ? (
                      <>
                        Starting analysis...{" "}
                        <span className="font-bold">
                          {Math.round(displayedProgress)}%
                        </span>
                      </>
                    ) : displayedProgress < 60 ? (
                      <>
                        Analyzing website...{" "}
                        <span className="font-bold">
                          {Math.round(displayedProgress)}%
                        </span>
                      </>
                    ) : displayedProgress < 90 ? (
                      <>
                        Almost there...{" "}
                        <span className="font-bold">
                          {Math.round(displayedProgress)}%
                        </span>
                      </>
                    ) : (
                      <>
                        Finalizing results...{" "}
                        <span className="font-bold">
                          {Math.round(displayedProgress)}%
                        </span>
                      </>
                    )}
                  </span>
                </>
              )}
            </div>
          </div>

          {/* Progress bar */}
          <div className="w-full bg-gray-100 rounded-full h-3 overflow-hidden shadow-inner">
            <div
              className={`h-3 rounded-full relative ${
                isComplete ? "bg-green-500" : "bg-primary"
              }`}
              style={{
                width: `${displayedProgress}%`,
                transition: "width 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              }}
            >
              {/* Animated gradient effect for in-progress state */}
              {!isComplete &&
                displayedProgress > 0 &&
                displayedProgress < 100 && (
                  <div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                    style={{
                      animation: "shimmer 2s infinite linear",
                      backgroundSize: "200% 100%",
                    }}
                  ></div>
                )}
            </div>
          </div>
        </div>
      </div>

      {/* Add keyframes for shimmer and fade-out animations */}
      <style jsx>{`
        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }

        @keyframes fade-out {
          0% {
            opacity: 1;
          }
          100% {
            opacity: 0;
            transform: translateY(-100%);
          }
        }

        .animate-fade-out {
          animation: fade-out 0.7s ease-in-out forwards;
        }
      `}</style>
    </div>
  );
}
