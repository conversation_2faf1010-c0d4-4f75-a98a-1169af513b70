import { motion } from "framer-motion";
import { FlagIcon, DocumentCrossIcon, ChainIcon } from "@/ui/icons/general";
import { CategoryOutlineIcon } from "@/ui/icons/categories";
import StateCard from "../../StateCard";
import OverallSection from "../OverallSection";
import ShowMoreSection from "../usability/ShowMoreSection";
import Table from "@/ui/Table";
import { LinksAnalysisData } from "./types";
import {
  formatMetricValue,
  formatMetricValueWithPrefix,
  getMetricInfo,
  getActiveData,
} from "./utils";

type BrokenLinksTabProps = {
  data: LinksAnalysisData;
  itemVariants: any;
};

export default function BrokenLinksTab({
  data,
  itemVariants,
}: BrokenLinksTabProps) {
  const brokenLinks = data.broken_links;
  const activeData = getActiveData("broken_links", data);

  if (!brokenLinks) {
    return (
      <div className="col-span-full p-4 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-red-700 text-sm font-medium">
          ⚠️ Something went wrong while getting broken links data
        </p>
      </div>
    );
  }

  return (
    <>
      <motion.div variants={itemVariants}>
        <OverallSection
          title={activeData.title}
          description={activeData.description}
        />
      </motion.div>

      <motion.div
        variants={itemVariants}
        className="w-full grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4"
      >
        <StateCard
          icon={<ChainIcon className="w-6 h-6" />}
          value={formatMetricValue(
            brokenLinks,
            "links_checked_on_page",
            0,
            "count"
          )}
          label="Links Checked"
        />
        <StateCard
          icon={<DocumentCrossIcon className="w-6 h-6" />}
          value={formatMetricValue(
            brokenLinks,
            "broken_count_on_page",
            0,
            "count"
          )}
          label="Broken Links"
        />
        <StateCard
          icon={<FlagIcon className="w-6 h-6" />}
          value={formatMetricValue(
            brokenLinks,
            "broken_percentage_on_page",
            0,
            "percentage"
          )}
          label="Broken %"
        />
        <StateCard
          icon={<CategoryOutlineIcon className="w-6 h-6" />}
          value={
            brokenLinks?.broken_links_detail_sample?.length?.toString() || "0"
          }
          label="Sample Count"
        />
      </motion.div>

      {/* Detailed sections */}
      <div className="space-y-6 mt-6">
        {/* Broken Count on Page Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Broken Links Count"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    brokenLinks,
                    "broken_count_on_page",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(brokenLinks, "broken_count_on_page")
                  ?.description ||
                  "Number of links found to be broken or problematic during the on-page check."}
              </div>
            }
            importance={
              getMetricInfo(brokenLinks, "broken_count_on_page")?.importance
            }
            recommendation={
              getMetricInfo(brokenLinks, "broken_count_on_page")
                ?.recommendation || undefined
            }
            icon={<DocumentCrossIcon className="w-12 h-12 text-red-600" />}
          >
            {/* Broken Links Table */}
            {brokenLinks?.broken_links_detail_sample &&
              brokenLinks.broken_links_detail_sample.length > 0 && (
                <div className="mt-4">
                  <h5 className="font-medium text-gray-900 mb-3">
                    Broken Links Found
                  </h5>
                  <Table>
                    <Table.Header>
                      <th className="pb-4 whitespace-nowrap">URL</th>
                      <th className="text-center pb-4">Status</th>
                      <th className="text-center pb-4">Error Code</th>
                    </Table.Header>
                    <Table.Body>
                      {brokenLinks.broken_links_detail_sample.map(
                        (item, index) => (
                          <Table.Row key={index}>
                            <td className="py-4 pr-2 sm:pr-4 break-words">
                              <a
                                href={item.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-primary hover:underline text-xs sm:text-sm"
                              >
                                {item.url || "N/A"}
                              </a>
                            </td>
                            <td className="py-4 text-center">
                              <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                                {item.status || "Broken"}
                              </span>
                            </td>
                            <td className="py-4 text-center text-xs sm:text-sm">
                              {item.error_code || "N/A"}
                            </td>
                          </Table.Row>
                        )
                      )}
                    </Table.Body>
                  </Table>
                </div>
              )}
          </ShowMoreSection>
        </div>

        {/* Links Checked on Page Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Links Checked"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    brokenLinks,
                    "links_checked_on_page",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(brokenLinks, "links_checked_on_page")
                  ?.description ||
                  "Number of links on this specific page that were checked for broken status (e.g., 404 errors, timeouts)."}
              </div>
            }
            importance={
              getMetricInfo(brokenLinks, "links_checked_on_page")?.importance
            }
            recommendation={
              getMetricInfo(brokenLinks, "links_checked_on_page")
                ?.recommendation || undefined
            }
            icon={<ChainIcon className="w-12 h-12 text-blue-600" />}
          />
        </div>

        {/* Broken Percentage on Page Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Broken Links Percentage"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    brokenLinks,
                    "broken_percentage_on_page",
                    0,
                    "percentage"
                  )}
                </div>
                {getMetricInfo(brokenLinks, "broken_percentage_on_page")
                  ?.description ||
                  "Percentage of checked links on this page that were found to be broken."}
              </div>
            }
            importance={
              getMetricInfo(brokenLinks, "broken_percentage_on_page")
                ?.importance
            }
            recommendation={
              getMetricInfo(brokenLinks, "broken_percentage_on_page")
                ?.recommendation || undefined
            }
            icon={<FlagIcon className="w-12 h-12 text-orange-600" />}
          />
        </div>
      </div>
    </>
  );
}
