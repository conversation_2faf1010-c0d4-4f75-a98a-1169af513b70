type Props = {
  title: string;
  children?: React.ReactNode;
};

export default function LinkCard({ title, children }: Props) {
  if (!title) return null;

  return (
    <div className="flex items-center gap-2.5 p-2 rounded bg-light-gray-6 overflow-hidden">
      {children}
      <p className="font-semibold text-xs sm:text-sm text-secondary truncate">
        url:
      </p>
      <span className="text-xs sm:text-sm text-secondary/70 truncate">
        {title}
      </span>
    </div>
  );
}
