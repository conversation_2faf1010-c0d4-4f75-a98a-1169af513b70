import "@/app/globals.css"

import nunitoSansFont from "@/constants/localFont";
import TopBg from "./components/TopBg";

export default function RootLayout({
    children,
  }: Readonly<{
    children: React.ReactNode;
  }>) {
    return (
      <html lang="en">
        <body
          className={`${nunitoSansFont.variable} font-[family-name:var(--font-nunito-sans)] antialiased relative min-h-screen`}
        >
            <TopBg/>
            {children}
        </body>
      </html>
    );
  }
  