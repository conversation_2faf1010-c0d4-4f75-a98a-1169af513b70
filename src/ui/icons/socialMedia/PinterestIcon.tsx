import { SVGProps } from "react";

export function PinterestIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_pinterest)">
        <path
          d="M12 0C5.373 0 0 5.373 0 12C0 17.302 3.438 21.8 8.207 23.387C8.006 22.498 7.831 21.159 8.082 20.207C8.307 19.32 9.395 14.8 9.395 14.8C9.395 14.8 9.078 14.166 9.078 13.256C9.078 11.85 9.903 10.807 10.946 10.807C11.834 10.807 12.254 11.454 12.254 12.235C12.254 13.123 11.672 14.453 11.372 15.677C11.123 16.692 11.878 17.52 12.88 17.52C14.692 17.52 16.104 15.608 16.104 12.786C16.104 10.291 14.309 8.575 11.956 8.575C9.165 8.575 7.567 10.681 7.567 13.09C7.567 13.978 7.928 14.934 8.388 15.448C8.463 15.535 8.475 15.622 8.45 15.734C8.388 15.959 8.27 16.459 8.245 16.571C8.207 16.721 8.12 16.758 7.97 16.684C6.782 16.122 6.04 14.247 6.04 13.053C6.04 9.753 8.438 6.747 12.229 6.747C15.254 6.747 17.597 8.891 17.597 12.748C17.597 16.797 15.479 20.05 12.68 20.05C11.653 20.05 10.693 19.524 10.36 18.873C10.36 18.873 9.84 20.794 9.715 21.287C9.465 22.312 8.768 23.662 8.364 24.387C9.559 24.787 10.754 25 12 25C18.627 25 24 19.627 24 13C24 6.373 18.627 1 12 1V0Z"
          fill="#E60023"
        />
      </g>
      <defs>
        <clipPath id="clip0_pinterest">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
