import Link from "next/link";
import React from "react";

type Props = {
  title: string;
  description: React.ReactNode;
  icon: React.ReactNode;
  btnTxt?: string;
  className?: string;
};

export default function OverviewDetails({
  title,
  description,
  icon,
  btnTxt,
  className,
}: Props) {
  return (
    <div
      className={`bg-light-blue p-5 lg:p-12 rounded-2xl overflow-hidden ${className}`}
    >
      <div className="flex-shrink-0">{icon}</div>
      <h4 className="mt-4 mb-3 text-secondary font-black text-lg lg:text-xl leading-tight">
        {title}
      </h4>
      <div className="mb-5 font-medium text-secondary whitespace-pre-line text-sm lg:text-base leading-relaxed">
        {description}
      </div>
      <div className="w-full">
        <Link
          href="/pricing"
          className="btn btn--primary py-3 !w-full lg:!w-auto max-w-full block text-center"
        >
          {btnTxt}
        </Link>
      </div>
    </div>
  );
}
