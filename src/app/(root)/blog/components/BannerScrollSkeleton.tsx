"use client";

const BannerScrollSkeleton: React.FC = () => {
  return (
    <div>
      <style jsx>{`
        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
        .shimmer {
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: shimmer 2s infinite linear;
        }
        .shimmer-white {
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0.1) 25%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0.1) 75%
          );
          background-size: 200% 100%;
          animation: shimmer 2s infinite linear;
        }
      `}</style>
      {/* Banner skeleton */}
      <div className="w-full decoration-0 relative overflow-hidden rounded-2xl block mb-3 sm:mb-4 lg:mb-6">
        {/* Image skeleton */}
        <div className="w-full h-[220px] md:h-[400px] max-h-[550px] shimmer rounded-2xl"></div>

        {/* Overlay content skeleton */}
        <div className="p-3 sm:p-4 md:p-6 overflow-hidden rounded-2xl flex flex-col max-h-[400px] justify-between absolute top-0 left-0 w-full h-full bg-gradient-to-b from-[#00000000] to-[#00000080]">
          {/* Top section with date and author */}
          <div className="flex gap-1 sm:gap-2 items-start justify-between">
            {/* Date skeleton */}
            <div className="bg-white/80 rounded-lg px-2 py-1 w-16 h-8"></div>

            {/* Author info skeleton */}
            <div className="bg-white flex flex-col gap-1 rounded-lg px-2 md:px-4 py-2">
              <div className="h-3 shimmer rounded w-20"></div>
              <div className="h-2 shimmer rounded w-16"></div>
            </div>
          </div>

          {/* Bottom section with title and description */}
          <div className="text-white">
            {/* Title skeleton */}
            <div className="mb-1 sm:mb-2 md:mb-3 lg:mb-4 space-y-2">
              <div className="h-6 md:h-7 lg:h-8 shimmer-white rounded w-full"></div>
              <div className="h-6 md:h-7 lg:h-8 shimmer-white rounded w-3/4"></div>
            </div>

            {/* Description skeleton */}
            <div className="space-y-1 sm:space-y-2">
              <div className="h-3 sm:h-4 md:h-5 shimmer-white rounded w-full"></div>
              <div className="h-3 sm:h-4 md:h-5 shimmer-white rounded w-5/6"></div>
              <div className="h-3 sm:h-4 md:h-5 shimmer-white rounded w-4/5 sm:block hidden"></div>
              <div className="h-3 sm:h-4 md:h-5 shimmer-white rounded w-3/4 md:block hidden"></div>
              <div className="h-3 sm:h-4 md:h-5 shimmer-white rounded w-2/3 md:block hidden"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation buttons skeleton */}
      <div className="flex items-center gap-2 sm:gap-3 md:gap-4 mt-3 sm:mt-4 lg:mt-6 mb-3 sm:mb-4 lg:mb-6 pb-3 sm:pb-4 lg:pb-6 border-b border-b-light-gray">
        <div className="w-10 h-8 shimmer rounded border"></div>
        <div className="w-10 h-8 shimmer rounded border"></div>
      </div>
    </div>
  );
};

export default BannerScrollSkeleton;
