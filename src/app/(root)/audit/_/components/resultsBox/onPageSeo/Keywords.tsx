import { CheckIcon, CrossIcon } from "@/ui/icons/general";
import Table from "@/ui/Table";

import ProgressPercent from "../../ProgressPercent";

import { KeywordType, PharseType } from "./OnPageSeo";

// const tableData = [
//   {
//     keyword: "seoptimer",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: false,
//     frequency: "39",
//     percentage: 100,
//   },
//   {
//     keyword: "seo",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "16",
//     percentage: 70,
//   },
//   {
//     keyword: "audit",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "18",
//     percentage: 100,
//   },
//   {
//     keyword: "reports",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "40",
//     percentage: 100,
//   },
//   {
//     keyword: "website",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "8",
//     percentage: 40,
//   },
//   {
//     keyword: "tool",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: true,
//     frequency: "24",
//     percentage: 80,
//   },
//   {
//     keyword: "site",
//     title: true,
//     metaDescriptionTag: false,
//     headingsTags: true,
//     frequency: "12",
//     percentage: 40,
//   },
//   {
//     keyword: "rank",
//     title: true,
//     metaDescriptionTag: true,
//     headingsTags: false,
//     frequency: "10",
//     percentage: 50,
//   },
// ];

export default function Keywords({
  data,
  pharses,
}: {
  data: KeywordType[];
  pharses: PharseType[];
}) {
  // Calculate percentages for keywords if not provided
  const processedData = data.map((item) => {
    if (item.percentage !== undefined) return item;

    // Find the max frequency to calculate percentage
    const maxFrequency = Math.max(...data.map((k) => k.frequency));
    const percentage =
      maxFrequency > 0 ? Math.round((item.frequency / maxFrequency) * 100) : 0;

    return {
      ...item,
      percentage,
    };
  });

  // Calculate percentages for phrases if not provided
  const processedPhrases = pharses.map((item) => {
    if (item.percentage !== undefined) return item;

    // Find the max frequency to calculate percentage
    const maxFrequency = Math.max(...pharses.map((p) => p.frequency));
    const percentage =
      maxFrequency > 0 ? Math.round((item.frequency / maxFrequency) * 100) : 0;

    return {
      ...item,
      percentage,
    };
  });

  return (
    <div className="!text-xs lg:text-sm mt-6 pb-2">
      <h4 className="font-semibold text-secondary pb-2 text-sm lg:text-base ">
        Individual Keywords
      </h4>
      {processedData.length > 0 ? (
        <Table>
          <Table.Header>
            <th className="pb-2 pr-2 lg:pr-[58px] !text-xs lg:text-sm">
              Keyword
            </th>
            <th className="pb-2 !text-xs lg:text-sm">Title</th>
            <th className="text-center pb-2 whitespace-nowrap px-2 lg:px-[58px] !text-xs lg:text-sm">
              Meta Description
              <br /> Tag
            </th>
            <th className="text-center pb-2 !text-xs lg:text-sm">
              Headings
              <br /> Tags
            </th>
            <th className="min-w-0 lg:min-w-[158px] text-center pb-2 !text-xs lg:text-sm">
              Page <br />
              Frequency
            </th>
            <th className="lg:w-[180px]"></th>
          </Table.Header>
          <Table.Body>
            {processedData.map((item) => (
              <Table.Row key={item.keyword} className="!h-10 sm:!h-[51px]">
                <td className="px-2 lg:px-3 !text-xs lg:text-sm">
                  {item.keyword}
                </td>
                <td className="!text-xs lg:text-sm">
                  {item.in_title ? (
                    <CheckIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-green" />
                  ) : (
                    <CrossIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-red" />
                  )}
                </td>
                <td className="!text-xs lg:text-sm">
                  <div className="w-full flex justify-center">
                    {item.in_meta_description ? (
                      <CheckIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-green" />
                    ) : (
                      <CrossIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-red" />
                    )}
                  </div>
                </td>
                <td className="!text-xs lg:text-sm">
                  <div className="w-full flex justify-center">
                    {item.in_headings ? (
                      <CheckIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-green" />
                    ) : (
                      <CrossIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-red" />
                    )}
                  </div>
                </td>
                <td className="text-center !text-xs lg:text-sm">
                  {item.frequency}
                </td>
                <td className="pl-2 lg:pl-4 !text-xs lg:text-sm">
                  <div className="flex justify-end">
                    <div className="w-[120px] sm:w-[150px] lg:w-[180px]">
                      <ProgressPercent percentage={item.percentage || 0} />
                    </div>
                  </div>
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="p-4 bg-light-gray-6 rounded-lg text-center text-secondary/60">
          No keyword data available
        </div>
      )}

      <h4 className="font-semibold text-secondary pb-2  !text-sm lg:text-base mt-8">
        Phrases
      </h4>
      {processedPhrases.length > 0 ? (
        <Table>
          <Table.Header>
            <th className="pb-2 pr-2 lg:pr-[58px] !text-xs lg:text-sm">
              Phrase
            </th>
            <th className="pb-2 !text-xs lg:text-sm">Title</th>
            <th className="text-center pb-2 whitespace-nowrap px-2 lg:px-[58px] !text-xs lg:text-sm">
              Meta Description
              <br /> Tag
            </th>
            <th className="text-center pb-2 !text-xs lg:text-sm">
              Headings
              <br /> Tags
            </th>
            <th className="min-w-0 lg:min-w-[158px] text-center pb-2 !text-xs lg:text-sm">
              Page <br />
              Frequency
            </th>
            <th className="lg:w-[180px]"></th>
          </Table.Header>
          <Table.Body>
            {processedPhrases.map((item) => (
              <Table.Row key={item.phrase} className="!h-10 sm:!h-[51px]">
                <td className="px-2 lg:px-3 !text-xs lg:text-sm">
                  {item.phrase}
                </td>
                <td className="!text-xs lg:text-sm">
                  {item.in_title ? (
                    <CheckIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-green" />
                  ) : (
                    <CrossIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-red" />
                  )}
                </td>
                <td className="!text-xs lg:text-sm">
                  <div className="w-full flex justify-center">
                    {item.in_meta_description ? (
                      <CheckIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-green" />
                    ) : (
                      <CrossIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-red" />
                    )}
                  </div>
                </td>
                <td className="!text-xs lg:text-sm">
                  <div className="w-full flex justify-center">
                    {item.in_headings ? (
                      <CheckIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-green" />
                    ) : (
                      <CrossIcon className="w-5 h-5 sm:w-6 sm:h-6 text-primary-red" />
                    )}
                  </div>
                </td>
                <td className="text-center !text-xs lg:text-sm">
                  {item.frequency}
                </td>
                <td className="pl-2 lg:pl-4 !text-xs lg:text-sm">
                  <div className="flex justify-end">
                    <div className="w-[120px] sm:w-[150px] lg:w-[180px]">
                      <ProgressPercent percentage={item.percentage || 0} />
                    </div>
                  </div>
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="p-4 bg-light-gray-6 rounded-lg text-center text-secondary/60">
          No phrase data available
        </div>
      )}
    </div>
  );
}
