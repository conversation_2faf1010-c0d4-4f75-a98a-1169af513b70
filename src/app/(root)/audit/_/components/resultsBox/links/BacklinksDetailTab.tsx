import { motion } from "framer-motion";
import Link from "next/link";
import {
  CheckIcon,
  CrossIcon,
  ChainIcon,
  SparklesIcon,
} from "@/ui/icons/general";
import StateCard from "../../StateCard";
import OverallSection from "../OverallSection";
import ShowMoreSection from "../usability/ShowMoreSection";
import Table from "@/ui/Table";
import { LinksAnalysisData } from "./types";
import {
  formatMetricValue,
  formatMetricValueWithPrefix,
  getMetricInfo,
  getActiveData,
} from "./utils";

type BacklinksDetailTabProps = {
  data: LinksAnalysisData;
  itemVariants: any;
};

export default function BacklinksDetailTab({
  data,
  itemVariants,
}: BacklinksDetailTabProps) {
  const backlinksDetail = data.backlinks_detail;
  const activeData = getActiveData("backlinks_detail", data);

  if (!backlinksDetail) {
    return (
      <div className="bg-white rounded-lg border border-light-gray p-6">
        <div className="text-center py-8">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-red-700 mb-2">
            Backlinks Detail Data Unavailable
          </h3>
          <p className="text-red-600">
            Something went wrong while getting backlinks detail data. Please try
            again later.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <motion.div variants={itemVariants}>
        <OverallSection
          title={activeData.title}
          description={activeData.description}
        />
      </motion.div>

      <motion.div
        variants={itemVariants}
        className="w-full grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4"
      >
        <StateCard
          icon={<ChainIcon className="w-6 h-6" />}
          value={formatMetricValue(backlinksDetail, "total", 0, "count")}
          label="Total Backlinks"
        />
        <StateCard
          icon={<SparklesIcon className="w-6 h-6" />}
          value={formatMetricValue(
            backlinksDetail,
            "unique_domain_count",
            0,
            "count"
          )}
          label="Unique Domains"
        />
        <StateCard
          icon={<CheckIcon className="w-6 h-6" />}
          value={formatMetricValue(
            backlinksDetail,
            "dofollow_links",
            0,
            "count"
          )}
          label="Dofollow Links"
        />
        <StateCard
          icon={<CrossIcon className="w-6 h-6" />}
          value={formatMetricValue(
            backlinksDetail,
            "nofollow_links",
            0,
            "count"
          )}
          label="Nofollow Links"
        />
      </motion.div>

      {/* Detailed sections */}
      <div className="space-y-6 mt-6">
        {/* Total Backlinks Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Total Backlinks"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    backlinksDetail,
                    "total",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(backlinksDetail, "total")?.description ||
                  "The total number of external pages linking to your entire root domain."}
              </div>
            }
            importance={getMetricInfo(backlinksDetail, "total")?.importance}
            recommendation={
              getMetricInfo(backlinksDetail, "total")?.recommendation ||
              undefined
            }
            icon={<ChainIcon className="w-12 h-12 text-blue-600" />}
          />
        </div>

        {/* Dofollow Links Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Dofollow Links"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    backlinksDetail,
                    "dofollow_links",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(backlinksDetail, "dofollow_links")
                  ?.description ||
                  "Count of 'dofollow' (or standard) links in your backlink profile. These links pass link equity."}
              </div>
            }
            importance={
              getMetricInfo(backlinksDetail, "dofollow_links")?.importance
            }
            recommendation={
              getMetricInfo(backlinksDetail, "dofollow_links")
                ?.recommendation || undefined
            }
            icon={<CheckIcon className="w-12 h-12 text-green-600" />}
          />
        </div>

        {/* Nofollow Links Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Nofollow Links"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    backlinksDetail,
                    "nofollow_links",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(backlinksDetail, "nofollow_links")
                  ?.description ||
                  "Count of 'nofollow' links in your backlink profile. Nofollow links typically don't pass link equity."}
              </div>
            }
            importance={
              getMetricInfo(backlinksDetail, "nofollow_links")?.importance
            }
            recommendation={
              getMetricInfo(backlinksDetail, "nofollow_links")
                ?.recommendation || undefined
            }
            icon={<CrossIcon className="w-12 h-12 text-red-600" />}
          />
        </div>

        {/* Unique Domains Section */}
        <div className="bg-white ">
          <ShowMoreSection
            title="Unique Referring Domains"
            description={
              <div className="text-xs sm:text-sm text-gray-700 leading-relaxed">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                  {formatMetricValueWithPrefix(
                    backlinksDetail,
                    "unique_domain_count",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(backlinksDetail, "unique_domain_count")
                  ?.description ||
                  "The number of unique root domains that link to your website."}
              </div>
            }
            importance={
              getMetricInfo(backlinksDetail, "unique_domain_count")?.importance
            }
            recommendation={
              getMetricInfo(backlinksDetail, "unique_domain_count")
                ?.recommendation || undefined
            }
            icon={<SparklesIcon className="w-12 h-12 text-purple-600" />}
          >
            {/* Premium user check */}
            {backlinksDetail?.is_premium_user === false && (
              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <Link
                  href="/pricing"
                  className="text-sm text-yellow-800 hover:underline"
                >
                  {backlinksDetail?.upgrade_message ||
                    "Upgrade to Pro plan for more detailed backlink information and larger samples."}
                </Link>
              </div>
            )}

            {/* Top Backlinks Sample Table */}
            {backlinksDetail?.top_backlinks_sample &&
              backlinksDetail.top_backlinks_sample.length > 0 && (
                <div className="mt-4">
                  <h5 className="font-medium text-gray-900 mb-3">
                    Top Backlinks Sample
                  </h5>
                  <Table>
                    <Table.Header>
                      <th className="pb-4 whitespace-nowrap">Domain</th>
                      <th className="text-center pb-4">Anchor Text</th>
                      <th className="text-center pb-4">Type</th>
                      <th className="text-center pb-4">DA</th>
                      <th className="text-center pb-4">PA</th>
                      <th className="text-center pb-4">Spam Score</th>
                    </Table.Header>
                    <Table.Body>
                      {backlinksDetail.top_backlinks_sample.map(
                        (item, index) => (
                          <Table.Row key={index}>
                            <td className="py-4 pr-2 sm:pr-4 break-words">
                              <a
                                href={`https://${item.domain_from}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-primary hover:underline text-xs sm:text-sm"
                              >
                                {item.domain_from}
                              </a>
                            </td>
                            <td className="py-4 break-words text-xs sm:text-sm text-center max-w-[200px]">
                              {item.anchor || "N/A"}
                            </td>
                            <td className="py-4 text-center">
                              <span
                                className={`px-2 py-1 rounded text-xs ${
                                  item.dofollow
                                    ? "bg-green-100 text-green-800"
                                    : "bg-gray-100 text-gray-800"
                                }`}
                              >
                                {item.dofollow ? "Dofollow" : "Nofollow"}
                              </span>
                            </td>
                            <td className="py-4 text-center text-xs sm:text-sm">
                              {item.domain_authority}
                            </td>
                            <td className="py-4 text-center text-xs sm:text-sm">
                              {item.page_authority}
                            </td>
                            <td className="py-4 text-center text-xs sm:text-sm">
                              {item.spam_score}
                            </td>
                          </Table.Row>
                        )
                      )}
                    </Table.Body>
                  </Table>
                </div>
              )}
          </ShowMoreSection>
        </div>
      </div>
    </>
  );
}
