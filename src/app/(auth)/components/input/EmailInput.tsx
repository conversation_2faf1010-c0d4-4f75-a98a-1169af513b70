import { ComponentProps, memo, useCallback } from "react";
type EmailInputProps = {
  onChange: (value: string) => void;
} & Omit<ComponentProps<"input">, "onChange">;

const EmailInputComponent: React.FC<EmailInputProps> = ({
  onChange,
  className,
  ...rest
}) => {
  // Optimized onChange handler to prevent unnecessary re-renders
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    },
    [onChange]
  );

  return (
    <div className="flex flex-col gap-2">
      <label className="text-secondary" htmlFor={`email`}>
        <span className="text-red-500">*</span> E-Mail
      </label>
      <input
        type={`email`}
        name={`email`}
        required
        autoComplete="username email"
        {...rest}
        onChange={handleChange}
        className={`textField__input focus:border-primary ${className}`}
        // Add performance attributes
        data-lpignore="true" // Disable LastPass autofill
      />
    </div>
  );
};

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(EmailInputComponent);
