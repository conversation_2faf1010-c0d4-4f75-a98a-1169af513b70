// Constants
const AUDIT_COUNT_KEY = 'seo_audit_count';
// Unlimited free audits
const MAX_FREE_AUDITS = Number.MAX_SAFE_INTEGER;

/**
 * Check if we're in a browser environment
 */
const isBrowser = typeof window !== 'undefined';

/**
 * Get the current audit count for the session
 * @returns The number of audits performed in the current session
 */
const getAuditCount = (): number => {
  if (!isBrowser) return 0;

  try {
    const count = sessionStorage.getItem(AUDIT_COUNT_KEY);
    return count ? parseInt(count, 10) : 0;
  } catch (error) {
    // Error getting audit count
    return 0;
  }
};

/**
 * Increment the audit count by one
 * @returns The new audit count
 */
const incrementAuditCount = (): number => {
  if (!isBrowser) return 0;

  try {
    const currentCount = getAuditCount();
    const newCount = currentCount + 1;
    sessionStorage.setItem(AUDIT_COUNT_KEY, newCount.toString());
    return newCount;
  } catch (error) {
    // Error incrementing audit count
    return getAuditCount();
  }
};

/**
 * Check if the user has reached the maximum number of free audits
 * @returns Always returns false as the audit limit has been removed
 */
const hasReachedAuditLimit = (): boolean => {
  return false;
};

/**
 * Reset the audit count for the current session
 */
const resetAuditCount = (): void => {
  if (!isBrowser) return;

  try {
    sessionStorage.setItem(AUDIT_COUNT_KEY, '0');
  } catch (error) {
    // Error resetting audit count
  }
};

/**
 * Get the maximum number of free audits allowed
 * @returns The maximum number of free audits
 */
const getMaxFreeAudits = (): number => {
  return MAX_FREE_AUDITS;
};

const sessionService = {
  getAuditCount,
  incrementAuditCount,
  hasReachedAuditLimit,
  resetAuditCount,
  getMaxFreeAudits,
};

export default sessionService;
