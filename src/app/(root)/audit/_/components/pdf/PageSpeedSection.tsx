"use client";
import React from "react";
import {
  PageSpeedAnalysis,
  PageSpeedMobileAnalysis,
} from "@/types/seoAnalyzerTypes";
import {
  SectionHeader,
  SectionScoreBox,
  DataRow,
  ScoreChart,
  RecommendationCard,
} from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";

export interface PageSpeedSectionProps {
  pagespeedData?: PageSpeedAnalysis;
  pagespeedMobileData?: PageSpeedMobileAnalysis;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const PageSpeedSection: React.FC<PageSpeedSectionProps> = ({
  pagespeedData,
  pagespeedMobileData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  // Helper function to get Core Web Vitals score color
  const getVitalColor = (score: number) => {
    if (score >= 90) return "#4CAF50"; // Green
    if (score >= 50) return "#FF9800"; // Orange
    return "#F44336"; // Red
  };

  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "SEO ANALYSER"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoPageSpeed")}
        onLogoError={() => onImageError?.("sectionLogoPageSpeed")}
        sectionId="pagespeed-details"
      />

      <SectionHeader
        title="PageSpeed Audit"
        brandName={brand_name}
        brandWebsite={brand_website}
      />

      <div className="space-y-8">
        {/* Desktop PageSpeed */}
        {pagespeedData && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800 border-b pb-2">
              Desktop Performance
            </h3>

            {/* Core Web Vitals Desktop */}
            {pagespeedData.core_web_vitals_desktop && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                  <h4 className="font-bold text-gray-800 text-lg">
                    Core Web Vitals - Desktop
                  </h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  {/* LCP */}
                  {pagespeedData.core_web_vitals_desktop[
                    "Largest Contentful Paint (LCP)"
                  ] !== undefined && (
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="flex justify-center mb-2">
                        <ScoreChart
                          score={Math.round(
                            ((4 -
                              Math.min(
                                pagespeedData.core_web_vitals_desktop[
                                  "Largest Contentful Paint (LCP)"
                                ],
                                4
                              )) /
                              4) *
                              100
                          )}
                          size="w-16 h-16"
                          compact={true}
                        />
                      </div>
                      <div className="text-sm font-medium">LCP</div>
                      <div className="text-xs text-gray-600">
                        {pagespeedData.core_web_vitals_desktop[
                          "Largest Contentful Paint (LCP)"
                        ]?.toFixed(2)}
                        s
                      </div>
                    </div>
                  )}

                  {/* FID */}
                  {pagespeedData.core_web_vitals_desktop[
                    "First Input Delay (FID)"
                  ] !== undefined && (
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="flex justify-center mb-2">
                        <ScoreChart
                          score={Math.round(
                            ((100 -
                              Math.min(
                                pagespeedData.core_web_vitals_desktop[
                                  "First Input Delay (FID)"
                                ],
                                100
                              )) /
                              100) *
                              100
                          )}
                          size="w-16 h-16"
                          compact={true}
                        />
                      </div>
                      <div className="text-sm font-medium">FID</div>
                      <div className="text-xs text-gray-600">
                        {pagespeedData.core_web_vitals_desktop[
                          "First Input Delay (FID)"
                        ]?.toFixed(0)}
                        ms
                      </div>
                    </div>
                  )}

                  {/* CLS */}
                  {pagespeedData.core_web_vitals_desktop[
                    "Cumulative Layout Shift (CLS)"
                  ] !== undefined && (
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="flex justify-center mb-2">
                        <ScoreChart
                          score={Math.round(
                            ((0.25 -
                              Math.min(
                                pagespeedData.core_web_vitals_desktop[
                                  "Cumulative Layout Shift (CLS)"
                                ],
                                0.25
                              )) /
                              0.25) *
                              100
                          )}
                          size="w-16 h-16"
                          compact={true}
                        />
                      </div>
                      <div className="text-sm font-medium">CLS</div>
                      <div className="text-xs text-gray-600">
                        {pagespeedData.core_web_vitals_desktop[
                          "Cumulative Layout Shift (CLS)"
                        ]?.toFixed(3)}
                      </div>
                    </div>
                  )}
                </div>

                {/* Desktop Core Web Vitals Recommendation */}
                {pagespeedData.core_web_vitals_desktop.recommendation && (
                  <div className="mt-4">
                    <RecommendationCard
                      recommendation={
                        pagespeedData.core_web_vitals_desktop.recommendation
                      }
                    />
                  </div>
                )}
              </div>
            )}

            {/* Performance Metrics Desktop - moved to next page */}
            {pagespeedData.performance_desktop && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200 ">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                  <h4 className="font-bold text-gray-800 text-lg">
                    Performance Metrics - Desktop
                  </h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                    <DataRow
                      label="First Contentful Paint (FCP)"
                      value={`${
                        pagespeedData.performance_desktop[
                          "First Contentful Paint (FCP)"
                        ] || "N/A"
                      }s`}
                    />
                    <DataRow
                      label="Speed Index (SI)"
                      value={`${
                        pagespeedData.performance_desktop["Speed Index (SI)"] ||
                        "N/A"
                      }s`}
                    />
                    <DataRow
                      label="Time to Interactive (TTI)"
                      value={`${
                        pagespeedData.performance_desktop[
                          "Time to Interactive (TTI)"
                        ] || "N/A"
                      }s`}
                    />
                  </div>
                  <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                    <DataRow
                      label="Total Blocking Time (TBT)"
                      value={`${
                        pagespeedData.performance_desktop[
                          "Total Blocking Time (TBT)"
                        ] || "N/A"
                      }ms`}
                    />
                  </div>
                </div>

                {/* Desktop Performance Metrics Recommendation */}
                {pagespeedData.performance_desktop.recommendation && (
                  <div className="mt-4">
                    <RecommendationCard
                      recommendation={
                        pagespeedData.performance_desktop.recommendation
                      }
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Mobile PageSpeed */}
        {pagespeedMobileData && (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-800 border-b pb-2">
              Mobile Performance
            </h3>

            {/* Core Web Vitals Mobile */}
            {pagespeedMobileData.core_web_vitals_mobile && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                  <h4 className="font-bold text-gray-800 text-lg">
                    Core Web Vitals - Mobile
                  </h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  {/* LCP */}
                  {pagespeedMobileData.core_web_vitals_mobile[
                    "Largest Contentful Paint (LCP)"
                  ] !== undefined && (
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="flex justify-center mb-2">
                        <ScoreChart
                          score={Math.round(
                            ((4 -
                              Math.min(
                                pagespeedMobileData.core_web_vitals_mobile[
                                  "Largest Contentful Paint (LCP)"
                                ],
                                4
                              )) /
                              4) *
                              100
                          )}
                          size="w-16 h-16"
                          compact={true}
                        />
                      </div>
                      <div className="text-sm font-medium">LCP</div>
                      <div className="text-xs text-gray-600">
                        {pagespeedMobileData.core_web_vitals_mobile[
                          "Largest Contentful Paint (LCP)"
                        ]?.toFixed(2)}
                        s
                      </div>
                    </div>
                  )}

                  {/* FID */}
                  {pagespeedMobileData.core_web_vitals_mobile[
                    "First Input Delay (FID)"
                  ] !== undefined && (
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="flex justify-center mb-2">
                        <ScoreChart
                          score={Math.round(
                            ((100 -
                              Math.min(
                                pagespeedMobileData.core_web_vitals_mobile[
                                  "First Input Delay (FID)"
                                ],
                                100
                              )) /
                              100) *
                              100
                          )}
                          size="w-16 h-16"
                          compact={true}
                        />
                      </div>
                      <div className="text-sm font-medium">FID</div>
                      <div className="text-xs text-gray-600">
                        {pagespeedMobileData.core_web_vitals_mobile[
                          "First Input Delay (FID)"
                        ]?.toFixed(0)}
                        ms
                      </div>
                    </div>
                  )}

                  {/* CLS */}
                  {pagespeedMobileData.core_web_vitals_mobile[
                    "Cumulative Layout Shift (CLS)"
                  ] !== undefined && (
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="flex justify-center mb-2">
                        <ScoreChart
                          score={Math.round(
                            ((0.25 -
                              Math.min(
                                pagespeedMobileData.core_web_vitals_mobile[
                                  "Cumulative Layout Shift (CLS)"
                                ],
                                0.25
                              )) /
                              0.25) *
                              100
                          )}
                          size="w-16 h-16"
                          compact={true}
                        />
                      </div>
                      <div className="text-sm font-medium">CLS</div>
                      <div className="text-xs text-gray-600">
                        {pagespeedMobileData.core_web_vitals_mobile[
                          "Cumulative Layout Shift (CLS)"
                        ]?.toFixed(3)}
                      </div>
                    </div>
                  )}
                </div>

                {/* Mobile Core Web Vitals Recommendation */}
                {pagespeedMobileData.core_web_vitals_mobile.recommendation && (
                  <div className="mt-4">
                    <RecommendationCard
                      recommendation={
                        pagespeedMobileData.core_web_vitals_mobile
                          .recommendation
                      }
                    />
                  </div>
                )}
              </div>
            )}

            {/* Performance Metrics Mobile */}
            {pagespeedMobileData.performance_mobile && (
              <div className="p-6 border border-gray-200/80 rounded-xl bg-white transition-all duration-200">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                  <h4 className="font-bold text-gray-800 text-lg">
                    Performance Metrics - Mobile
                  </h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                    <DataRow
                      label="First Contentful Paint (FCP)"
                      value={`${
                        pagespeedMobileData.performance_mobile[
                          "First Contentful Paint (FCP)"
                        ] || "N/A"
                      }s`}
                    />
                    <DataRow
                      label="Speed Index (SI)"
                      value={`${
                        pagespeedMobileData.performance_mobile[
                          "Speed Index (SI)"
                        ] || "N/A"
                      }s`}
                    />
                    <DataRow
                      label="Time to Interactive (TTI)"
                      value={`${
                        pagespeedMobileData.performance_mobile[
                          "Time to Interactive (TTI)"
                        ] || "N/A"
                      }s`}
                    />
                  </div>
                  <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                    <DataRow
                      label="Total Blocking Time (TBT)"
                      value={`${
                        pagespeedMobileData.performance_mobile[
                          "Total Blocking Time (TBT)"
                        ] || "N/A"
                      }ms`}
                    />
                  </div>
                </div>

                {/* Mobile Performance Metrics Recommendation */}
                {pagespeedMobileData.performance_mobile.recommendation && (
                  <div className="mt-4">
                    <RecommendationCard
                      recommendation={
                        pagespeedMobileData.performance_mobile.recommendation
                      }
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Comprehensive PageSpeed Recommendations */}
        <div className="mt-6">
          <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
            All PageSpeed Recommendations
          </h3>
          <div className="grid grid-cols-1 gap-4 recommendations-grid">
            {/* Desktop Performance Recommendations */}
            {pagespeedData?.performance?.recommendation && (
              <RecommendationCard
                recommendation={pagespeedData.performance.recommendation}
              />
            )}

            {/* Mobile Performance Recommendations */}
            {pagespeedMobileData?.performance_mobile?.recommendation && (
              <RecommendationCard
                recommendation={
                  pagespeedMobileData.performance_mobile.recommendation
                }
              />
            )}

            {/* If no recommendations found */}
            {!pagespeedData?.performance?.recommendation &&
              !pagespeedMobileData?.performance_mobile?.recommendation && (
                <RecommendationCard
                  recommendation={{
                    text: "Your website's page speed appears to be well optimized. Continue monitoring performance metrics and optimizing images and resources.",
                    priority: "Low",
                  }}
                />
              )}
          </div>
        </div>
      </div>
    </div>
  );
};
