import Pie<PERSON><PERSON>, { PieChartDataItem } from "@/ui/charts/PieChart";
import { PageSizeAnalysis } from "@/types/seoAnalyzerTypes";

type CompressionPieChartProps = {
  pageSizeData: PageSizeAnalysis;
  className?: string;
};

const CompressionPieChart = ({ pageSizeData, className = "" }: CompressionPieChartProps) => {
  // Extract data from the page size breakdown
  const { breakdown_estimated_mb } = pageSizeData;

  // Calculate other resources (if any difference between total and sum of known types)
  const totalSize = pageSizeData.total_estimated_size_mb;
  const knownSize =
    breakdown_estimated_mb.html_mb +
    breakdown_estimated_mb.est_css_mb +
    breakdown_estimated_mb.est_js_mb +
    breakdown_estimated_mb.est_images_mb;

  const otherSize = Math.max(0, totalSize - knownSize);

  // Create data for the pie chart with colors matching the provided image
  const chartData: PieChartDataItem[] = [
    {
      name: "HTML",
      value: Number(breakdown_estimated_mb.html_mb.toFixed(2)),
      color: "#38BDF8" // Blue
    },
    {
      name: "CSS",
      value: Number(breakdown_estimated_mb.est_css_mb.toFixed(2)),
      color: "#FF9BC2" // Pink
    },
    {
      name: "Images",
      value: Number(breakdown_estimated_mb.est_images_mb.toFixed(2)),
      color: "#2DD4BF" // Teal/Green
    },
    {
      name: "JS",
      value: Number(breakdown_estimated_mb.est_js_mb.toFixed(2)),
      color: "#9D7BEA" // Purlpe
    }
  ];

  // Add other resources if they exist
  if (otherSize > 0) {
    chartData.push({
      name: "Others",
      value: Number(otherSize.toFixed(2)),
      color: "#FBBF24" // Yellow/Orange
    });
  }

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <PieChart
        data={chartData}
        title="Page Size"
        size="md"
        showLegend={true}
        legendPosition="bottom"
        donut={true}
        donutSize="75%"
        className="mb-4"
      />
      <div className="text-sm text-secondary/70 mt-2">
        Total Size: {totalSize.toFixed(2)} MB
      </div>
    </div>
  );
};

export default CompressionPieChart;
