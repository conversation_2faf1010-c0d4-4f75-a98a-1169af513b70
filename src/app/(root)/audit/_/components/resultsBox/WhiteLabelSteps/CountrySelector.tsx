"use client";
import { useState, useEffect, useRef, useCallback } from "react";
import Flag from "react-world-flags";

// Define the country data interface
interface CountryData {
  code: string;
  name: string;
  dialCode: string;
}

// Props for the CountrySelector component
interface CountrySelectorProps {
  selectedCountry: CountryData;
  onSelect: (country: CountryData) => void;
  className?: string;
}

// List of countries with dial codes
const countries: CountryData[] = [
  { code: "US", name: "United States", dialCode: "+1" },
  { code: "GB", name: "United Kingdom", dialCode: "+44" },
  { code: "CA", name: "Canada", dialCode: "+1" },
  { code: "AU", name: "Australia", dialCode: "+61" },
  { code: "DE", name: "Germany", dialCode: "+49" },
  { code: "FR", name: "France", dialCode: "+33" },
  { code: "IT", name: "Italy", dialCode: "+39" },
  { code: "ES", name: "Spain", dialCode: "+34" },
  { code: "JP", name: "Japan", dialCode: "+81" },
  { code: "CN", name: "China", dialCode: "+86" },
  { code: "IN", name: "India", dialCode: "+91" },
  { code: "BR", name: "Brazil", dialCode: "+55" },
  { code: "RU", name: "Russia", dialCode: "+7" },
  { code: "KR", name: "South Korea", dialCode: "+82" },
  { code: "IR", name: "Iran", dialCode: "+98" },
  { code: "TR", name: "Turkey", dialCode: "+90" },
  { code: "NL", name: "Netherlands", dialCode: "+31" },
  { code: "SE", name: "Sweden", dialCode: "+46" },
  { code: "NO", name: "Norway", dialCode: "+47" },
  { code: "DK", name: "Denmark", dialCode: "+45" },
  { code: "FI", name: "Finland", dialCode: "+358" },
  { code: "PL", name: "Poland", dialCode: "+48" },
  { code: "PT", name: "Portugal", dialCode: "+351" },
  { code: "GR", name: "Greece", dialCode: "+30" },
  { code: "CH", name: "Switzerland", dialCode: "+41" },
  { code: "AT", name: "Austria", dialCode: "+43" },
  { code: "BE", name: "Belgium", dialCode: "+32" },
  { code: "IE", name: "Ireland", dialCode: "+353" },
  { code: "NZ", name: "New Zealand", dialCode: "+64" },
  { code: "SG", name: "Singapore", dialCode: "+65" },
  { code: "MY", name: "Malaysia", dialCode: "+60" },
  { code: "TH", name: "Thailand", dialCode: "+66" },
  { code: "ID", name: "Indonesia", dialCode: "+62" },
  { code: "PH", name: "Philippines", dialCode: "+63" },
  { code: "VN", name: "Vietnam", dialCode: "+84" },
  { code: "ZA", name: "South Africa", dialCode: "+27" },
  { code: "MX", name: "Mexico", dialCode: "+52" },
  { code: "AR", name: "Argentina", dialCode: "+54" },
  { code: "CL", name: "Chile", dialCode: "+56" },
  { code: "CO", name: "Colombia", dialCode: "+57" },
  { code: "PE", name: "Peru", dialCode: "+51" },
  { code: "VE", name: "Venezuela", dialCode: "+58" },
  { code: "AE", name: "United Arab Emirates", dialCode: "+971" },
  { code: "SA", name: "Saudi Arabia", dialCode: "+966" },
  { code: "EG", name: "Egypt", dialCode: "+20" },
  { code: "IL", name: "Israel", dialCode: "+972" },
  { code: "HK", name: "Hong Kong", dialCode: "+852" },
  { code: "TW", name: "Taiwan", dialCode: "+886" },
  { code: "UA", name: "Ukraine", dialCode: "+380" },
  { code: "RO", name: "Romania", dialCode: "+40" },
];

export { countries };
export type { CountryData };

export default function CountrySelector({
  selectedCountry,
  onSelect,
  className = "",
}: CountrySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCountries, setFilteredCountries] = useState(countries);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter countries based on search term
  useEffect(() => {
    if (searchTerm) {
      const filtered = countries.filter(
        (country) =>
          country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          country.dialCode.includes(searchTerm) ||
          country.code.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCountries(filtered);
    } else {
      setFilteredCountries(countries);
    }
  }, [searchTerm]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Handle country selection
  const handleSelectCountry = useCallback(
    (country: CountryData) => {
      onSelect(country);
      setIsOpen(false);
      setSearchTerm("");
    },
    [onSelect]
  );

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Selected country display */}
      <div
        className="flex items-center gap-2 cursor-pointer p-2 sm:p-3 border border-gray-300 rounded-lg text-sm sm:text-base"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="w-5 h-3 sm:w-6 sm:h-4 overflow-hidden rounded-sm">
          <Flag code={selectedCountry.code} />
        </div>
        <span className="font-medium">{selectedCountry.dialCode}</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-3 w-3 sm:h-4 sm:w-4 transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 bottom-full mb-1 w-full sm:w-64 bg-white border border-gray-300 rounded-lg shadow-lg flex flex-col">
          {/* Search input - always visible */}
          <div className="p-2 border-b border-gray-200 sticky top-0 bg-white z-10">
            <input
              ref={searchInputRef}
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search country or code..."
              className="w-full p-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
            />
          </div>

          {/* Country list - scrollable */}
          <div className="max-h-40 overflow-y-auto">
            {filteredCountries.length > 0 ? (
              filteredCountries.map((country) => (
                <div
                  key={country.code}
                  className="flex items-center gap-2 sm:gap-3 p-2 hover:bg-gray-100 cursor-pointer text-sm"
                  onClick={() => handleSelectCountry(country)}
                >
                  <div className="w-5 h-3 sm:w-6 sm:h-4 overflow-hidden rounded-sm">
                    <Flag code={country.code} />
                  </div>
                  <span className="font-medium">{country.dialCode}</span>
                  <span className="text-xs sm:text-sm text-gray-600 truncate">
                    {country.name}
                  </span>
                </div>
              ))
            ) : (
              <div className="p-3 text-center text-gray-500 text-sm">
                No countries found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
