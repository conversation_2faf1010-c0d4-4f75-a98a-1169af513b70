"use client";

import React from "react";

interface CatItemProps {
  title: string;
  isActive: boolean;
  onClick: () => void;
}
const CatItem: React.FC<CatItemProps> = ({ title, isActive, onClick }) => {
  return (
    <div
      onClick={onClick}
      className={`rounded-lg px-4 py-2.5 transition-all text-secondary cursor-pointer ${
        isActive ? "bg-primary/10 !text-primary font-bold" : ""
      }`}
    >
      {title}
    </div>
  );
};

export default CatItem;
